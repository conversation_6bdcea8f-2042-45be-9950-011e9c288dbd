-- 测试1：检查基础数据是否存在
-- 用于验证传入的参数和相关表数据

-- 替换这里的参数值进行测试
-- @param p_objid: 你的机构objid
-- @param p_datetime: 测试时间（可选）
\set p_objid 'ZQW9LIB5301C4LN5JG0207'
\set p_datetime '2025-07-29 14:30:00'

-- 1. 检查机构关系表
SELECT
    '1. 机构关系检查' as test_name,
    orgcode,
    porgcode,
    used
FROM sys_org_relation
WHERE orgcode = :'p_objid' AND used = 1;

-- 2. 检查班次数据表中是否有该objid的记录
SELECT
    '2. 班次数据检查' as test_name,
    orgcode,
    objid,
    objname,
    dbrq,
    sbsj,
    xbsj,
    shiftclassid,
    shiftclassname
FROM shift_data
WHERE objid = :'p_objid'
ORDER BY dbrq DESC, sbsj DESC
LIMIT 5;

-- 3. 检查是否能找到父级机构代码
WITH parent_org AS (
    SELECT porgcode as parent_orgcode
    FROM sys_org_relation
    WHERE orgcode = :'p_objid' AND used = 1
    UNION ALL
    SELECT orgcode as parent_orgcode
    FROM shift_data
    WHERE objid = :'p_objid'
    LIMIT 1
)
SELECT
    '3. 父级机构代码' as test_name,
    parent_orgcode
FROM parent_org
WHERE parent_orgcode IS NOT NULL
LIMIT 1;

-- 4. 检查当前时间点的班次
WITH parent_org AS (
    SELECT COALESCE(
        (SELECT porgcode FROM sys_org_relation WHERE orgcode = :'p_objid' AND used = 1),
        (SELECT orgcode FROM shift_data WHERE objid = :'p_objid' LIMIT 1)
    ) as parent_orgcode
)
SELECT
    '4. 当前班次检查' as test_name,
    sd.*
FROM shift_data sd, parent_org po
WHERE sd.orgcode = po.parent_orgcode
  AND sd.objid = :'p_objid'
  AND (
      -- 同一天的班次，当前时间在上下班时间范围内
      (sd.dbrq = SUBSTRING(:'p_datetime' FROM 1 FOR 10)
       AND SUBSTRING(:'p_datetime' FROM 12 FOR 8) BETWEEN sd.sbsj AND sd.xbsj)
      OR
      -- 跨日班次处理
      (sd.dbrq = TO_CHAR(TO_DATE(SUBSTRING(:'p_datetime' FROM 1 FOR 10), 'YYYY-MM-DD') - INTERVAL '1 day', 'YYYY-MM-DD')
       AND sd.xbsj < sd.sbsj
       AND SUBSTRING(:'p_datetime' FROM 12 FOR 8) >= sd.sbsj)
      OR
      (sd.dbrq = SUBSTRING(:'p_datetime' FROM 1 FOR 10)
       AND sd.xbsj < sd.sbsj
       AND SUBSTRING(:'p_datetime' FROM 12 FOR 8) <= sd.xbsj)
  )
ORDER BY sd.dbrq DESC, sd.sbsj DESC
LIMIT 1;
