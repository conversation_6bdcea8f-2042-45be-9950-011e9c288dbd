-- 调试特定参数的查询
-- 参数：objid='ZQW9LIB5301C4LN5JG0207', datetime='2025-07-29 14:30:00', acctobj_id='ZR880Q91V07EDEWGGZ1121'

-- 第1步：检查机构关系
SELECT 
    '1. 机构关系检查' as step_name,
    orgcode,
    porgcode,
    used
FROM sys_org_relation 
WHERE orgcode = 'ZQW9LIB5301C4LN5JG0207' AND used = 1;

-- 第2步：检查班次数据中是否有该objid
SELECT 
    '2. 班次数据检查(按objid)' as step_name,
    orgcode,
    objid,
    objname,
    dbrq,
    sbsj,
    xbsj,
    shiftclassid,
    shiftclassname
FROM shift_data 
WHERE objid = 'ZQW9LIB5301C4LN5JG0207'
ORDER BY dbrq DESC, sbsj DESC
LIMIT 5;

-- 第3步：获取父级机构代码
WITH parent_org AS (
    SELECT COALESCE(
        (SELECT porgcode FROM sys_org_relation WHERE orgcode = 'ZQW9LIB5301C4LN5JG0207' AND used = 1),
        (SELECT orgcode FROM shift_data WHERE objid = 'ZQW9LIB5301C4LN5JG0207' LIMIT 1)
    ) as parent_orgcode
)
SELECT 
    '3. 父级机构代码' as step_name,
    parent_orgcode
FROM parent_org;

-- 第4步：检查指定时间的班次（使用父级机构代码）
WITH parent_org AS (
    SELECT COALESCE(
        (SELECT porgcode FROM sys_org_relation WHERE orgcode = 'ZQW9LIB5301C4LN5JG0207' AND used = 1),
        (SELECT orgcode FROM shift_data WHERE objid = 'ZQW9LIB5301C4LN5JG0207' LIMIT 1)
    ) as parent_orgcode
)
SELECT 
    '4. 当前时间班次检查' as step_name,
    sd.orgcode,
    sd.objid,
    sd.dbrq,
    sd.sbsj,
    sd.xbsj,
    sd.shiftclassid,
    '2025-07-29' as query_date,
    '14:30:00' as query_time,
    CASE 
        WHEN sd.dbrq = '2025-07-29' AND '14:30:00' BETWEEN sd.sbsj AND sd.xbsj THEN '时间匹配-同日'
        WHEN sd.dbrq = '2025-07-28' AND sd.xbsj < sd.sbsj AND '14:30:00' >= sd.sbsj THEN '时间匹配-跨日前'
        WHEN sd.dbrq = '2025-07-29' AND sd.xbsj < sd.sbsj AND '14:30:00' <= sd.xbsj THEN '时间匹配-跨日后'
        ELSE '时间不匹配'
    END as time_match_status
FROM shift_data sd, parent_org po
WHERE sd.orgcode = po.parent_orgcode
  AND sd.objid = 'ZQW9LIB5301C4LN5JG0207'
ORDER BY sd.dbrq DESC, sd.sbsj DESC
LIMIT 10;

-- 第5步：查找当前班次（精确匹配）
WITH parent_org AS (
    SELECT COALESCE(
        (SELECT porgcode FROM sys_org_relation WHERE orgcode = 'ZQW9LIB5301C4LN5JG0207' AND used = 1),
        (SELECT orgcode FROM shift_data WHERE objid = 'ZQW9LIB5301C4LN5JG0207' LIMIT 1)
    ) as parent_orgcode
),
current_shift AS (
    SELECT 
        sd.orgcode, sd.dbrq, sd.objid, sd.shiftclassid, sd.shiftclassname, sd.sbsj, sd.xbsj
    FROM shift_data sd, parent_org po
    WHERE sd.orgcode = po.parent_orgcode
      AND sd.objid = 'ZQW9LIB5301C4LN5JG0207'
      AND (
          (sd.dbrq = '2025-07-29' AND '14:30:00' BETWEEN sd.sbsj AND sd.xbsj)
          OR
          (sd.dbrq = '2025-07-28' AND sd.xbsj < sd.sbsj AND '14:30:00' >= sd.sbsj)
          OR
          (sd.dbrq = '2025-07-29' AND sd.xbsj < sd.sbsj AND '14:30:00' <= sd.xbsj)
      )
    ORDER BY sd.dbrq DESC, sd.sbsj DESC
    LIMIT 1
)
SELECT 
    '5. 当前班次信息' as step_name,
    cs.*
FROM current_shift cs;

-- 第6步：查找上个班次
WITH parent_org AS (
    SELECT COALESCE(
        (SELECT porgcode FROM sys_org_relation WHERE orgcode = 'ZQW9LIB5301C4LN5JG0207' AND used = 1),
        (SELECT orgcode FROM shift_data WHERE objid = 'ZQW9LIB5301C4LN5JG0207' LIMIT 1)
    ) as parent_orgcode
),
current_shift AS (
    SELECT 
        sd.orgcode, sd.dbrq, sd.objid, sd.shiftclassid, sd.sbsj, sd.xbsj
    FROM shift_data sd, parent_org po
    WHERE sd.orgcode = po.parent_orgcode
      AND sd.objid = 'ZQW9LIB5301C4LN5JG0207'
      AND (
          (sd.dbrq = '2025-07-29' AND '14:30:00' BETWEEN sd.sbsj AND sd.xbsj)
          OR
          (sd.dbrq = '2025-07-28' AND sd.xbsj < sd.sbsj AND '14:30:00' >= sd.sbsj)
          OR
          (sd.dbrq = '2025-07-29' AND sd.xbsj < sd.sbsj AND '14:30:00' <= sd.xbsj)
      )
    ORDER BY sd.dbrq DESC, sd.sbsj DESC
    LIMIT 1
),
previous_shift AS (
    SELECT 
        sd.shiftclassid, sd.shiftclassname, sd.sbsj, sd.xbsj, sd.dbrq, sd.objid
    FROM shift_data sd, parent_org po, current_shift cs
    WHERE sd.orgcode = po.parent_orgcode
      AND (
          (sd.dbrq = cs.dbrq AND sd.xbsj = cs.sbsj)
          OR
          (sd.dbrq = TO_CHAR(TO_DATE(cs.dbrq, 'YYYY-MM-DD') - INTERVAL '1 day', 'YYYY-MM-DD')
           AND sd.xbsj = cs.sbsj)
          OR
          (sd.dbrq = cs.dbrq AND sd.sbsj < cs.sbsj)
          OR
          (sd.dbrq = TO_CHAR(TO_DATE(cs.dbrq, 'YYYY-MM-DD') - INTERVAL '1 day', 'YYYY-MM-DD')
           AND NOT EXISTS (
               SELECT 1 FROM shift_data sd2, parent_org po2
               WHERE sd2.orgcode = po2.parent_orgcode
                 AND sd2.dbrq = cs.dbrq
                 AND sd2.sbsj < cs.sbsj
           ))
      )
    ORDER BY
        CASE WHEN sd.xbsj = cs.sbsj THEN 1 ELSE 2 END,
        sd.dbrq DESC,
        sd.sbsj DESC
    LIMIT 1
)
SELECT 
    '6. 上个班次信息' as step_name,
    ps.*
FROM previous_shift ps;

-- 第7步：检查采集点相关表数据量
SELECT 
    '7a. acctobj_input 总数据量' as step_name,
    COUNT(*) as total_count,
    COUNT(CASE WHEN tmused = 1 THEN 1 END) as used_count
FROM acctobj_input;

SELECT 
    '7b. acctobj_inputmx 总数据量' as step_name,
    COUNT(*) as total_count,
    COUNT(CASE WHEN tmused = 1 THEN 1 END) as used_count
FROM acctobj_inputmx;

-- 第8步：检查是否有匹配的采集点数据
WITH parent_org AS (
    SELECT COALESCE(
        (SELECT porgcode FROM sys_org_relation WHERE orgcode = 'ZQW9LIB5301C4LN5JG0207' AND used = 1),
        (SELECT orgcode FROM shift_data WHERE objid = 'ZQW9LIB5301C4LN5JG0207' LIMIT 1)
    ) as parent_orgcode
),
current_shift AS (
    SELECT 
        sd.dbrq, sd.sbsj, sd.shiftclassid, sd.objid
    FROM shift_data sd, parent_org po
    WHERE sd.orgcode = po.parent_orgcode
      AND sd.objid = 'ZQW9LIB5301C4LN5JG0207'
      AND (
          (sd.dbrq = '2025-07-29' AND '14:30:00' BETWEEN sd.sbsj AND sd.xbsj)
          OR
          (sd.dbrq = '2025-07-28' AND sd.xbsj < sd.sbsj AND '14:30:00' >= sd.sbsj)
          OR
          (sd.dbrq = '2025-07-29' AND sd.xbsj < sd.sbsj AND '14:30:00' <= sd.xbsj)
      )
    ORDER BY sd.dbrq DESC, sd.sbsj DESC
    LIMIT 1
),
previous_shift AS (
    SELECT 
        sd.shiftclassid, sd.sbsj, sd.objid
    FROM shift_data sd, parent_org po, current_shift cs
    WHERE sd.orgcode = po.parent_orgcode
      AND (
          (sd.dbrq = cs.dbrq AND sd.xbsj = cs.sbsj)
          OR
          (sd.dbrq = TO_CHAR(TO_DATE(cs.dbrq, 'YYYY-MM-DD') - INTERVAL '1 day', 'YYYY-MM-DD')
           AND sd.xbsj = cs.sbsj)
          OR
          (sd.dbrq = cs.dbrq AND sd.sbsj < cs.sbsj)
          OR
          (sd.dbrq = TO_CHAR(TO_DATE(cs.dbrq, 'YYYY-MM-DD') - INTERVAL '1 day', 'YYYY-MM-DD')
           AND NOT EXISTS (
               SELECT 1 FROM shift_data sd2, parent_org po2
               WHERE sd2.orgcode = po2.parent_orgcode
                 AND sd2.dbrq = cs.dbrq
                 AND sd2.sbsj < cs.sbsj
           ))
      )
    ORDER BY
        CASE WHEN sd.xbsj = cs.sbsj THEN 1 ELSE 2 END,
        sd.dbrq DESC,
        sd.sbsj DESC
    LIMIT 1
)
SELECT 
    '8. 匹配的acctobj_input记录' as step_name,
    ai.id,
    ai.acctobj_id,
    ai.bcdm,
    ai.sbsj,
    ai.team_id,
    ps.shiftclassid as expected_bcdm,
    ps.sbsj as expected_sbsj,
    ps.objid as expected_team_id
FROM acctobj_input ai, previous_shift ps
WHERE ai.tmused = 1
  AND ai.bcdm = ps.shiftclassid
  AND ai.sbsj = ps.sbsj
  AND ai.team_id = ps.objid
  AND ai.acctobj_id = 'ZR880Q91V07EDEWGGZ1121'
LIMIT 5;
