-- 测试2：查找上个班次
-- 基于当前班次信息查找上个班次

-- 替换这里的参数值进行测试
\set p_objid 'YOUR_OBJID_HERE'
\set p_datetime '2024-01-15 14:30:00'

-- 先获取当前班次信息
WITH parent_org AS (
    SELECT COALESCE(
        (SELECT porgcode FROM sys_org_relation WHERE orgcode = :'p_objid' AND used = 1),
        (SELECT orgcode FROM shift_data WHERE objid = :'p_objid' LIMIT 1)
    ) as parent_orgcode
),
current_shift AS (
    SELECT 
        sd.orgcode, sd.modelid, sd.dbrq, sd.objid, sd.objname, sd.objtype,
        sd.shiftclassid, sd.shiftclassname, sd.sbsj, sd.xbsj, sd.tjrq, sd.gzsj, sd.progid
    FROM shift_data sd, parent_org po
    WHERE sd.orgcode = po.parent_orgcode
      AND sd.objid = :'p_objid'
      AND (
          -- 同一天的班次，当前时间在上下班时间范围内
          (sd.dbrq = SUBSTRING(:'p_datetime' FROM 1 FOR 10) 
           AND SUBSTRING(:'p_datetime' FROM 12 FOR 8) BETWEEN sd.sbsj AND sd.xbsj)
          OR
          -- 跨日班次处理
          (sd.dbrq = TO_CHAR(TO_DATE(SUBSTRING(:'p_datetime' FROM 1 FOR 10), 'YYYY-MM-DD') - INTERVAL '1 day', 'YYYY-MM-DD')
           AND sd.xbsj < sd.sbsj
           AND SUBSTRING(:'p_datetime' FROM 12 FOR 8) >= sd.sbsj)
          OR
          (sd.dbrq = SUBSTRING(:'p_datetime' FROM 1 FOR 10)
           AND sd.xbsj < sd.sbsj
           AND SUBSTRING(:'p_datetime' FROM 12 FOR 8) <= sd.xbsj)
      )
    ORDER BY sd.dbrq DESC, sd.sbsj DESC
    LIMIT 1
)
-- 显示当前班次
SELECT 
    '当前班次信息' as info_type,
    cs.*
FROM current_shift cs;

-- 查找上个班次
WITH parent_org AS (
    SELECT COALESCE(
        (SELECT porgcode FROM sys_org_relation WHERE orgcode = :'p_objid' AND used = 1),
        (SELECT orgcode FROM shift_data WHERE objid = :'p_objid' LIMIT 1)
    ) as parent_orgcode
),
current_shift AS (
    SELECT 
        sd.orgcode, sd.modelid, sd.dbrq, sd.objid, sd.objname, sd.objtype,
        sd.shiftclassid, sd.shiftclassname, sd.sbsj, sd.xbsj, sd.tjrq, sd.gzsj, sd.progid
    FROM shift_data sd, parent_org po
    WHERE sd.orgcode = po.parent_orgcode
      AND sd.objid = :'p_objid'
      AND (
          (sd.dbrq = SUBSTRING(:'p_datetime' FROM 1 FOR 10) 
           AND SUBSTRING(:'p_datetime' FROM 12 FOR 8) BETWEEN sd.sbsj AND sd.xbsj)
          OR
          (sd.dbrq = TO_CHAR(TO_DATE(SUBSTRING(:'p_datetime' FROM 1 FOR 10), 'YYYY-MM-DD') - INTERVAL '1 day', 'YYYY-MM-DD')
           AND sd.xbsj < sd.sbsj
           AND SUBSTRING(:'p_datetime' FROM 12 FOR 8) >= sd.sbsj)
          OR
          (sd.dbrq = SUBSTRING(:'p_datetime' FROM 1 FOR 10)
           AND sd.xbsj < sd.sbsj
           AND SUBSTRING(:'p_datetime' FROM 12 FOR 8) <= sd.xbsj)
      )
    ORDER BY sd.dbrq DESC, sd.sbsj DESC
    LIMIT 1
),
previous_shift AS (
    SELECT 
        sd.orgcode, sd.modelid, sd.dbrq, sd.objid, sd.objname, sd.objtype,
        sd.shiftclassid, sd.shiftclassname, sd.sbsj, sd.xbsj, sd.tjrq, sd.gzsj, sd.progid
    FROM shift_data sd, parent_org po, current_shift cs
    WHERE sd.orgcode = po.parent_orgcode
      AND (
          -- 情况1：同一天的上个班次，下班时间等于当前班次上班时间
          (sd.dbrq = cs.dbrq AND sd.xbsj = cs.sbsj)
          OR
          -- 情况2：前一天的班次，下班时间等于当前班次上班时间（跨日情况）
          (sd.dbrq = TO_CHAR(TO_DATE(cs.dbrq, 'YYYY-MM-DD') - INTERVAL '1 day', 'YYYY-MM-DD')
           AND sd.xbsj = cs.sbsj)
          OR
          -- 情况3：如果找不到精确匹配，则找同一天最接近的前一个班次
          (sd.dbrq = cs.dbrq AND sd.sbsj < cs.sbsj)
          OR
          -- 情况4：如果当天没有前一个班次，则找前一天的最后一个班次
          (sd.dbrq = TO_CHAR(TO_DATE(cs.dbrq, 'YYYY-MM-DD') - INTERVAL '1 day', 'YYYY-MM-DD')
           AND NOT EXISTS (
               SELECT 1 FROM shift_data sd2, parent_org po2
               WHERE sd2.orgcode = po2.parent_orgcode
                 AND sd2.dbrq = cs.dbrq
                 AND sd2.sbsj < cs.sbsj
           ))
      )
    ORDER BY
        CASE WHEN sd.xbsj = cs.sbsj THEN 1 ELSE 2 END,  -- 优先精确匹配
        sd.dbrq DESC,
        sd.sbsj DESC
    LIMIT 1
)
-- 显示上个班次
SELECT 
    '上个班次信息' as info_type,
    ps.*
FROM previous_shift ps;
