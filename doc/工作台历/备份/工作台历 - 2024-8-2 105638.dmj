{"RootName": "DataModels", "CTVER": "43543337", "TableCount": 21, "CreateDate": "2024/6/24 11:07:21", "ModifyDate": "2024/6/24 11:07:21", "Count": 1, "items": [{"ID": 1, "Name": "模型", "CreateDate": "2024/6/6 9:30:12", "OrderNo": 1, "CustomAttr1": "DVS:-202.48,-463.89,1.00,0,", "ConfigStr": "DrawerWidth=2028\r\nDrawerHeight=2560\r\nWorkAreaColor=16777215\r\nSelectedColor=16711680\r\nDefaultObjectColor=15921906\r\nDefaultTitleColor=255\r\nDefaultPKColor=16711935\r\nDefaultFKColor=16711680\r\nDefaultBorderColor=12632256\r\nDefaultLineColor=16711680\r\nDefaultGroupEdgeColor=128\r\nShowFieldType=1\r\nShowFieldIcon=1\r\nShowPhyFieldName=2\r\nDatabaseEngine=\r\nGenFKIndexesSQL=0\r\nIndependPosForOverviewMode=0\r\n", "Tables": {"Count": 21, "items": [{"ID": 1, "Name": "ACCTOBJ_INPUT", "Caption": "核算对象录入表（主表）", "CreateDate": "2024/1/31 15:24:46", "OrderNo": 1, "GraphDesc": "Left=2571.35\r\nTop=1121.96", "MetaFields": {"Count": 16, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DisplayName": "编号", "DataType": 1, "KeyFieldType": 1, "RelateTable": "JOBLIST_INPUT_MAPPING", "RelateField": "SLAVE_ID", "DataLength": 50, "GraphDesc": "P1=2455.61,1174.00\r\nP2=2513.00,1174.00\r\nP3=2513.00,1174.00\r\nP4=2571.35,1174.00\r\nHookP1=424.39,51.34\r\nHookP2=19.65,52.04\r\nMod_OP1=0\r\nMod_OP2=0\r\nMod_CP=0\r\nHorz1=1\r\nHorz2=1"}, {"ID": 15, "Name": "BA_ID", "Memo": "BA：Business Activity", "OrderNo": 2, "DisplayName": "业务活动ID", "DataType": 1, "RelateTable": "任务督办", "DataLength": 200}, {"ID": 16, "Name": "BA_NAME", "Memo": "BA：Business Activity", "OrderNo": 3, "DisplayName": "业务活动名称", "DataType": 1, "RelateTable": "任务督办", "DataLength": 200}, {"ID": 3, "Name": "ACCTOBJ_ID", "OrderNo": 4, "DisplayName": "核算对象ID", "DataType": 1, "RelateTable": "任务督办", "DataLength": 200}, {"ID": 13, "Name": "ACCTOBJ_NAME", "OrderNo": 5, "DisplayName": "核算对象名称", "DataType": 1, "RelateTable": "任务督办", "DataLength": 200}, {"ID": 4, "Name": "INPUT_TIME", "OrderNo": 6, "DisplayName": "录入时间", "DataType": 4, "RelateTable": "任务督办"}, {"ID": 5, "Name": "INPUT_LONGITUDE", "OrderNo": 7, "DisplayName": "录入时所在位置经度", "DataType": 3, "RelateTable": "任务督办", "DataLength": 18, "DataScale": 6}, {"ID": 6, "Name": "INPUT_LATITUDE", "OrderNo": 8, "DisplayName": "录入时所在位置纬度", "DataType": 3, "RelateTable": "任务督办", "DataLength": 18, "DataScale": 6}, {"ID": 14, "Name": "OPERATING_RADIUS", "Memo": "录入人不在作业半径以内，不允许录入", "OrderNo": 9, "DisplayName": "作业半径", "DataType": 3, "RelateTable": "任务督办", "DataLength": 18, "DataScale": 6}, {"ID": 7, "Name": "BCDM", "OrderNo": 10, "DisplayName": "班次代码", "DataType": 1, "RelateTable": "任务督办", "DataLength": 50}, {"ID": 4, "Name": "BCMC", "OrderNo": 11, "DisplayName": "班次名称", "DataType": 1, "RelateTable": "任务督办", "DataLength": 200}, {"ID": 5, "Name": "SBSJ", "OrderNo": 12, "DisplayName": "上班时间", "DataType": 4, "RelateTable": "任务督办"}, {"ID": 6, "Name": "XBSJ", "OrderNo": 13, "DisplayName": "下班时间", "DataType": 4, "RelateTable": "任务督办"}, {"ID": 14, "Name": "TEAM_ID", "OrderNo": 14, "DisplayName": "班组ID", "DataType": 1, "RelateTable": "任务督办", "DataLength": 50}, {"ID": 15, "Name": "TEAM_NAME", "OrderNo": 15, "DisplayName": "班组名称", "DataType": 1, "RelateTable": "任务督办", "DataLength": 200}, {"ID": 12, "Name": "TMUSED", "Memo": "1=可用，0=无效", "OrderNo": 16, "DisplayName": "可用标识", "DataType": 2, "RelateTable": "任务督办"}]}}, {"ID": 3, "Name": "ACCTOBJ_INPUTMX", "Caption": "核算对象采集点录入表（明细）", "CreateDate": "2024/1/31 15:46:14", "OrderNo": 2, "GraphDesc": "Left=3464.35\r\nTop=1113.96", "MetaFields": {"Count": 17, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DisplayName": "编号", "DataType": 1, "KeyFieldType": 1, "RelateTable": "任务督办", "DataLength": 50}, {"ID": 15, "Name": "BA_ID", "Memo": "BA：Business Activity", "OrderNo": 2, "DisplayName": "业务活动ID", "DataType": 1, "RelateTable": "任务督办", "DataLength": 200}, {"ID": 3, "Name": "ACCTOBJ_ID", "OrderNo": 3, "DisplayName": "核算对象ID", "DataType": 1, "RelateTable": "任务督办", "DataLength": 200}, {"ID": 3, "Name": "IPT_ID", "Memo": "ACCTOBJ_INPUT 表的 ID", "OrderNo": 4, "DisplayName": "录入主表ID", "DataType": 1, "KeyFieldType": 3, "RelateTable": "ACCTOBJ_INPUT", "RelateField": "ID", "DataLength": 50, "GraphDesc": "P1=2867.35,1260.00\r\nP2=2958.00,1260.00\r\nP3=2958.00,1177.00\r\nP4=3464.35,1177.00\r\nHookP1=132.00,138.04\r\nHookP2=19.65,63.04\r\nMod_OP1=1\r\nMod_OP2=0\r\nMod_CP=0\r\nHorz1=1\r\nHorz2=1"}, {"ID": 17, "Name": "IPT_BATCH_ID", "OrderNo": 5, "DisplayName": "录入批次ID", "DataType": 1, "KeyFieldType": 3, "RelateTable": "ACCTOBJ_INPUT_BATCH", "RelateField": "ID", "DataLength": 50, "GraphDesc": "P1=3395.00,1281.00\r\nP2=3430.00,1281.00\r\nP3=3430.00,1281.00\r\nP4=3464.35,1281.00\r\nHookP1=386.00,83.00\r\nHookP2=27.65,167.04\r\nMod_OP1=0\r\nMod_OP2=0\r\nMod_CP=0\r\nHorz1=1\r\nHorz2=1"}, {"ID": 4, "Name": "INPUT_TIME", "OrderNo": 6, "DisplayName": "录入时间", "DataType": 4, "RelateTable": "任务督办"}, {"ID": 8, "Name": "TAG_NO", "OrderNo": 7, "DisplayName": "仪表位号", "DataType": 1, "RelateTable": "任务督办", "DataLength": 200}, {"ID": 13, "Name": "COLLECT_POINT_ID", "OrderNo": 8, "DisplayName": "采集点ID", "DataType": 1, "RelateTable": "任务督办", "DataLength": 50}, {"ID": 5, "Name": "COLLECT_POINT", "OrderNo": 9, "DisplayName": "采集点", "DataType": 1, "RelateTable": "任务督办", "DataLength": 200}, {"ID": 7, "Name": "COLLECT_POINT_VAL", "OrderNo": 10, "DisplayName": "采集点录入值", "DataType": 1, "RelateTable": "任务督办", "DataLength": 4000}, {"ID": 11, "Name": "COLLECT_POINT_TEXT", "OrderNo": 11, "DisplayName": "下拉框选择值对应的显示值", "DataType": 1, "RelateTable": "任务督办", "DataLength": 200}, {"ID": 9, "Name": "SN", "OrderNo": 12, "DisplayName": "序号", "DataType": 2, "RelateTable": "任务督办"}, {"ID": 12, "Name": "TMUSED", "Memo": "1=可用，0=无效", "OrderNo": 13, "DisplayName": "可用标识", "DataType": 2, "RelateTable": "任务督办"}, {"ID": 12, "Name": "INPUT_COMP_TYPE", "Memo": "空=文本输入框，textfield=文本输入框，numberfield=数值输入框，datetimefield=日期时间选择框，combobox=下拉选择框，checkbox=复选框", "OrderNo": 14, "DisplayName": "录入组件类型", "DataType": 1, "RelateTable": "任务督办", "DataLength": 50}, {"ID": 13, "Name": "INPUT_OPTIONS", "Memo": "下拉选择框的备选数据，格式为JSON数组：[{value:\"\",text:\"\"}]", "OrderNo": 15, "DisplayName": "录入组件备选数据", "DataType": 1, "RelateTable": "任务督办", "DataLength": 4000}, {"ID": 14, "Name": "EQUIP_MT_REMARK", "Memo": "对应设备维护保养表格中的记录列", "OrderNo": 16, "DisplayName": "设备维保备注信息", "DataType": 1, "RelateTable": "任务督办", "DataLength": 4000}, {"ID": 15, "Name": "IS_EQUIP_MAINTENANCE", "Memo": "1=是设备维保，0=不是", "OrderNo": 17, "DisplayName": "设备维保业务标识", "DataType": 2, "RelateTable": "任务督办"}]}}, {"ID": 3, "Name": "采集点录入", "TypeName": "GROUP", "Memo": "采集点录入（复用：辽兴项目移动端录入表结构）", "CreateDate": "2024/6/6 9:40:51", "OrderNo": 3, "GraphDesc": "Left=2536.35\r\nTop=1072.96\r\nWidth=1373.00\r\nHeight=367.00\r\nAutoSize=0\r\nBWidth=0.00\r\nBHeight=0.00", "BgColor": 16777215, "MetaFields": {"items": []}}, {"ID": 4, "Name": "JOBLIST_INPUT_DATA", "Caption": "工作清单录入数据表", "CreateDate": "2024/6/6 9:44:35", "OrderNo": 4, "GraphDesc": "Left=1562.35\r\nTop=1102.96\r\nWidth=299.00\r\nHeight=149.00\r\nAutoSize=0", "MetaFields": {"Count": 7, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DisplayName": "编号", "DataType": 1, "KeyFieldType": 1, "RelateTable": "JOBLIST_INPUT_MAPPING", "RelateField": "MASTER_ID", "DataLength": 50, "GraphDesc": "P1=2011.61,1181.00\r\nP2=1936.00,1181.00\r\nP3=1936.00,1181.00\r\nP4=1861.35,1181.00\r\nHookP1=36.39,58.34\r\nHookP2=278.65,78.04\r\nMod_OP1=0\r\nMod_OP2=0\r\nMod_CP=0\r\nHorz1=1\r\nHorz2=1"}, {"ID": 15, "Name": "JOB_ID", "OrderNo": 2, "DisplayName": "作业活动ID", "DataType": 1, "KeyFieldType": 3, "RelateTable": "JOBLIST_ACTIVITYEXAMPLE", "RelateField": "ID", "DataLength": 50, "GraphDesc": "P1=1279.07,663.00\r\nP2=1681.00,663.00\r\nP3=1681.00,663.00\r\nP4=1681.00,1102.96\r\nHookP1=178.00,144.80\r\nHookP2=118.65,36.04\r\nMod_OP1=1\r\nMod_OP2=1\r\nMod_CP=1\r\nHorz1=1\r\nHorz2=0"}, {"ID": 16, "Name": "JOB_TYPE", "Memo": "1=例行工作，2=例外工作", "OrderNo": 3, "DisplayName": "作业活动类型", "DataType": 2, "RelateTable": "任务督办"}, {"ID": 14, "Name": "INPUT_TIME", "OrderNo": 4, "DisplayName": "录入时间", "DataType": 4, "RelateTable": "任务督办"}, {"ID": 6, "Name": "INPUT_PERSON_ID", "OrderNo": 5, "DisplayName": "录入人ID", "DataType": 1, "RelateTable": "任务督办", "DataLength": 50}, {"ID": 7, "Name": "INPUT_PERSON_NAME", "OrderNo": 6, "DisplayName": "录入人", "DataType": 1, "RelateTable": "任务督办", "DataLength": 200}, {"ID": 12, "Name": "TMUSED", "Memo": "1=可用，0=无效", "OrderNo": 7, "DisplayName": "可用标识", "DataType": 2, "RelateTable": "任务督办"}]}}, {"ID": 5, "Name": "作业活动录入", "TypeName": "GROUP", "Memo": "作业活动录入", "CreateDate": "2024/6/6 9:49:47", "OrderNo": 5, "GraphDesc": "Left=1533.35\r\nTop=1052.96\r\nWidth=358.00\r\nHeight=239.00\r\nAutoSize=0\r\nBWidth=0.00\r\nBHeight=0.00", "BgColor": 16777215, "MetaFields": {"items": []}}, {"ID": 7, "Name": "JOBLIST_INPUT_MAPPING", "Caption": "作业活动录入关系表", "Memo": "用于连接内部功能模块、外部数据表结构", "CreateDate": "2024/6/6 9:58:46", "OrderNo": 6, "GraphDesc": "Left=2011.61\r\nTop=1122.66", "MetaFields": {"Count": 5, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DisplayName": "编号", "DataType": 1, "KeyFieldType": 1, "RelateTable": "任务督办", "DataLength": 50}, {"ID": 15, "Name": "MAP_TYPE", "Memo": "temp_work=临时工作，ledger=电子台账，account=核算报表，collection_point=采集点，general=常规录入", "OrderNo": 2, "DisplayName": "关系类型（根据实际业务约定）", "DataType": 1, "RelateTable": "任务督办"}, {"ID": 4, "Name": "MASTER_ID", "Memo": "逻辑相当于主表记录ID作为从表的外键（主表 : 从表 = 1 : n）", "OrderNo": 3, "DisplayName": "主表记录ID", "DataType": 1, "RelateTable": "任务督办", "GraphDesc": "P1=348.40,1470.62\r\nP2=428.00,1470.62\r\nP3=428.00,1470.62\r\nP4=507.66,1470.62\r\nHookP1=124.00,108.99\r\nHookP2=206.00,50.29\r\nMod_OP1=1\r\nMod_OP2=1\r\nMod_CP=0\r\nHorz1=1\r\nHorz2=1"}, {"ID": 5, "Name": "SLAVE_ID", "Memo": "逻辑相当于主表记录ID作为从表的外键（主表 : 从表 = 1 : n）", "OrderNo": 4, "DisplayName": "从表记录ID", "DataType": 1, "RelateTable": "任务督办", "GraphDesc": "P1=1065.00,200.00\r\nP2=998.00,200.00\r\nP3=998.00,200.00\r\nP4=930.86,200.00\r\nHookP1=20.00,85.00\r\nHookP2=416.14,58.73\r\nMod_OP1=0\r\nMod_OP2=0\r\nMod_CP=0\r\nHorz1=1\r\nHorz2=1"}, {"ID": 12, "Name": "TMUSED", "Memo": "1=可用，0=无效", "OrderNo": 5, "DisplayName": "可用标识", "DataType": 2, "RelateTable": "任务督办"}]}}, {"ID": 2, "Name": "工作清单设置", "TypeName": "GROUP", "Memo": "工作清单设置", "CreateDate": "2024/6/5 10:22:04", "OrderNo": 7, "GraphDesc": "Left=35.11\r\nTop=35.00\r\nWidth=1374.78\r\nHeight=1129.17\r\nAutoSize=0\r\nBWidth=0.00\r\nBHeight=0.00", "BgColor": 16777215, "MetaFields": {"items": []}}, {"ID": 1, "Name": "JOBLIST_UNIT", "Caption": "作业单元", "Memo": "作业单元，作业活动依附在作业单元上", "CreateDate": "2024/6/5 10:21:44", "OrderNo": 8, "GraphDesc": "Left=524.75\r\nTop=338.56", "MetaFields": {"Count": 8, "items": [{"ID": 1, "Name": "ID", "Memo": "作业单元的子单元、作业活动要使用", "OrderNo": 1, "DisplayName": "作业单元ID", "DataType": 1, "KeyFieldType": 1, "RelateTable": "任务督办", "DataLength": 100}, {"ID": 2, "Name": "PID", "Memo": "父单元ID，根分类ID是root", "OrderNo": 2, "DisplayName": "父单元ID", "DataType": 1, "RelateTable": "任务督办", "DataLength": 100}, {"ID": 3, "Name": "ORGID", "Memo": "作业单元所属的机构ID", "OrderNo": 3, "DisplayName": "机构ID", "DataType": 1, "RelateTable": "任务督办", "DataLength": 100}, {"ID": 4, "Name": "UNITNAME", "OrderNo": 4, "DisplayName": "作业单元名称", "DataType": 1, "RelateTable": "任务督办", "DataLength": 200}, {"ID": 12, "Name": "TYPEID", "Memo": "作业单元的类型ID，作业单元是管理还是操作", "OrderNo": 5, "DisplayName": "类型ID", "DataType": 1, "KeyFieldType": 3, "RelateTable": "JOBLIST_CLASS", "RelateField": "TYPEID", "DataLength": 100, "GraphDesc": "P1=584.00,238.34\r\nP2=584.00,288.00\r\nP3=584.00,288.00\r\nP4=584.00,338.56\r\nHookP1=140.07,113.66\r\nHookP2=59.25,28.44\r\nMod_OP1=0\r\nMod_OP2=0\r\nMod_CP=0\r\nHorz1=0\r\nHorz2=0"}, {"ID": 5, "Name": "MEMO", "OrderNo": 6, "DisplayName": "描述", "DataType": 1, "RelateTable": "任务督办", "DataLength": 2000}, {"ID": 13, "Name": "TMUSED", "Memo": "1 使用，0 已删除，默认使用", "OrderNo": 7, "DisplayName": "使用标识", "DataType": 2, "RelateTable": "任务督办"}, {"ID": 11, "Name": "TMSORT", "OrderNo": 8, "DisplayName": "排序", "DataType": 2, "RelateTable": "任务督办"}]}}, {"ID": 4, "Name": "JOBLIST_PERSONBIND", "Caption": "作业单元的人员绑定", "Memo": "作业单元上绑定的岗位和工区", "CreateDate": "2024/6/5 10:23:36", "OrderNo": 9, "GraphDesc": "Left=934.83\r\nTop=363.18", "MetaFields": {"Count": 6, "items": [{"ID": 1, "Name": "ID", "Memo": "唯一键", "OrderNo": 1, "DisplayName": "编号", "DataType": 1, "KeyFieldType": 1, "RelateTable": "任务督办", "DataLength": 100}, {"ID": 2, "Name": "PID", "Memo": "绑定关联的作业单元ID,关联的分类ID，关联的活动ID", "OrderNo": 2, "DisplayName": "关联ID", "DataType": 1, "KeyFieldType": 3, "RelateTable": "JOBLIST_UNIT", "RelateField": "ID", "DataLength": 100, "GraphDesc": "P1=753.75,422.00\r\nP2=844.00,422.00\r\nP3=844.00,422.00\r\nP4=934.83,422.00\r\nHookP1=209.25,83.44\r\nHookP2=20.17,58.82\r\nMod_OP1=0\r\nMod_OP2=0\r\nMod_CP=0\r\nHorz1=1\r\nHorz2=1"}, {"ID": 12, "Name": "ORGID", "Memo": "作业单元的所属机构ID", "OrderNo": 3, "DisplayName": "机构ID", "DataType": 1, "KeyFieldType": 3, "RelateTable": "JOBLIST_CLASS", "RelateField": "ID", "DataLength": 100, "GraphDesc": "P1=643.93,210.00\r\nP2=789.00,210.00\r\nP3=789.00,391.00\r\nP4=934.83,391.00\r\nHookP1=172.07,121.66\r\nHookP2=28.17,27.82\r\nMod_OP1=0\r\nMod_OP2=0\r\nMod_CP=0\r\nHorz1=1\r\nHorz2=1"}, {"ID": 13, "Name": "BINDTYPE", "Memo": "类型：0 人员ID，1 岗位ID，2 工区ID，3 专业ID", "OrderNo": 4, "DisplayName": "绑定类型", "DataType": 2, "RelateTable": "任务督办"}, {"ID": 14, "Name": "BINDID", "Memo": "BINDTYPE=0时是人员ID，BINDTYPE=1时是岗位ID，BINDTYPE=2时是工区ID，BINDTYPE=3时是专业ID", "OrderNo": 5, "DisplayName": "绑定ID", "DataType": 1, "KeyFieldType": 3, "RelateTable": "JOBLIST_EXTPROPERTIESCONFIG", "RelateField": "ID", "DataLength": 100, "GraphDesc": "P1=1018.00,253.06\r\nP2=1018.00,308.00\r\nP3=1018.00,308.00\r\nP4=1018.00,363.18\r\nHookP1=175.16,145.94\r\nHookP2=83.17,35.82\r\nMod_OP1=0\r\nMod_OP2=0\r\nMod_CP=0\r\nHorz1=0\r\nHorz2=0"}, {"ID": 15, "Name": "TMUSED", "Memo": "1 使用，0 已删除，默认使用", "OrderNo": 6, "DisplayName": "使用标识", "DataType": 2, "RelateTable": "任务督办"}]}}, {"ID": 6, "Name": "JOBLIST_CLASS", "Caption": "作业分类", "CreateDate": "2024/6/5 13:34:35", "OrderNo": 10, "GraphDesc": "Left=443.93\r\nTop=88.34", "MetaFields": {"Count": 8, "items": [{"ID": 1, "Name": "ID", "Memo": "唯一键", "OrderNo": 1, "DisplayName": "编号", "DataType": 1, "KeyFieldType": 1, "RelateTable": "任务督办", "DataLength": 100}, {"ID": 3, "Name": "TYPEID", "Memo": "作业单元的类型ID，分类是管理还是操作的。与作业单元的typeid对应", "OrderNo": 2, "DisplayName": "类型ID", "DataType": 1, "RelateTable": "任务督办", "DataLength": 100}, {"ID": 2, "Name": "PID", "Memo": "父分类ID，根分类ID是root", "OrderNo": 3, "DisplayName": "父分类ID", "DataType": 1, "RelateTable": "任务督办", "DataLength": 100}, {"ID": 12, "Name": "ORGID", "Memo": "作业单元所属的机构ID", "OrderNo": 4, "DisplayName": "机构ID", "DataType": 1, "RelateTable": "任务督办", "DataLength": 100}, {"ID": 4, "Name": "CNAME", "OrderNo": 5, "DisplayName": "名称", "DataType": 1, "RelateTable": "任务督办", "DataLength": 200}, {"ID": 5, "Name": "MEMO", "Memo": "关键活动", "OrderNo": 6, "DisplayName": "描述", "DataType": 1, "RelateTable": "任务督办", "DataLength": 2000}, {"ID": 13, "Name": "TMUSED", "Memo": "1 使用，0 已删除，默认使用", "OrderNo": 7, "DisplayName": "使用标识", "DataType": 2, "RelateTable": "任务督办"}, {"ID": 11, "Name": "TMSORT", "OrderNo": 8, "DisplayName": "排序", "DataType": 2, "RelateTable": "任务督办"}]}}, {"ID": 9, "Name": "JOBLIST_EXTPROPERTIESCONFIG", "Caption": "专业表", "CreateDate": "2024/6/6 9:05:30", "OrderNo": 11, "GraphDesc": "Left=842.84\r\nTop=87.06", "MetaFields": {"Count": 9, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DisplayName": "专业ID", "DataType": 1, "KeyFieldType": 1, "RelateTable": "任务督办", "DataLength": 100}, {"ID": 2, "Name": "PID", "Memo": "根是root", "OrderNo": 2, "DisplayName": "父专业ID", "DataType": 1, "RelateTable": "任务督办", "DataLength": 100}, {"ID": 3, "Name": "ORGID", "Memo": "专业所属的机构ID，system是租户内通用的默认分类 ，如果机构有自己的专业就用自己的", "OrderNo": 3, "DisplayName": "机构ID", "DataType": 1, "RelateTable": "任务督办", "DataLength": 100}, {"ID": 8, "Name": "PTYPE", "Memo": "0 专业，1 优先级，2频次，3 规程", "OrderNo": 4, "DisplayName": "类型", "DataType": 2, "RelateTable": "任务督办"}, {"ID": 4, "Name": "PNAME", "OrderNo": 5, "DisplayName": "名称", "DataType": 1, "RelateTable": "任务督办", "DataLength": 200}, {"ID": 5, "Name": "MEMO", "OrderNo": 6, "DisplayName": "描述", "DataType": 1, "RelateTable": "任务督办", "DataLength": 2000}, {"ID": 9, "Name": "FREQUENCYTYPE", "Memo": "0 单次 1 重复", "OrderNo": 7, "DisplayName": "频次类型", "DataType": 2, "RelateTable": "任务督办"}, {"ID": 13, "Name": "TMUSED", "Memo": "1 使用，0 已删除，默认使用", "OrderNo": 8, "DisplayName": "使用标识", "DataType": 2, "RelateTable": "任务督办"}, {"ID": 11, "Name": "TMSORT", "OrderNo": 9, "DisplayName": "排序", "DataType": 2, "RelateTable": "任务督办"}]}}, {"ID": 12, "Name": "JOBLIST_ACTIVITYPROPERTIES", "Caption": "活动属性", "CreateDate": "2024/6/6 15:05:43", "OrderNo": 12, "GraphDesc": "Left=460.66\r\nTop=552.83\r\nWidth=328.00\r\nHeight=530.00\r\nAutoSize=0", "MetaFields": {"Count": 32, "items": [{"ID": 1, "Name": "ID", "Memo": "这个ID也是核算对象的ID", "OrderNo": 1, "DisplayName": "活动ID", "DataType": 1, "RelateTable": "任务督办", "DataLength": 100, "GraphDesc": "P1=609.52,744.00\r\nP2=524.00,744.00\r\nP3=524.00,744.00\r\nP4=438.66,744.00\r\nHookP1=20.48,146.94\r\nHookP2=261.34,168.17\r\nMod_OP1=0\r\nMod_OP2=0\r\nMod_CP=0\r\nHorz1=1\r\nHorz2=1"}, {"ID": 3, "Name": "ORGID", "Memo": "作业活动所属的机构ID", "OrderNo": 2, "DisplayName": "机构ID", "DataType": 1, "RelateTable": "任务督办", "DataLength": 100}, {"ID": 14, "Name": "ACTIVITYTYPE", "Memo": "目前仅有固定工作和日周作业，来源于数据字典", "OrderNo": 3, "DisplayName": "活动类型", "DataType": 1, "RelateTable": "任务督办", "DataLength": 100}, {"ID": 2, "Name": "CLASSID", "Memo": "活动所属分类的ID", "OrderNo": 4, "DisplayName": "分类ID", "DataType": 1, "KeyFieldType": 3, "RelateTable": "JOBLIST_CLASS", "RelateField": "ID", "DataLength": 100, "GraphDesc": "P1=504.00,238.34\r\nP2=504.00,524.00\r\nP3=600.66,524.00\r\nP4=600.66,552.83\r\nHookP1=60.07,129.66\r\nHookP2=140.00,235.00\r\nMod_OP1=0\r\nMod_OP2=1\r\nMod_CP=0\r\nHorz1=0\r\nHorz2=0"}, {"ID": 12, "Name": "STATUSID", "OrderNo": 5, "DisplayName": "工况ID", "DataType": 1, "RelateTable": "任务督办", "DataLength": 100}, {"ID": 4, "Name": "PRIORITYID", "OrderNo": 6, "DisplayName": "优先级ID", "DataType": 1, "RelateTable": "任务督办", "DataLength": 100}, {"ID": 5, "Name": "RESPONSIBILITYTYPE", "Memo": "0 班组，1 岗位，默认班组", "OrderNo": 7, "DisplayName": "责任对象类型", "DataType": 2, "RelateTable": "任务督办"}, {"ID": 6, "Name": "AVAILABLECOUNT", "Memo": "默认是0", "OrderNo": 8, "DisplayName": "领取次数", "DataType": 2, "RelateTable": "任务督办"}, {"ID": 19, "Name": "FREQUENCYID", "Memo": "绑定的频次ID", "OrderNo": 9, "DisplayName": "频次ID", "DataType": 1, "KeyFieldType": 3, "RelateTable": "CYCLE_SCHEME", "RelateField": "ID", "DataLength": 100, "GraphDesc": "P1=398.00,1013.00\r\nP2=429.00,1013.00\r\nP3=429.00,1013.00\r\nP4=460.66,1013.00\r\nHookP1=305.00,70.00\r\nHookP2=44.34,460.17\r\nMod_OP1=0\r\nMod_OP2=0\r\nMod_CP=0\r\nHorz1=1\r\nHorz2=1"}, {"ID": 7, "Name": "ISFIXTIME", "Memo": "0 不是固定时间，1 是固定时间，默认是时间", "OrderNo": 10, "DisplayName": "固定时间", "DataType": 2, "RelateTable": "任务督办"}, {"ID": 8, "Name": "FIXTIME", "Memo": "文本形式的时间，格式是hh:mm:ss", "OrderNo": 11, "DisplayName": "固定时间点", "DataType": 1, "RelateTable": "任务督办", "DataLength": 10}, {"ID": 9, "Name": "RELATIVETYPE", "Memo": "0 上班之前 1 上班之后 2下班之前 3 下班之后，默认是1", "OrderNo": 12, "DisplayName": "相对时间类型", "DataType": 2, "RelateTable": "任务督办"}, {"ID": 10, "Name": "RELATIVETIME", "Memo": "单位是分钟，默认是0", "OrderNo": 13, "DisplayName": "相对时间", "DataType": 2, "RelateTable": "任务督办"}, {"ID": 11, "Name": "BEGINDATE", "Memo": "文本形式的日期，格式是yyyy-mm-dd", "OrderNo": 14, "DisplayName": "活动开始日期", "DataType": 1, "RelateTable": "任务督办", "DataLength": 10}, {"ID": 12, "Name": "ENDDATE", "Memo": "文本形式的日期，格式是yyyy-mm-dd", "OrderNo": 15, "DisplayName": "活动结束日期", "DataType": 1, "RelateTable": "任务督办", "DataLength": 10}, {"ID": 13, "Name": "NOENDDATE", "Memo": "0 活动有结束期限，1 活动永久有效，默认是1，此时不能修改结束日期", "OrderNo": 16, "DisplayName": "无限期", "DataType": 2, "RelateTable": "任务督办"}, {"ID": 15, "Name": "JOBREQUIREMENT", "OrderNo": 17, "DisplayName": "工作要求", "DataType": 1, "RelateTable": "任务督办", "DataLength": 4000}, {"ID": 16, "Name": "CONFIRMTYPE", "Memo": "0 自动确认 1 手工确认，默认是自动确认", "OrderNo": 18, "DisplayName": "确认方式", "DataType": 2, "RelateTable": "任务督办"}, {"ID": 18, "Name": "CALCTPYE", "Memo": "0 给定分值 1给定权重，默认是给定分值", "OrderNo": 19, "DisplayName": "分值计算方式", "DataType": 2, "RelateTable": "任务督办"}, {"ID": 28, "Name": "CALCSCHEMEID", "OrderNo": 20, "DisplayName": "分解方案ID", "DataType": 1, "RelateTable": "任务督办", "DataLength": 100}, {"ID": 17, "Name": "BASICSCORE", "Memo": "先给定2位小数，给定分值时不可填写", "OrderNo": 21, "DisplayName": "基本分", "DataType": 3, "RelateTable": "任务督办"}, {"ID": 20, "Name": "STANDARDDURATION", "Memo": "标准时长，2位小数", "OrderNo": 22, "DisplayName": "标准时长", "DataType": 3, "RelateTable": "任务督办"}, {"ID": 21, "Name": "TIMEUNIT", "Memo": "0 秒 1 分 2 小时 3 天", "OrderNo": 23, "DisplayName": "标准时长单位", "DataType": 2, "RelateTable": "任务督办"}, {"ID": 22, "Name": "RECORDTYPE", "Memo": "从数据字典读取来源", "OrderNo": 24, "DisplayName": "记录来源", "DataType": 1, "RelateTable": "任务督办", "DataLength": 100}, {"ID": 23, "Name": "RECORDID", "OrderNo": 25, "DisplayName": "绑定的记录ID", "DataType": 1, "RelateTable": "任务督办", "DataLength": 100}, {"ID": 25, "Name": "STARTINGCONDITIONID", "OrderNo": 26, "DisplayName": "开始条件ID", "DataType": 1, "RelateTable": "任务督办", "DataLength": 100}, {"ID": 24, "Name": "ENDCONDITIONID", "OrderNo": 27, "DisplayName": "结束条件ID", "DataType": 1, "RelateTable": "任务督办", "DataLength": 100}, {"ID": 26, "Name": "PREPLANID", "OrderNo": 28, "DisplayName": "预案ID", "DataType": 1, "RelateTable": "任务督办", "DataLength": 100}, {"ID": 27, "Name": "REGULATIONID", "OrderNo": 29, "DisplayName": "操作规程ID", "DataType": 1, "RelateTable": "任务督办", "DataLength": 100}, {"ID": 12, "Name": "TMUSED", "Memo": "1=可用，0=无效", "OrderNo": 30, "DisplayName": "可用标识", "DataType": 2, "RelateTable": "任务督办"}, {"ID": 11, "Name": "TMSORT", "OrderNo": 31, "DisplayName": "排序", "DataType": 2, "RelateTable": "任务督办"}, {"ID": 32, "Name": "UNITID", "OrderNo": 32, "DisplayName": "作业单元ID", "DataType": 1, "DataLength": 100}]}}, {"ID": 8, "Name": "TMTASK_INFO_MAIN", "Caption": "督办信息主表", "Memo": "督办基础信息", "OrderNo": 13, "GraphDesc": "Left=89.64\r\nTop=1261.98", "MetaFields": {"Count": 71, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DisplayName": "编号", "DataType": 1, "KeyFieldType": 1, "RelateTable": "JOBLIST_INPUT_DATA", "RelateField": "JOB_ID", "DataLength": 50, "GraphDesc": "P1=1562.35,1191.78\r\nP2=724.00,1191.78\r\nP3=724.00,1290.00\r\nP4=622.64,1290.00\r\nHookP1=124.00,88.82\r\nHookP2=505.36,28.02\r\nMod_OP1=1\r\nMod_OP2=0\r\nMod_CP=0\r\nHorz1=1\r\nHorz2=1"}, {"ID": 49, "Name": "FLOW_ID", "OrderNo": 2, "DisplayName": "流程标识", "DataType": 1, "RelateTable": "任务督办", "DataLength": 50}, {"ID": 12, "Name": "FLOW_VERSION", "OrderNo": 3, "DisplayName": "流程版本ID", "DataType": 1, "RelateTable": "任务督办", "DataLength": 50}, {"ID": 50, "Name": "STEP_NUM", "OrderNo": 4, "DisplayName": "步骤数", "DataType": 2, "RelateTable": "任务督办"}, {"ID": 51, "Name": "STEP_CURR", "OrderNo": 5, "DisplayName": "步骤当前位置", "DataType": 2, "RelateTable": "任务督办"}, {"ID": 14, "Name": "TEMPLATE_MARK", "OrderNo": 6, "DisplayName": "是否模板", "DataType": 2, "RelateTable": "任务督办"}, {"ID": 52, "Name": "TEMPLATE_ID", "OrderNo": 7, "DisplayName": "模板id", "DataType": 1, "RelateTable": "任务督办", "DataLength": 500}, {"ID": 53, "Name": "PARENT_ID", "OrderNo": 8, "DisplayName": "父id(分解来源)", "DataType": 1, "RelateTable": "任务督办", "DataLength": 50}, {"ID": 51, "Name": "EID", "OrderNo": 9, "DisplayName": "延期数据ID", "DataType": 1, "RelateTable": "任务督办", "DataLength": 50}, {"ID": 45, "Name": "NODE_MARK", "OrderNo": 10, "DisplayName": "是否阶段反馈任务1是0否", "DataType": 2, "RelateTable": "任务督办"}, {"ID": 47, "Name": "STEP_INFO", "OrderNo": 11, "DisplayName": "所有步骤信息字符串", "DataType": 1, "RelateTable": "任务督办", "DataLength": 2000}, {"ID": 48, "Name": "APPLY_MARK", "OrderNo": 12, "DisplayName": "应用状态标识0无1应用中", "DataType": 2, "RelateTable": "任务督办"}, {"ID": 16, "Name": "TITLE", "OrderNo": 13, "DisplayName": "标题", "DataType": 1, "RelateTable": "任务督办", "DataLength": 1000}, {"ID": 17, "Name": "CONTENT", "OrderNo": 14, "DisplayName": "内容", "DataType": 1, "RelateTable": "任务督办"}, {"ID": 18, "Name": "COMBO1", "OrderNo": 15, "DisplayName": "任务类型", "DataType": 1, "RelateTable": "任务督办", "DataLength": 50}, {"ID": 13, "Name": "COMBO2", "OrderNo": 16, "DisplayName": "任务级别", "DataType": 1, "RelateTable": "任务督办", "DataLength": 50}, {"ID": 14, "Name": "COMBO3", "OrderNo": 17, "DisplayName": "重要程度", "DataType": 1, "RelateTable": "任务督办", "DataLength": 50}, {"ID": 15, "Name": "COMBO4", "OrderNo": 18, "DisplayName": "任务出处", "DataType": 1, "RelateTable": "任务督办", "DataLength": 50}, {"ID": 49, "Name": "COMBO5", "OrderNo": 19, "DisplayName": "选择1", "DataType": 1, "RelateTable": "任务督办", "DataLength": 50}, {"ID": 50, "Name": "COMBO6", "OrderNo": 20, "DisplayName": "选择2", "DataType": 1, "RelateTable": "任务督办", "DataLength": 50}, {"ID": 16, "Name": "CTIME", "OrderNo": 21, "DisplayName": "创建时间", "DataType": 4, "RelateTable": "任务督办"}, {"ID": 17, "Name": "PUBLISH_TIME", "OrderNo": 22, "DisplayName": "发布时间", "DataType": 4, "RelateTable": "任务督办"}, {"ID": 18, "Name": "START_TIME", "OrderNo": 23, "DisplayName": "开始时间", "DataType": 4, "RelateTable": "任务督办"}, {"ID": 19, "Name": "END_TIME", "OrderNo": 24, "DisplayName": "截止时间", "DataType": 4, "RelateTable": "任务督办"}, {"ID": 52, "Name": "RESOLVE_END_TIME", "OrderNo": 25, "DisplayName": "分解截至日期", "DataType": 4, "RelateTable": "任务督办"}, {"ID": 46, "Name": "PERIOD_MARK", "OrderNo": 26, "DisplayName": "周期类型1日2周3旬4月5季6半年7年0自定义", "DataType": 2, "RelateTable": "任务督办"}, {"ID": 44, "Name": "TASK_PERIOD", "OrderNo": 27, "DisplayName": "任务周期", "DataType": 1, "RelateTable": "任务督办", "DataLength": 300}, {"ID": 36, "Name": "COMPLETE_MARK", "OrderNo": 28, "DisplayName": "完成标识2完成1未完成", "DataType": 2, "RelateTable": "任务督办"}, {"ID": 47, "Name": "END_MARK", "OrderNo": 29, "DisplayName": "任务结束标识0未结束1销项2结束", "DataType": 2, "RelateTable": "任务督办"}, {"ID": 37, "Name": "OVERTIME_MARK", "OrderNo": 30, "DisplayName": "超期标识", "DataType": 2, "RelateTable": "任务督办"}, {"ID": 38, "Name": "ASSESS_TYPE", "OrderNo": 31, "DisplayName": "考核类型1分2奖", "DataType": 2, "RelateTable": "任务督办"}, {"ID": 46, "Name": "ASSESS_MODE", "OrderNo": 32, "DisplayName": "考核模式1单2共同", "DataType": 2, "RelateTable": "任务督办"}, {"ID": 39, "Name": "BASIC_ASSESS_VAL", "OrderNo": 33, "DisplayName": "基础考核值", "DataType": 3, "RelateTable": "任务督办"}, {"ID": 40, "Name": "ASSESS_VAL", "OrderNo": 34, "DisplayName": "考核值", "DataType": 3, "RelateTable": "任务督办"}, {"ID": 42, "Name": "OVERTIME_VAL", "OrderNo": 35, "DisplayName": "超期考核值", "DataType": 3, "RelateTable": "任务督办"}, {"ID": 41, "Name": "EVALUATE_VAL", "OrderNo": 36, "DisplayName": "评价值（平均）", "DataType": 3, "RelateTable": "任务督办"}, {"ID": 43, "Name": "CREATE_ORG_CODE", "OrderNo": 37, "DisplayName": "创建机构", "DataType": 1, "RelateTable": "任务督办", "DataLength": 50}, {"ID": 44, "Name": "CREATE_JOB_CODE", "OrderNo": 38, "DisplayName": "创建岗位", "DataType": 1, "RelateTable": "任务督办", "DataLength": 50}, {"ID": 45, "Name": "CREATE_USER_CODE", "OrderNo": 39, "DisplayName": "创建人员", "DataType": 1, "RelateTable": "任务督办", "DataLength": 50}, {"ID": 43, "Name": "CREATE_USER_NAME", "OrderNo": 40, "DisplayName": "创建人", "DataType": 1, "RelateTable": "任务督办", "DataLength": 50}, {"ID": 32, "Name": "EXT_INFO1", "OrderNo": 41, "DisplayName": "扩展内容1", "DataType": 1, "RelateTable": "任务督办", "DataLength": 1000}, {"ID": 33, "Name": "EXT_INFO2", "OrderNo": 42, "DisplayName": "扩展内容2", "DataType": 1, "RelateTable": "任务督办", "DataLength": 1000}, {"ID": 34, "Name": "EXT_INFO3", "OrderNo": 43, "DisplayName": "扩展内容3", "DataType": 1, "RelateTable": "任务督办", "DataLength": 1000}, {"ID": 35, "Name": "EXT_INFO4", "OrderNo": 44, "DisplayName": "扩展内容4", "DataType": 1, "RelateTable": "任务督办", "DataLength": 1000}, {"ID": 49, "Name": "EXT_INFO5", "OrderNo": 45, "DisplayName": "扩展内容5", "DataType": 1, "RelateTable": "任务督办", "DataLength": 1000}, {"ID": 50, "Name": "EXT_INFO6", "OrderNo": 46, "DisplayName": "扩展内容6", "DataType": 1, "RelateTable": "任务督办", "DataLength": 1000}, {"ID": 51, "Name": "EXT_INFO7", "OrderNo": 47, "DisplayName": "扩展内容7", "DataType": 1, "RelateTable": "任务督办", "DataLength": 1000}, {"ID": 52, "Name": "EXT_INFO8", "OrderNo": 48, "DisplayName": "扩展内容8", "DataType": 1, "RelateTable": "任务督办", "DataLength": 1000}, {"ID": 53, "Name": "EXT_INFO9", "OrderNo": 49, "DisplayName": "扩展内容9", "DataType": 1, "RelateTable": "任务督办", "DataLength": 1000}, {"ID": 56, "Name": "EXT_INFO10", "OrderNo": 50, "DisplayName": "扩展内容10", "DataType": 1, "RelateTable": "任务督办", "DataLength": 1000}, {"ID": 19, "Name": "TMSORT", "OrderNo": 51, "DisplayName": "排序号", "DataType": 2, "RelateTable": "任务督办"}, {"ID": 20, "Name": "TMUSED", "OrderNo": 52, "DisplayName": "是否使用", "DataType": 2, "RelateTable": "任务督办"}, {"ID": 53, "Name": "ACTUAL_END_TIME", "OrderNo": 53, "DisplayName": "任务结束时间", "DataType": 4, "RelateTable": "任务督办"}, {"ID": 54, "Name": "OVER_DAYS", "OrderNo": 54, "DisplayName": "超期天数，默认0", "DataType": 2, "RelateTable": "任务督办"}, {"ID": 55, "Name": "STEP_PUBLISH_MARK", "OrderNo": 55, "DisplayName": "发布标识，1待发布2已发布", "DataType": 2, "RelateTable": "任务督办"}, {"ID": 56, "Name": "STEP_PUBLISH_USER_CODE", "OrderNo": 56, "DisplayName": "发布人代码", "DataType": 1, "RelateTable": "任务督办", "DataLength": 50}, {"ID": 57, "Name": "STEP_PUBLISH_TIME", "OrderNo": 57, "DisplayName": "发布时间", "DataType": 4, "RelateTable": "任务督办"}, {"ID": 58, "Name": "THEME_ID", "OrderNo": 58, "DisplayName": "关联主题", "DataType": 1, "RelateTable": "任务督办", "DataLength": 50}, {"ID": 60, "Name": "PERIOD_TYPE", "OrderNo": 59, "DisplayName": "周期类型1日2周3月4季度5半年6年", "DataType": 2, "RelateTable": "任务督办"}, {"ID": 61, "Name": "PERIOD_START", "OrderNo": 60, "DisplayName": "周期开始", "DataType": 1, "RelateTable": "任务督办", "DataLength": 200}, {"ID": 59, "Name": "PERIOD_END", "OrderNo": 61, "DisplayName": "周期截止", "DataType": 1, "RelateTable": "任务督办", "DataLength": 200}, {"ID": 63, "Name": "LIMIT_MARK", "OrderNo": 62, "DisplayName": "有效标识1永久0自定义", "DataType": 2, "RelateTable": "任务督办"}, {"ID": 64, "Name": "LIMIT_START_DAY", "OrderNo": 63, "DisplayName": "有效开始日期", "DataType": 4, "RelateTable": "任务督办"}, {"ID": 65, "Name": "LIMIT_END_DAY", "OrderNo": 64, "DisplayName": "有效截至日期", "DataType": 4, "RelateTable": "任务督办"}, {"ID": 62, "Name": "PERIOD_USED", "OrderNo": 65, "DisplayName": "周期启用0未启用1启用", "DataType": 2, "RelateTable": "任务督办"}, {"ID": 66, "Name": "PERIOD_PUBLISH_TIME", "OrderNo": 66, "DisplayName": "周期任务最后发布时间", "DataType": 4, "RelateTable": "任务督办"}, {"ID": 67, "Name": "PERIOD_UPDATE_TIME", "OrderNo": 67, "DisplayName": "周期任务最后修改时间", "DataType": 4, "RelateTable": "任务督办"}, {"ID": 69, "Name": "INDEX_ID", "OrderNo": 68, "DisplayName": "关联指标ID", "DataType": 1, "RelateTable": "任务督办", "DataLength": 50}, {"ID": 68, "Name": "INDEX_NAME", "OrderNo": 69, "DisplayName": "关联指标名称", "DataType": 1, "RelateTable": "任务督办", "DataLength": 200}, {"ID": 70, "Name": "HAVE_FEED_WEEK", "OrderNo": 70, "DisplayName": "是否有周反馈", "DataType": 2, "RelateTable": "任务督办"}, {"ID": 71, "Name": "ORG_ACC_MARK", "OrderNo": 71, "DisplayName": "机构任务接收标识1任务下达到机构", "DataType": 2, "RelateTable": "任务督办"}]}}, {"ID": 10, "Name": "TMTASK_INFO_DEGREE", "Caption": "督办信息处理表", "Memo": "审核、审批、反馈、确认、评价等", "OrderNo": 14, "GraphDesc": "Left=764.58\r\nTop=1264.11", "MetaFields": {"Count": 56, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DisplayName": "编号", "DataType": 1, "KeyFieldType": 1, "RelateTable": "JOBLIST_INPUT_MAPPING", "RelateField": "SLAVE_ID", "DataLength": 50, "GraphDesc": "P1=2056.00,1224.66\r\nP2=2056.00,1508.43\r\nP3=2056.00,1508.43\r\nP4=1252.58,1508.43\r\nHookP1=44.39,59.34\r\nHookP2=244.00,244.32\r\nMod_OP1=1\r\nMod_OP2=1\r\nMod_CP=1\r\nHorz1=0\r\nHorz2=1"}, {"ID": 36, "Name": "FLOW_ID", "OrderNo": 2, "DisplayName": "流程标识", "DataType": 1, "RelateTable": "任务督办", "DataLength": 50}, {"ID": 34, "Name": "FLOW_VERSION", "OrderNo": 3, "DisplayName": "流程版本标识", "DataType": 1, "RelateTable": "任务督办", "DataLength": 50}, {"ID": 37, "Name": "STEP_ID", "OrderNo": 4, "DisplayName": "步骤标识", "DataType": 1, "RelateTable": "任务督办", "DataLength": 50}, {"ID": 35, "Name": "STEP_VERSION", "OrderNo": 5, "DisplayName": "步骤版本标识", "DataType": 1, "RelateTable": "任务督办", "DataLength": 50}, {"ID": 14, "Name": "STEP_TYPE", "OrderNo": 6, "DisplayName": "步骤类型", "DataType": 2, "RelateTable": "任务督办"}, {"ID": 26, "Name": "STEP_NO", "OrderNo": 7, "DisplayName": "步骤序号", "DataType": 2, "RelateTable": "任务督办"}, {"ID": 41, "Name": "STEP_NAME", "OrderNo": 8, "DisplayName": "步骤名称", "DataType": 1, "RelateTable": "任务督办", "DataLength": 200}, {"ID": 38, "Name": "INFO_ID", "OrderNo": 9, "DisplayName": "主信息标识", "DataType": 1, "KeyFieldType": 3, "RelateTable": "TMTASK_INFO_MAIN", "RelateField": "ID", "DataLength": 50, "GraphDesc": "P1=622.64,1547.38\r\nP2=694.00,1547.38\r\nP3=694.00,1674.00\r\nP4=764.58,1674.00\r\nHookP1=288.80,285.40\r\nHookP2=28.42,409.89\r\nMod_OP1=1\r\nMod_OP2=0\r\nMod_CP=0\r\nHorz1=1\r\nHorz2=1"}, {"ID": 44, "Name": "HAVE_EXT", "OrderNo": 10, "DisplayName": "是否进行过延期0无1有", "DataType": 2, "RelateTable": "任务督办"}, {"ID": 51, "Name": "EXT_NUM", "OrderNo": 11, "DisplayName": "延期申请次数，默认0", "DataType": 2, "RelateTable": "任务督办"}, {"ID": 46, "Name": "OVER_EXT_MARK", "OrderNo": 12, "DisplayName": "延期超期标识0无1是", "DataType": 2, "RelateTable": "任务督办"}, {"ID": 47, "Name": "OVER_EXT_VAL", "OrderNo": 13, "DisplayName": "延期超期考核值", "DataType": 3, "RelateTable": "任务督办"}, {"ID": 45, "Name": "HAVE_RESOLVE", "OrderNo": 14, "DisplayName": "是否进行过分解0无1有", "DataType": 1, "RelateTable": "任务督办"}, {"ID": 43, "Name": "HAVE_CALLBACK", "OrderNo": 15, "DisplayName": "是否分解回调0无1有", "DataType": 2, "RelateTable": "任务督办"}, {"ID": 48, "Name": "SHOW_STATUS", "OrderNo": 16, "DisplayName": "是否显示处理状态1显示0不显示", "DataType": 2, "RelateTable": "任务督办"}, {"ID": 40, "Name": "DEGREE_MODE", "OrderNo": 17, "DisplayName": "处理方式1需要处理0不需要处理", "DataType": 2, "RelateTable": "任务督办"}, {"ID": 15, "Name": "DEGREE_STATUS", "OrderNo": 18, "DisplayName": "处理标识状态-1被否决0未处理1通过", "DataType": 2, "RelateTable": "任务督办"}, {"ID": 21, "Name": "DEGREE_ID", "OrderNo": 19, "DisplayName": "处理标识", "DataType": 1, "RelateTable": "任务督办", "DataLength": 50}, {"ID": 16, "Name": "DEGREE_NAME", "OrderNo": 20, "DisplayName": "处理状态名称", "DataType": 1, "RelateTable": "任务督办", "DataLength": 100}, {"ID": 31, "Name": "DEGREE_TYPE", "OrderNo": 21, "DisplayName": "处理类型1人3机构", "DataType": 2, "RelateTable": "任务督办"}, {"ID": 28, "Name": "DEGREE_MARK", "OrderNo": 22, "DisplayName": "处理标识1主办2协办3协助", "DataType": 2, "RelateTable": "任务督办"}, {"ID": 17, "Name": "DEGREE_ORG_CODE", "OrderNo": 23, "DisplayName": "机构代码", "DataType": 1, "RelateTable": "任务督办", "DataLength": 50}, {"ID": 18, "Name": "DEGREE_ORG_NAME", "OrderNo": 24, "DisplayName": "机构名称", "DataType": 1, "RelateTable": "任务督办", "DataLength": 100}, {"ID": 22, "Name": "DEGREE_JOB_CODE", "OrderNo": 25, "DisplayName": "岗位代码", "DataType": 1, "RelateTable": "任务督办", "DataLength": 50}, {"ID": 23, "Name": "DEGREE_JOB_NAME", "OrderNo": 26, "DisplayName": "岗位名称", "DataType": 1, "RelateTable": "任务督办", "DataLength": 100}, {"ID": 24, "Name": "DEGREE_USER_CODE", "OrderNo": 27, "DisplayName": "人员代码", "DataType": 1, "RelateTable": "任务督办", "DataLength": 50}, {"ID": 25, "Name": "DEGREE_USER_NAME", "OrderNo": 28, "DisplayName": "人员名称", "DataType": 1, "RelateTable": "任务督办", "DataLength": 100}, {"ID": 26, "Name": "DEGREE_TIME", "OrderNo": 29, "DisplayName": "最后处理时间", "DataType": 4, "RelateTable": "任务督办"}, {"ID": 27, "Name": "DEGREE_INFO", "OrderNo": 30, "DisplayName": "处理说明（文本编辑器）", "DataType": 1, "RelateTable": "任务督办", "DataLength": 8000}, {"ID": 28, "Name": "DEGREE_CONTENT", "OrderNo": 31, "DisplayName": "处理说明（文本域）", "DataType": 1, "RelateTable": "任务督办", "DataLength": 8000}, {"ID": 32, "Name": "EVALUATE_SELECT", "OrderNo": 32, "DisplayName": "评价选择", "DataType": 1, "RelateTable": "任务督办", "DataLength": 50}, {"ID": 35, "Name": "EVALUATE_SELECT_NAME", "OrderNo": 33, "DisplayName": "评价选择名称", "DataType": 1, "RelateTable": "任务督办", "DataLength": 100}, {"ID": 33, "Name": "EVALUATE_SCORE", "OrderNo": 34, "DisplayName": "评价分", "DataType": 3, "RelateTable": "任务督办"}, {"ID": 34, "Name": "EVALUATE_BONUS", "OrderNo": 35, "DisplayName": "评价奖", "DataType": 3, "RelateTable": "任务督办"}, {"ID": 29, "Name": "OVER_MARK", "OrderNo": 36, "DisplayName": "超期标识", "DataType": 2, "RelateTable": "任务督办"}, {"ID": 32, "Name": "ASSESS_MODE", "OrderNo": 37, "DisplayName": "考核模式1分2奖", "DataType": 2, "RelateTable": "任务督办"}, {"ID": 33, "Name": "ASSESS_VAL", "OrderNo": 38, "DisplayName": "工作考核值", "DataType": 3, "RelateTable": "任务督办"}, {"ID": 42, "Name": "OVER_CATCH_DAYS", "OrderNo": 39, "DisplayName": "超期反馈延期天数", "DataType": 2, "RelateTable": "任务督办"}, {"ID": 52, "Name": "OVER_FEEDBACK_MARK", "OrderNo": 40, "DisplayName": "超期反馈标识", "DataType": 2, "RelateTable": "任务督办"}, {"ID": 34, "Name": "OVERTIME_VAL", "OrderNo": 41, "DisplayName": "超期反馈考核值", "DataType": 3, "RelateTable": "任务督办"}, {"ID": 49, "Name": "OVER_RESOLVE_MARK", "OrderNo": 42, "DisplayName": "超期分解标识1超0无", "DataType": 2, "RelateTable": "任务督办"}, {"ID": 50, "Name": "OVER_RESOLVE_VAL", "OrderNo": 43, "DisplayName": "超期分解考核值", "DataType": 3, "RelateTable": "任务督办"}, {"ID": 35, "Name": "EVALUATE_VAL", "OrderNo": 44, "DisplayName": "评价值（平均）", "DataType": 3, "RelateTable": "任务督办"}, {"ID": 29, "Name": "EXT_INFO1", "OrderNo": 45, "DisplayName": "备用说明1", "DataType": 1, "RelateTable": "任务督办", "DataLength": 2000}, {"ID": 30, "Name": "EXT_INFO2", "OrderNo": 46, "DisplayName": "备用说明2", "DataType": 1, "RelateTable": "任务督办", "DataLength": 2000}, {"ID": 36, "Name": "EXT_INFO3", "OrderNo": 47, "DisplayName": "备用说明3", "DataType": 1, "RelateTable": "任务督办", "DataLength": 2000}, {"ID": 37, "Name": "EXT_INFO4", "OrderNo": 48, "DisplayName": "备用说明4", "DataType": 1, "RelateTable": "任务督办", "DataLength": 2000}, {"ID": 38, "Name": "EXT_INFO5", "OrderNo": 49, "DisplayName": "备用说明5", "DataType": 1, "RelateTable": "任务督办", "DataLength": 2000}, {"ID": 39, "Name": "EXT_INFO6", "OrderNo": 50, "DisplayName": "备用说明6", "DataType": 1, "RelateTable": "任务督办", "DataLength": 2000}, {"ID": 53, "Name": "FEEDBACK_ASSESS_ID", "OrderNo": 51, "DisplayName": "超期反馈考核库ID", "DataType": 1, "RelateTable": "任务督办", "DataLength": 50}, {"ID": 55, "Name": "RESOLVE_ASSESS_ID", "OrderNo": 52, "DisplayName": "超期分解考核库ID", "DataType": 1, "RelateTable": "任务督办", "DataLength": 50}, {"ID": 54, "Name": "ASSESS_ID", "OrderNo": 53, "DisplayName": "考核库ID", "DataType": 1, "RelateTable": "任务督办", "DataLength": 50}, {"ID": 19, "Name": "TMSORT", "OrderNo": 54, "DisplayName": "排序号", "DataType": 2, "RelateTable": "任务督办"}, {"ID": 20, "Name": "TMUSED", "OrderNo": 55, "DisplayName": "是否使用", "DataType": 2, "RelateTable": "任务督办"}, {"ID": 56, "Name": "DEGREE_ACC_MARK", "OrderNo": 56, "DisplayName": "只下达到机构标识1是", "DataType": 2, "RelateTable": "任务督办"}]}}, {"ID": 15, "Name": "任务督办", "TypeName": "GROUP", "Memo": "任务督办", "CreateDate": "2024/6/27 8:14:38", "OrderNo": 15, "GraphDesc": "Left=32.13\r\nTop=1217.68\r\nWidth=1261.59\r\nHeight=901.02\r\nAutoSize=0\r\nBWidth=0.00\r\nBHeight=0.00", "BgColor": 16777215, "MetaFields": {"items": []}}, {"ID": 13, "Name": "CYCLE_SCHEME", "Caption": "周期方案", "CreateDate": "2024/6/24 9:49:39", "OrderNo": 16, "GraphDesc": "Left=73.00\r\nTop=943.00", "MetaFields": {"Count": 9, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DisplayName": "编号", "DataType": 1, "KeyFieldType": 1, "DataLength": 100}, {"ID": 13, "Name": "ORGID", "Memo": "方案的机构ID，system为系统内置方案，所有机构可见", "OrderNo": 2, "DisplayName": "机构ID", "DataType": 1, "DataLength": 100}, {"ID": 12, "Name": "SNAME", "OrderNo": 3, "DisplayName": "方案名称", "DataType": 1, "DataLength": 100}, {"ID": 14, "Name": "MEMO", "OrderNo": 4, "DisplayName": "描述", "DataType": 1, "DataLength": 2000}, {"ID": 7, "Name": "ISCRON", "Memo": "0 不是，1 是", "OrderNo": 5, "DisplayName": "cyclestr是cron表达式", "DataType": 2}, {"ID": 8, "Name": "CYCLESTR", "Memo": "cron表达式或周期设置的json", "OrderNo": 6, "DisplayName": "表达式", "DataType": 1, "DataLength": 4000}, {"ID": 15, "Name": "TMUSED", "Memo": "1=可用，0=无效", "OrderNo": 7, "DisplayName": "可用标识", "DataType": 2}, {"ID": 16, "Name": "TMSORT", "OrderNo": 8, "DisplayName": "排序", "DataType": 2}, {"ID": 9, "Name": "BINDCYCLE", "Memo": "1、班；2、日；3、周；4、月；5、季；6、年", "OrderNo": 9, "DisplayName": "周期类型", "DataType": 2}]}}, {"ID": 16, "Name": "COSTUINT", "Caption": "核算对象", "CreateDate": "2024/7/4 8:33:00", "OrderNo": 17, "GraphDesc": "Left=162.00\r\nTop=555.00", "MetaFields": {"Count": 4, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DisplayName": "编号", "DataType": 1, "KeyFieldType": 1, "RelateTable": "JOBLIST_ACTIVITYPROPERTIES", "RelateField": "ID", "DataLength": 100, "GraphDesc": "P1=460.66,598.00\r\nP2=418.00,598.00\r\nP3=418.00,598.00\r\nP4=376.00,598.00\r\nHookP1=20.34,45.17\r\nHookP2=194.00,43.00\r\nMod_OP1=0\r\nMod_OP2=0\r\nMod_CP=0\r\nHorz1=1\r\nHorz2=1"}, {"ID": 12, "Name": "NAME", "OrderNo": 2, "DisplayName": "名称", "DataType": 1, "DataLength": 200}, {"ID": 13, "Name": "PID", "Memo": "对于作业活动应该是活动的PID", "OrderNo": 3, "DisplayName": "父ID", "DataType": 1, "DataLength": 100}, {"ID": 14, "Name": "PRODUCTIVE", "Memo": "1是作业活动，其余是普通核算对象", "OrderNo": 4, "DisplayName": "是活动", "DataType": 2}]}}, {"ID": 5, "Name": "常规活动反馈", "TypeName": "GROUP", "Memo": "常规活动反馈", "CreateDate": "2024/6/6 9:49:47", "OrderNo": 18, "GraphDesc": "Left=2102.00\r\nTop=1517.00\r\nWidth=366.00\r\nHeight=221.00\r\nAutoSize=0\r\nBWidth=0.00\r\nBHeight=0.00", "BgColor": 16777215, "MetaFields": {"items": []}}, {"ID": 19, "Name": "JOBLIST_GENERAL_FEEDBACK", "Caption": "常规活动反馈", "CreateDate": "2024/7/10 14:45:24", "OrderNo": 19, "GraphDesc": "Left=2148.00\r\nTop=1572.00\r\nWidth=294.00\r\nHeight=152.00\r\nAutoSize=0", "MetaFields": {"Count": 8, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DisplayName": "编号", "DataType": 1, "KeyFieldType": 1, "RelateTable": "JOBLIST_INPUT_MAPPING", "RelateField": "SLAVE_ID", "DataLength": 50, "GraphDesc": "P1=2295.00,1224.66\r\nP2=2295.00,1398.00\r\nP3=2295.00,1398.00\r\nP4=2295.00,1572.00\r\nHookP1=283.39,74.34\r\nHookP2=147.00,20.00\r\nMod_OP1=0\r\nMod_OP2=0\r\nMod_CP=0\r\nHorz1=0\r\nHorz2=0"}, {"ID": 15, "Name": "JOB_ID", "OrderNo": 2, "DisplayName": "作业活动ID", "DataType": 1, "DataLength": 50, "GraphDesc": "P1=1248.52,800.00\r\nP2=1522.00,800.00\r\nP3=1522.00,1600.00\r\nP4=2148.00,1600.00\r\nHookP1=305.48,233.94\r\nHookP2=28.00,28.00\r\nMod_OP1=0\r\nMod_OP2=0\r\nMod_CP=0\r\nHorz1=1\r\nHorz2=1"}, {"ID": 4, "Name": "INPUT_TIME", "OrderNo": 3, "DisplayName": "反馈时间", "DataType": 4, "RelateTable": "任务督办"}, {"ID": 6, "Name": "INPUT_PERSON_ID", "OrderNo": 4, "DisplayName": "反馈人ID", "DataType": 1, "RelateTable": "任务督办", "DataLength": 50}, {"ID": 7, "Name": "INPUT_PERSON_NAME", "OrderNo": 5, "DisplayName": "反馈人", "DataType": 1, "RelateTable": "任务督办", "DataLength": 200}, {"ID": 7, "Name": "ACTIVITY_STATUS", "Memo": "0=未开始，1=进行中，2=已完成", "OrderNo": 6, "DisplayName": "活动完成状态", "DataType": 2}, {"ID": 8, "Name": "FEEDBACK_CONTENT", "OrderNo": 7, "DisplayName": "反馈内容", "DataType": 1, "DataLength": 4000}, {"ID": 12, "Name": "TMUSED", "Memo": "1=可用，0=无效", "OrderNo": 8, "DisplayName": "可用标识", "DataType": 2, "RelateTable": "任务督办"}]}}, {"ID": 20, "Name": "ACCTOBJ_INPUT_BATCH", "Caption": "核算对象录入批次表", "CreateDate": "2024/7/22 16:11:13", "OrderNo": 20, "GraphDesc": "Left=2981.00\r\nTop=1198.00", "MetaFields": {"Count": 8, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DisplayName": "编号", "DataType": 1, "KeyFieldType": 1, "DataLength": 50, "GraphDesc": "P1=2428.00,1224.66\r\nP2=2428.00,1499.00\r\nP3=2428.00,1499.00\r\nP4=3083.00,1499.00\r\nHookP1=416.39,74.34\r\nHookP2=20.00,20.00\r\nMod_OP1=0\r\nMod_OP2=0\r\nMod_CP=0\r\nHorz1=0\r\nHorz2=1"}, {"ID": 15, "Name": "BA_ID", "Memo": "BA：Business Activity", "OrderNo": 2, "DisplayName": "业务活动ID", "DataType": 1, "RelateTable": "任务督办", "DataLength": 200}, {"ID": 3, "Name": "ACCTOBJ_ID", "OrderNo": 3, "DisplayName": "核算对象ID", "DataType": 1, "RelateTable": "任务督办", "DataLength": 200}, {"ID": 3, "Name": "IPT_ID", "Memo": "ACCTOBJ_INPUT 表的 ID", "OrderNo": 4, "DisplayName": "录入主表ID", "DataType": 1, "KeyFieldType": 3, "RelateTable": "ACCTOBJ_INPUT", "RelateField": "ID", "DataLength": 50, "GraphDesc": "P1=2867.35,1268.96\r\nP2=2924.00,1268.96\r\nP3=2924.00,1273.00\r\nP4=2981.00,1273.00\r\nHookP1=156.00,147.00\r\nHookP2=20.00,75.00\r\nMod_OP1=1\r\nMod_OP2=0\r\nMod_CP=0\r\nHorz1=1\r\nHorz2=1"}, {"ID": 13, "Name": "COLLECT_POINT_ID", "Memo": "特殊采集点，不做为录入项存在", "OrderNo": 5, "DisplayName": "采集点ID（批次标题采集点）", "DataType": 1, "RelateTable": "任务督办", "DataLength": 50}, {"ID": 5, "Name": "COLLECT_POINT", "Memo": "特殊采集点，不做为录入项存在", "OrderNo": 6, "DisplayName": "采集点（批次标题采集点）", "DataType": 1, "RelateTable": "任务督办", "DataLength": 200}, {"ID": 12, "Name": "TMUSED", "Memo": "1=可用，0=无效", "OrderNo": 7, "DisplayName": "可用标识", "DataType": 2, "RelateTable": "任务督办"}, {"ID": 9, "Name": "SN", "OrderNo": 8, "DisplayName": "序号", "DataType": 2, "RelateTable": "任务督办"}]}}, {"Name": "JOBLIST_ACTIVITYEXAMPLE", "OrderNo": 21, "GraphDesc": "Left=905.63\r\nTop=518.20\r\nWidth=373.44\r\nHeight=460.92\r\nAutoSize=0", "MetaFields": {"Count": 27, "items": [{"ID": 1, "Name": "ID", "OrderNo": 1, "DisplayName": "活动实例ID", "DataType": 1, "Not_Nullable": true, "DataLength": 50}, {"ID": 19, "Name": "PID", "Memo": "当此id为空时代表当前任务是主任务，反之为子任务", "OrderNo": 2, "DisplayName": "父任务ID", "DataType": 1, "DataLength": 100}, {"ID": 9, "Name": "ACTIVITY_ID", "Memo": "活动属性表ID", "OrderNo": 3, "DisplayName": "活动配置ID", "DataType": 1, "KeyFieldType": 3, "RelateTable": "JOBLIST_ACTIVITYPROPERTIES", "RelateField": "ID", "DataLength": 100, "GraphDesc": "P1=788.66,766.00\r\nP2=847.00,766.00\r\nP3=847.00,766.00\r\nP4=905.63,766.00\r\nHookP1=300.34,213.17\r\nHookP2=20.37,247.80\r\nMod_OP1=0\r\nMod_OP2=0\r\nMod_CP=0\r\nHorz1=1\r\nHorz2=1"}, {"ID": 8, "Name": "ACTIVITY_DATE", "OrderNo": 4, "DataType": 1, "DataLength": 100}, {"ID": 10, "Name": "ACTIVITY_STATUS", "Memo": "活动状态：0=未开始，1=进行中，2=已完成，-1=未完成", "OrderNo": 5, "DisplayName": "活动实例状态", "DataType": 2}, {"ID": 11, "Name": "BEGIN_DATE", "Memo": "查询接口需要提供的时间符合此时间范围", "OrderNo": 6, "DisplayName": "活动实例的开始时间", "DataType": 1, "DataLength": 100}, {"ID": 12, "Name": "DELIVER_DATE", "OrderNo": 7, "DisplayName": "分配时间", "DataType": 1, "DataLength": 100}, {"ID": 13, "Name": "END_DATE", "Memo": "查询接口需要提供的时间符合此时间范围", "OrderNo": 8, "DisplayName": "活动实例的结束时间", "DataType": 1, "DataLength": 100}, {"ID": 14, "Name": "FINISH_DATE", "OrderNo": 9, "DisplayName": "完成时间", "DataType": 1, "DataLength": 100}, {"ID": 15, "Name": "IS_PARENT", "Memo": "1 父记录 0子记录", "OrderNo": 10, "DisplayName": "是否为父活动记录", "DataType": 2}, {"ID": 16, "Name": "MEMO", "OrderNo": 11, "DisplayName": "任务备注", "DataType": 1, "DataLength": 100}, {"ID": 17, "Name": "ORG_CODE", "OrderNo": 12, "DataType": 1, "DataLength": 100}, {"ID": 18, "Name": "P_ORG_CODE", "OrderNo": 13, "DataType": 1, "DataLength": 100}, {"ID": 20, "Name": "RECORD_ID", "OrderNo": 14, "DataType": 1, "DataLength": 100}, {"ID": 21, "Name": "RESPONSIBLE_TYPE", "OrderNo": 15, "DataType": 2}, {"ID": 22, "Name": "RESPONSIBLE_PERSON_ID", "OrderNo": 16, "DisplayName": "活动负责人id", "DataType": 1, "DataLength": 100}, {"ID": 23, "Name": "RESPONSIBLE_PERSON_NAME", "OrderNo": 17, "DisplayName": "活动负责人姓名", "DataType": 1, "DataLength": 100}, {"ID": 24, "Name": "SBSJ", "OrderNo": 18, "DataType": 1, "DataLength": 100}, {"ID": 29, "Name": "XBSJ", "OrderNo": 19, "DataType": 1, "DataLength": 100}, {"ID": 25, "Name": "SHIFT_CLASS_CODE", "OrderNo": 20, "DataType": 1, "DataLength": 100}, {"ID": 26, "Name": "STANDARD_DURATION", "Memo": "与活动属性表一致", "OrderNo": 21, "DisplayName": "标准时长", "DataType": 3}, {"ID": 30, "Name": "JOBREQUIREMENT", "OrderNo": 22, "DataType": 1, "DataLength": 4000}, {"ID": 31, "Name": "RECORDTYPE", "OrderNo": 23, "DataType": 1, "DataLength": 100}, {"ID": 32, "Name": "FREQUENCY_TYPE", "OrderNo": 24, "DataType": 2}, {"ID": 33, "Name": "JOB_NO", "OrderNo": 25, "DataType": 2}, {"ID": 28, "Name": "TMUSED", "OrderNo": 26, "DisplayName": "是否启用", "DataType": 2}, {"ID": 27, "Name": "TMSORT", "OrderNo": 27, "DisplayName": "排序", "DataType": 2}]}}]}}]}