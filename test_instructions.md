# 存储过程测试指南

## 测试步骤

### 1. 准备测试参数

在每个测试文件的开头，需要替换以下参数：

```sql
\set p_objid 'YOUR_OBJID_HERE'          -- 替换为实际的机构objid
\set p_datetime '2024-01-15 14:30:00'   -- 替换为测试时间
\set p_acctobj_id 'YOUR_ACCTOBJ_ID_HERE' -- 替换为核算对象ID（可选）
```

### 2. 按顺序执行测试

#### 测试1：基础数据检查 (test_01_check_data.sql)
```bash
psql -d your_database -f test_01_check_data.sql
```

**检查内容：**
- 机构关系表是否有对应记录
- 班次数据表是否有对应记录
- 是否能正确获取父级机构代码
- 是否能找到当前时间点的班次

**预期结果：**
- 每个查询都应该返回相关数据
- 如果某个查询返回空，说明该步骤有问题

#### 测试2：查找上个班次 (test_02_find_previous_shift.sql)
```bash
psql -d your_database -f test_02_find_previous_shift.sql
```

**检查内容：**
- 当前班次信息是否正确
- 上个班次信息是否能找到

**预期结果：**
- 应该能找到当前班次和上个班次的信息

#### 测试3：采集点数据检查 (test_03_check_collect_data.sql)
```bash
psql -d your_database -f test_03_check_collect_data.sql
```

**检查内容：**
- acctobj_input 和 acctobj_inputmx 表是否有数据
- 是否能找到匹配的采集点数据
- 最终查询是否能返回结果

#### 测试4：简化函数测试 (test_04_simple_function_test.sql)
```bash
psql -d your_database -f test_04_simple_function_test.sql
```

**检查内容：**
- 创建一个带调试信息的测试函数
- 逐步验证每个步骤的执行情况

## 常见问题排查

### 问题1：找不到机构关系
**现象：** test_01 中机构关系检查返回空
**解决：** 检查 sys_org_relation 表中是否有对应的 orgcode 记录

### 问题2：找不到班次数据
**现象：** test_01 中班次数据检查返回空
**解决：** 检查 shift_data 表中是否有对应的 objid 记录

### 问题3：时间匹配问题
**现象：** 能找到班次数据，但找不到当前班次
**解决：** 检查测试时间是否在班次的上下班时间范围内

### 问题4：找不到上个班次
**现象：** 找到当前班次，但找不到上个班次
**解决：** 检查班次数据的连续性，确保有前一个班次的记录

### 问题5：找不到采集点数据
**现象：** 找到班次信息，但没有采集点数据
**解决：** 检查 acctobj_input 和 acctobj_inputmx 表中的数据匹配条件

## 调试技巧

1. **逐步执行：** 按顺序执行测试文件，找到第一个失败的步骤
2. **检查参数：** 确保替换了正确的测试参数
3. **查看数据：** 直接查询相关表，确认数据是否存在
4. **时间格式：** 确保时间格式正确（YYYY-MM-DD HH24:MI:SS）
5. **调试输出：** test_04 会输出详细的调试信息

## 修复建议

根据测试结果，可能需要：

1. **修正参数逻辑：** 如果机构关系查找有问题
2. **调整时间匹配：** 如果班次时间匹配有问题
3. **修正查询条件：** 如果采集点数据查找有问题
4. **检查数据完整性：** 如果基础数据缺失

执行完测试后，请告诉我具体的错误信息和返回结果，我会帮您进一步分析和修复问题。
