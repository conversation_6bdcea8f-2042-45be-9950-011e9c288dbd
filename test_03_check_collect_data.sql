-- 测试3：检查采集点数据
-- 验证是否能找到上个班次的采集点数据

-- 替换这里的参数值进行测试
\set p_objid 'YOUR_OBJID_HERE'
\set p_datetime '2024-01-15 14:30:00'
\set p_acctobj_id 'YOUR_ACCTOBJ_ID_HERE'  -- 可选，如果不需要过滤可以设为空

-- 1. 检查 acctobj_input 表中是否有相关数据
SELECT 
    '1. acctobj_input 数据检查' as test_name,
    COUNT(*) as record_count
FROM acctobj_input ai
WHERE ai.tmused = 1;

-- 2. 检查 acctobj_inputmx 表中是否有相关数据
SELECT 
    '2. acctobj_inputmx 数据检查' as test_name,
    COUNT(*) as record_count
FROM acctobj_inputmx mx
WHERE mx.tmused = 1;

-- 3. 基于上个班次信息查找采集点数据
WITH parent_org AS (
    SELECT COALESCE(
        (SELECT porgcode FROM sys_org_relation WHERE orgcode = :'p_objid' AND used = 1),
        (SELECT orgcode FROM shift_data WHERE objid = :'p_objid' LIMIT 1)
    ) as parent_orgcode
),
current_shift AS (
    SELECT 
        sd.orgcode, sd.dbrq, sd.objid, sd.shiftclassid, sd.sbsj, sd.xbsj
    FROM shift_data sd, parent_org po
    WHERE sd.orgcode = po.parent_orgcode
      AND sd.objid = :'p_objid'
      AND (
          (sd.dbrq = SUBSTRING(:'p_datetime' FROM 1 FOR 10) 
           AND SUBSTRING(:'p_datetime' FROM 12 FOR 8) BETWEEN sd.sbsj AND sd.xbsj)
          OR
          (sd.dbrq = TO_CHAR(TO_DATE(SUBSTRING(:'p_datetime' FROM 1 FOR 10), 'YYYY-MM-DD') - INTERVAL '1 day', 'YYYY-MM-DD')
           AND sd.xbsj < sd.sbsj
           AND SUBSTRING(:'p_datetime' FROM 12 FOR 8) >= sd.sbsj)
          OR
          (sd.dbrq = SUBSTRING(:'p_datetime' FROM 1 FOR 10)
           AND sd.xbsj < sd.sbsj
           AND SUBSTRING(:'p_datetime' FROM 12 FOR 8) <= sd.xbsj)
      )
    ORDER BY sd.dbrq DESC, sd.sbsj DESC
    LIMIT 1
),
previous_shift AS (
    SELECT 
        sd.shiftclassid, sd.sbsj, sd.xbsj, sd.dbrq, sd.objid
    FROM shift_data sd, parent_org po, current_shift cs
    WHERE sd.orgcode = po.parent_orgcode
      AND (
          (sd.dbrq = cs.dbrq AND sd.xbsj = cs.sbsj)
          OR
          (sd.dbrq = TO_CHAR(TO_DATE(cs.dbrq, 'YYYY-MM-DD') - INTERVAL '1 day', 'YYYY-MM-DD')
           AND sd.xbsj = cs.sbsj)
          OR
          (sd.dbrq = cs.dbrq AND sd.sbsj < cs.sbsj)
          OR
          (sd.dbrq = TO_CHAR(TO_DATE(cs.dbrq, 'YYYY-MM-DD') - INTERVAL '1 day', 'YYYY-MM-DD')
           AND NOT EXISTS (
               SELECT 1 FROM shift_data sd2, parent_org po2
               WHERE sd2.orgcode = po2.parent_orgcode
                 AND sd2.dbrq = cs.dbrq
                 AND sd2.sbsj < cs.sbsj
           ))
      )
    ORDER BY
        CASE WHEN sd.xbsj = cs.sbsj THEN 1 ELSE 2 END,
        sd.dbrq DESC,
        sd.sbsj DESC
    LIMIT 1
)
-- 查找匹配的 acctobj_input 记录
SELECT 
    '3. 匹配的 acctobj_input 记录' as test_name,
    ai.id,
    ai.acctobj_id,
    ai.bcdm,
    ai.sbsj,
    ai.team_id,
    ps.shiftclassid as expected_bcdm,
    ps.sbsj as expected_sbsj,
    ps.objid as expected_team_id
FROM acctobj_input ai, previous_shift ps
WHERE ai.tmused = 1
  AND ai.bcdm = ps.shiftclassid
  AND ai.sbsj = ps.sbsj
  AND ai.team_id = ps.objid
  AND (:'p_acctobj_id' = '' OR ai.acctobj_id = :'p_acctobj_id')
LIMIT 10;

-- 4. 最终的采集点数据查询
WITH parent_org AS (
    SELECT COALESCE(
        (SELECT porgcode FROM sys_org_relation WHERE orgcode = :'p_objid' AND used = 1),
        (SELECT orgcode FROM shift_data WHERE objid = :'p_objid' LIMIT 1)
    ) as parent_orgcode
),
current_shift AS (
    SELECT 
        sd.orgcode, sd.dbrq, sd.objid, sd.shiftclassid, sd.sbsj, sd.xbsj
    FROM shift_data sd, parent_org po
    WHERE sd.orgcode = po.parent_orgcode
      AND sd.objid = :'p_objid'
      AND (
          (sd.dbrq = SUBSTRING(:'p_datetime' FROM 1 FOR 10) 
           AND SUBSTRING(:'p_datetime' FROM 12 FOR 8) BETWEEN sd.sbsj AND sd.xbsj)
          OR
          (sd.dbrq = TO_CHAR(TO_DATE(SUBSTRING(:'p_datetime' FROM 1 FOR 10), 'YYYY-MM-DD') - INTERVAL '1 day', 'YYYY-MM-DD')
           AND sd.xbsj < sd.sbsj
           AND SUBSTRING(:'p_datetime' FROM 12 FOR 8) >= sd.sbsj)
          OR
          (sd.dbrq = SUBSTRING(:'p_datetime' FROM 1 FOR 10)
           AND sd.xbsj < sd.sbsj
           AND SUBSTRING(:'p_datetime' FROM 12 FOR 8) <= sd.xbsj)
      )
    ORDER BY sd.dbrq DESC, sd.sbsj DESC
    LIMIT 1
),
previous_shift AS (
    SELECT 
        sd.shiftclassid, sd.shiftclassname, sd.sbsj, sd.xbsj, sd.dbrq, sd.objid
    FROM shift_data sd, parent_org po, current_shift cs
    WHERE sd.orgcode = po.parent_orgcode
      AND (
          (sd.dbrq = cs.dbrq AND sd.xbsj = cs.sbsj)
          OR
          (sd.dbrq = TO_CHAR(TO_DATE(cs.dbrq, 'YYYY-MM-DD') - INTERVAL '1 day', 'YYYY-MM-DD')
           AND sd.xbsj = cs.sbsj)
          OR
          (sd.dbrq = cs.dbrq AND sd.sbsj < cs.sbsj)
          OR
          (sd.dbrq = TO_CHAR(TO_DATE(cs.dbrq, 'YYYY-MM-DD') - INTERVAL '1 day', 'YYYY-MM-DD')
           AND NOT EXISTS (
               SELECT 1 FROM shift_data sd2, parent_org po2
               WHERE sd2.orgcode = po2.parent_orgcode
                 AND sd2.dbrq = cs.dbrq
                 AND sd2.sbsj < cs.sbsj
           ))
      )
    ORDER BY
        CASE WHEN sd.xbsj = cs.sbsj THEN 1 ELSE 2 END,
        sd.dbrq DESC,
        sd.sbsj DESC
    LIMIT 1
)
-- 最终的采集点数据
SELECT 
    '4. 最终采集点数据' as test_name,
    mx.collect_point_id,
    mx.ipt_id,
    mx.collect_point_text,
    mx.collect_point_val,
    mx.input_comp_type,
    mx.input_options,
    mx.input_time,
    mx.job_input_time,
    ps.shiftclassid as shift_class_id,
    ps.shiftclassname as shift_class_name,
    ps.sbsj as shift_start_time,
    ps.xbsj as shift_end_time,
    ps.dbrq as shift_date
FROM acctobj_inputmx mx, previous_shift ps
WHERE mx.tmused = 1
  AND EXISTS (
      SELECT ai.id
      FROM acctobj_input ai
      WHERE ai.tmused = 1
        AND ai.bcdm = ps.shiftclassid
        AND ai.sbsj = ps.sbsj
        AND ai.team_id = ps.objid
        AND (:'p_acctobj_id' = '' OR ai.acctobj_id = :'p_acctobj_id')
        AND ai.id = mx.ipt_id
  )
ORDER BY mx.collect_point_id
LIMIT 10;
