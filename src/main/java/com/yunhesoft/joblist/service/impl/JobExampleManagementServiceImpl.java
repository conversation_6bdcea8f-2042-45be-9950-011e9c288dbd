package com.yunhesoft.joblist.service.impl;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import com.yunhesoft.joblist.utils.JoblistActivityCacheUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSONObject;
import com.yunhesoft.core.utils.RedisUtil;
import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.joblist.entity.dto.JoblistInsertParamsDto;
import com.yunhesoft.joblist.entity.dto.StandardJobLibQueryDto;
import com.yunhesoft.joblist.entity.po.JobListExampleDutyPerson;
import com.yunhesoft.joblist.entity.po.JoblistActivityExample;
import com.yunhesoft.joblist.entity.po.JoblistActivityProperties;
import com.yunhesoft.joblist.entity.po.JoblistExampleFormBind;
import com.yunhesoft.joblist.entity.po.JoblistGeneraterReCord;
import com.yunhesoft.joblist.entity.vo.JoblistActivityExampleVo;
import com.yunhesoft.joblist.generater.AbstractJobGenerater;
import com.yunhesoft.joblist.generater.PostJobGenerrater;
import com.yunhesoft.joblist.service.IJobExampleManagementService;
import com.yunhesoft.joblist.service.IJobGeneraterService;
import com.yunhesoft.joblist.service.IJoblistMethodService;
import com.yunhesoft.leanCosting.unitConf.entity.dto.MethodQueryDto;
import com.yunhesoft.leanCosting.unitConf.entity.po.Costuint;
import com.yunhesoft.leanCosting.unitConf.service.IUnitMethodService;
import com.yunhesoft.shift.shift.entity.vo.ShiftForeignVo;
import com.yunhesoft.system.kernel.service.EntityService;
import com.yunhesoft.system.kernel.service.model.Order;
import com.yunhesoft.system.kernel.service.model.Where;
import org.springframework.transaction.annotation.Transactional;

@Service
public class JobExampleManagementServiceImpl implements IJobExampleManagementService {
    @Autowired
    private EntityService dao;
    @Autowired
    private IJobGeneraterService jobGeneraterService;
    @Autowired
    private IJoblistMethodService methodService;
    @Autowired
    private RedisUtil redisUtil;

    @Autowired
    private IUnitMethodService unitMethodService;

    /**
     * 删除任务实例
     *
     * @return
     * <AUTHOR>
     * @date 2024/8/9
     * @params
     */
    @Override
    public Boolean deleteJobExample(String id) {
        boolean flag = false;
        //根据id 进行删除数据
        List<String> ids = new ArrayList<>();
        ids.add(id);
        List<JoblistActivityExample> joblistActivityExamplesByIds = jobGeneraterService.getJoblistActivityExamplesByIds(ids, null);
        if (StringUtils.isEmpty(joblistActivityExamplesByIds)) {
            return flag;
        }
        JoblistActivityExample job = joblistActivityExamplesByIds.get(0);
        if (job.getIsParent() == 1) {
            //如果是父数据查询其所有的子数据
            List<String> pids = new ArrayList<>();
            pids.add(job.getId());
            List<JoblistActivityExample> activityListByPidList = jobGeneraterService.getActivityListByPidList(0, null, pids);
            if (StringUtils.isNotEmpty(activityListByPidList)) {
                joblistActivityExamplesByIds.addAll(activityListByPidList);
            }
            flag = dao.deleteByIdBatch(joblistActivityExamplesByIds) > 0;
            if(flag){
                JoblistActivityCacheUtils.clearActivityExampleCache(joblistActivityExamplesByIds, true);
            }
        }else{
            //仅删除子数据
            flag = dao.deleteByIdBatch(joblistActivityExamplesByIds) > 0;
            if(flag){
                JoblistActivityCacheUtils.clearActivityExampleCache(joblistActivityExamplesByIds, false);
            }
        }
        return flag;
    }

    /**
     * 更新任务实例  修改配置  仅在修改活动配置时
     *
     * @return
     * <AUTHOR>
     * @date 2024/8/9
     * @params
     */
    @Override
    @Transactional
    public Boolean updateJobExample(String propertyId) {
        boolean flag = false;
        if (StringUtils.isEmpty(propertyId)) {
            return flag;
        }
        //获取活动配置
        StandardJobLibQueryDto queryDto = new StandardJobLibQueryDto();
        queryDto.setId(propertyId);
        Date date = new Date();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        String selectDate = sdf.format(date);
        queryDto.setQueryDate(selectDate);
        queryDto.setTmUsed(null);
        List<JoblistActivityProperties> joblistActivityPropertiesList = methodService.getJoblistActivityPropertiesList(queryDto);
        if (StringUtils.isEmpty(joblistActivityPropertiesList)) {
            return flag;
        }
        JoblistActivityProperties property = joblistActivityPropertiesList.get(0);
        //查询今天的班次情况
        AbstractJobGenerater generater = new PostJobGenerrater();
        List<ShiftForeignVo> shiftInfoList = generater.getShiftInfo(property.getOrgid(), selectDate);
        if (StringUtils.isEmpty(shiftInfoList)) {
            return flag;
        }
        //查询今天的活动数据
        List<JoblistActivityExample> joblistActivityExampleList = getJoblistActivityExampleList(property, shiftInfoList);
        //查询此任务所涉及的活动班次
        List<String> jobShiftList = new ArrayList<>();
        if (StringUtils.isNotEmpty(joblistActivityExampleList)) {
            jobShiftList = joblistActivityExampleList.stream().map(JoblistActivityExample::getShiftClassCode).distinct().collect(Collectors.toList());
        }
        List<String> exampleIdList = new ArrayList<>();
        List<String> exampleIds = new ArrayList<>();
        if (StringUtils.isNotEmpty(joblistActivityExampleList)) {
            exampleIds = joblistActivityExampleList.stream().map(JoblistActivityExample::getId).collect(Collectors.toList());
            Map<String, List<String>> groupExampleId = joblistActivityExampleList.stream()
                    .collect(Collectors.groupingBy(JoblistActivityExample::getShiftClassCode,
                            Collectors.mapping(JoblistActivityExample::getId, Collectors.toList())
                    ));
            for (Map.Entry<String, List<String>> stringListEntry : groupExampleId.entrySet()) {
                List<String> value = stringListEntry.getValue();
                value = value.stream().sorted().distinct().collect(Collectors.toList());
                exampleIdList.add(StringUtils.join(value, ","));
            }
            //删除工作负责人
            deleteJobDutyPerson(exampleIds);
            //删除表单绑定关系
            deleteJobFormBind(exampleIds);
            //删除今天的任务实例数据
            dao.deleteByIdBatch(joblistActivityExampleList);
            JoblistActivityCacheUtils.clearActivityExampleCache(joblistActivityExampleList, true);
        }
        if ("none".equals(property.getFrequencyid())) {
            //不定期任务
            List<String> finalJobShiftList = jobShiftList;
            shiftInfoList = shiftInfoList.stream()
                    .filter(item-> !finalJobShiftList.contains(item.getShiftClassCode())).collect(Collectors.toList());
            flag = updateUncertainJob(propertyId, shiftInfoList, property, flag);
        } else {
            //频次任务
            //删除生成记录表
            deleteGenerateRecords(joblistActivityExampleList);
            List<String> ids = joblistActivityPropertiesList.stream().map(JoblistActivityProperties::getId).collect(Collectors.toList());
            //查询活动名称
            MethodQueryDto dto = new MethodQueryDto();
            dto.setIdList(ids);
            List<Costuint> unitList = unitMethodService.getCostuintList(dto);
            if (StringUtils.isNotEmpty(unitList)) {
                Map<String, String> nameMap = unitList.stream().collect(Collectors.toMap(Costuint::getId, Costuint::getName));
                joblistActivityPropertiesList.stream().forEach(i -> i.setJobName(nameMap.get(i.getId())));
            }
            //预制 按照新配置生成的数据
            JSONObject result = new JSONObject();
            jobGeneraterService.generaterJobByActivityProperties(0, joblistActivityPropertiesList, result, true,true);
            flag = true;
        }
        return flag;
    }

    private void deleteGenerateRecords(List<JoblistActivityExample> examples) {
        List<JoblistGeneraterReCord> reCordList = new ArrayList<>();
        if (StringUtils.isNotEmpty(examples)) {
            List<String> keyList = examples.stream().map(item -> {
                return JobGeneraterServiceImpl.PREFIX + item.getActivityId() + ":" + item.getBeginDate() + "_" + item.getEndDate() +
                        ":";
            }).distinct().collect(Collectors.toList());
            for (String key : keyList) {
                Where where1 = Where.create();
                where1.like(JoblistGeneraterReCord::getActivityRecordKey, key);
                List<JoblistGeneraterReCord> joblistGeneraterReCords = dao.rawQueryListByWhere(JoblistGeneraterReCord.class, where1);
                if(StringUtils.isNotEmpty(joblistGeneraterReCords)){
                    reCordList.addAll(joblistGeneraterReCords);
                }
            }
            if (StringUtils.isEmpty(reCordList)) {
                reCordList = new ArrayList<>();
                List<String> exampleIds =
                        examples.stream().map(JoblistActivityExample::getId).collect(Collectors.toList());
                //兼容老的错误数据
                for (String exampleId : exampleIds) {
                    Where where2 = Where.create();
                    where2.like(JoblistGeneraterReCord::getExampleIdList, exampleId);
                    reCordList.addAll(dao.rawQueryListByWhere(JoblistGeneraterReCord.class, where2));
                }
            }
            //删除记录
            if (StringUtils.isNotEmpty(reCordList)) {
                List<String> keys = reCordList.stream().map(JoblistGeneraterReCord::getActivityRecordKey).collect(Collectors.toList());
                //删除redis
                Map<String,String> keyBeans= new HashMap<>();
                for (String key : keys) {
                    redisUtil.set(key,null);
                }
                dao.deleteByIdBatch(reCordList);
            }
        }
    }
    /**
     * 根据班次重新生成不定期活动
     * <AUTHOR>
     * @date 2025/5/21
     * @params
     * @return
     *
    */
    private boolean updateUncertainJob(String propertyId, List<ShiftForeignVo> shiftInfoList, JoblistActivityProperties property, boolean flag) {
        //生成新的临时任务
        List<String> ids = new ArrayList<>();
        ids.add(propertyId);
        for (ShiftForeignVo shiftForeignVo : shiftInfoList) {
            JoblistInsertParamsDto paramsDto = new JoblistInsertParamsDto();
            paramsDto.setShiftInfo(shiftForeignVo);
            paramsDto.setResponsibilityType(property.getResponsibilityType());
            paramsDto.setPropertiesIdList(ids);
            List<JoblistActivityExampleVo> joblistActivityExampleVos = jobGeneraterService.insertActivityInstanceMain(paramsDto);
            if (StringUtils.isNotEmpty(joblistActivityExampleVos)) {
                flag = true;
            }
        }
        return flag;
    }

    /**
     * 批量更新任务实例  修改配置  仅在修改活动配置时
     *
     * @return
     * <AUTHOR>
     * @date 2024/8/9
     * @params
     */
    @Override
    public Boolean updateJobExampleBatch(List<String> deleteList, List<String> generatorList, Integer beforeHandDay) {
        //删除已有的活动数据
        List<String> exampleIdList = new ArrayList<>();
        List<JoblistActivityExample> joblistActivityExampleList = deleteJobBatch(deleteList, exampleIdList);
        return generatorJobBatch(generatorList, beforeHandDay, joblistActivityExampleList);
    }

    private Boolean generatorJobBatch(List<String> generatorList, Integer beforeHandDay, List<JoblistActivityExample> joblistActivityExampleList) {
        boolean flag = false;
        if(StringUtils.isEmpty(generatorList)){
            return flag;
        }
        //生成新的数据
        StandardJobLibQueryDto queryDto = new StandardJobLibQueryDto();
        queryDto.setIdList(generatorList);
        Date date = new Date();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        String selectDate = sdf.format(date);
        queryDto.setQueryDate(selectDate);
        queryDto.setTmUsed(1);
        List<JoblistActivityProperties> joblistActivityPropertiesList = methodService.getJoblistActivityPropertiesList(queryDto);
        if (StringUtils.isEmpty(joblistActivityPropertiesList)) {
            return flag;
        }
        for (JoblistActivityProperties property : joblistActivityPropertiesList) {
            AbstractJobGenerater generater = new PostJobGenerrater();
            List<ShiftForeignVo> shiftInfoList = generater.getShiftInfo(property.getOrgid(), selectDate);
            if (StringUtils.isEmpty(shiftInfoList)) {
                return flag;
            }
            if ("none".equals(property.getFrequencyid())) {
                //不定期任务
                //生成新的临时任务
                List<String> ids = new ArrayList<>();
                ids.add(property.getId());
                for (ShiftForeignVo shiftForeignVo : shiftInfoList) {
                    JoblistInsertParamsDto paramsDto = new JoblistInsertParamsDto();
                    paramsDto.setShiftInfo(shiftForeignVo);
                    paramsDto.setResponsibilityType(property.getResponsibilityType());
                    paramsDto.setPropertiesIdList(ids);
                    List<JoblistActivityExampleVo> joblistActivityExampleVos = jobGeneraterService.insertActivityInstanceMain(paramsDto);
                    if (StringUtils.isNotEmpty(joblistActivityExampleVos)) {
                        flag = true;
                    }
                }
            }

        }
        //频次任务
        joblistActivityPropertiesList = joblistActivityPropertiesList.stream()
                .filter(item -> !"none".equals(item.getFrequencyid()))
                .collect(Collectors.toList());
        List<String> ids = joblistActivityPropertiesList.stream().map(JoblistActivityProperties::getId).collect(Collectors.toList());
        //查询活动名称
        MethodQueryDto dto = new MethodQueryDto();
        dto.setIdList(ids);
        List<Costuint> unitList = unitMethodService.getCostuintList(dto);
        if (StringUtils.isNotEmpty(unitList)) {
            Map<String, String> nameMap = unitList.stream().collect(Collectors.toMap(Costuint::getId, Costuint::getName));
            joblistActivityPropertiesList.stream().forEach(i -> i.setJobName(nameMap.get(i.getId())));
        }
        //预制 按照新配置生成的数据
        JSONObject result = new JSONObject();
        result = jobGeneraterService.generaterJobByActivityProperties(beforeHandDay, joblistActivityPropertiesList, result, true);
        if (StringUtils.isNotEmpty(result.getString("error"))) {
            //有错误信息 还原 放弃更新
            dao.insertBatch(joblistActivityExampleList);
            return false;
        }
        flag = true;
        return flag;
    }

    private List<JoblistActivityExample> deleteJobBatch(List<String> deleteList, List<String> exampleIdList) {
        // 删除列表  获取活动配置
        StandardJobLibQueryDto queryDto = new StandardJobLibQueryDto();
        queryDto.setIdList(deleteList);
        Date date = new Date();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        String selectDate = sdf.format(date);
        queryDto.setQueryDate(selectDate);
        queryDto.setTmUsed(null);
        List<JoblistActivityProperties> joblistActivityPropertiesList = methodService.getJoblistActivityPropertiesList(queryDto);
        if (StringUtils.isEmpty(joblistActivityPropertiesList)) {
            return null;
        }
        List<JoblistActivityExample> joblistActivityExampleList = new ArrayList<>();
        for (JoblistActivityProperties property : joblistActivityPropertiesList) {
            //查询今天的班次情况
            AbstractJobGenerater generater = new PostJobGenerrater();
            List<ShiftForeignVo> shiftInfoList = generater.getShiftInfo(property.getOrgid(), selectDate);
            if (StringUtils.isEmpty(shiftInfoList)) {
                return null;
            }
            //查询今天的活动数据
            joblistActivityExampleList.addAll(getJoblistActivityExampleList(property, shiftInfoList));

        }
        //查询此任务所涉及的活动班次
        List<String> jobShiftList = new ArrayList<>();
        if (StringUtils.isNotEmpty(joblistActivityExampleList)) {
            jobShiftList = joblistActivityExampleList.stream().map(JoblistActivityExample::getShiftClassCode).distinct().collect(Collectors.toList());
        }
        List<String> exampleIds = new ArrayList<>();
        if (StringUtils.isNotEmpty(joblistActivityExampleList)) {
            exampleIds = joblistActivityExampleList.stream().map(JoblistActivityExample::getId).collect(Collectors.toList());
            Map<String, List<String>> groupExampleId = joblistActivityExampleList.stream()
                    .collect(Collectors.groupingBy(JoblistActivityExample::getShiftClassCode,
                            Collectors.mapping(JoblistActivityExample::getId, Collectors.toList())
                    ));
            for (Map.Entry<String, List<String>> stringListEntry : groupExampleId.entrySet()) {
                List<String> value = stringListEntry.getValue();
                value = value.stream().sorted().distinct().collect(Collectors.toList());
                exampleIdList.add(StringUtils.join(value, ","));
            }
            //频次任务
            //删除生成记录表
            deleteGenerateRecords(joblistActivityExampleList);
            //删除工作负责人
            deleteJobDutyPerson(exampleIds);
            //删除表单绑定关系
            deleteJobFormBind(exampleIds);
            //删除今天的任务实例数据
            dao.deleteByIdBatch(joblistActivityExampleList);
        }
        return joblistActivityExampleList;
    }

    /**
     * 删除表单绑定关系
     *
     * @param exampleIds
     */
    private void deleteJobFormBind(List<String> exampleIds) {
        List<JoblistExampleFormBind> formBindList;
        Where where = Where.create();
        //去重复,重新分组
        List<String> idList = new ArrayList<String>();
        for (String exampleId : exampleIds) {
			if(!idList.contains(exampleId)) {
				idList.add(exampleId);
			}
		}
    	List<String> newidList = new ArrayList<String>();
    	List<JoblistExampleFormBind> joblistExampleFormBinds = new ArrayList<JoblistExampleFormBind>();
    	for (int i = 0;i < idList.size(); i++) {
    		newidList.add(idList.get(i));
    		if(newidList.size()>500) {//整500处理一次
    			where.in(JoblistExampleFormBind::getActivityExampleId, newidList.toArray());
    	        List<JoblistExampleFormBind> binds = dao.rawQueryListByWhere(JoblistExampleFormBind.class, where);
    	        joblistExampleFormBinds.addAll(binds);
    			newidList = new ArrayList<String>();
    		}
		}
    	if(newidList.size()>0) {//最后剩余不到500处理
			where.in(JoblistExampleFormBind::getActivityExampleId, newidList.toArray());
	        List<JoblistExampleFormBind> binds = dao.rawQueryListByWhere(JoblistExampleFormBind.class, where);
	        joblistExampleFormBinds.addAll(binds);
    	}
        if (StringUtils.isNotEmpty(joblistExampleFormBinds)) {
            dao.deleteByIdBatch(joblistExampleFormBinds);
        }
        JoblistActivityCacheUtils.clearActivityFormBindInCache(exampleIds);
    }

    /**
     * 删除工作负责人
     *
     * @param exampleIds
     */
    private void deleteJobDutyPerson(List<String> exampleIds) {
        List<JobListExampleDutyPerson> dutyPersonList;
        //查询相关责任人
        Where where = Where.create();
        where.in(JobListExampleDutyPerson::getActivityExampleId, exampleIds.toArray());
        dutyPersonList = dao.rawQueryListByWhere(JobListExampleDutyPerson.class, where);
        //删除活动相关人数据
        if (StringUtils.isNotEmpty(dutyPersonList)) {
            dao.deleteByIdBatch(dutyPersonList);
        }
    }

    /**
     * 获取活动列表根据配置id 和 日期
     *
     * @return
     * <AUTHOR>
     * @date 2024/8/9
     * @params
     */
    List<JoblistActivityExample> getJoblistActivityExampleList(JoblistActivityProperties activityProperty, List<ShiftForeignVo> shiftInfoList) {
        List<JoblistActivityExample> result = new ArrayList<>();
        if (StringUtils.isEmpty(shiftInfoList)) {
            return result;
        }
        for (ShiftForeignVo shiftForeignVo : shiftInfoList) {
            Where where = Where.create();
            where.eq(JoblistActivityExample::getActivityId, activityProperty.getId());
            where.eq(JoblistActivityExample::getTmUsed, 1);
            where.eq(JoblistActivityExample::getShiftClassCode, shiftForeignVo.getShiftClassCode());
            //上下班查询
            where.ge(JoblistActivityExample::getSbsj, shiftForeignVo.getSbsj());
            Order order = Order.create();
            order.orderByAsc(JoblistActivityExample::getTmSort);
            List<JoblistActivityExample> joblistActivityExamples = dao.rawQueryListByWhere(JoblistActivityExample.class, where, order);
            if (StringUtils.isNotEmpty(joblistActivityExamples)) {
                result.addAll(joblistActivityExamples);
            }
        }
        return result;
    }

    /**
     * 删除今日班次 以及未来班次的所有活动实例相关数据
     *
     * @param property
     */
    @Override
    public void removeJobExample(JoblistActivityProperties property) {
        //查询今天的班次情况
        Date date = new Date();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        String selectDate = sdf.format(date);

        AbstractJobGenerater generater = new PostJobGenerrater();
        List<ShiftForeignVo> shiftInfoList = generater.getShiftInfo(property.getOrgid(), selectDate);
        if (StringUtils.isEmpty(shiftInfoList)) {
            return;
        }
        //查询今天的活动数据
        List<JoblistActivityExample> joblistActivityExampleList = getJoblistActivityExampleList(property, shiftInfoList);
        if(StringUtils.isNotEmpty(joblistActivityExampleList)) {
            List<String> exampleIds = joblistActivityExampleList.stream().map(JoblistActivityExample::getId).collect(Collectors.toList());
            //删除工作负责人
            deleteJobDutyPerson(exampleIds);
            //删除表单绑定关系
            deleteJobFormBind(exampleIds);
            //删除今天的任务实例数据
            dao.deleteByIdBatch(joblistActivityExampleList);
        }
    }

    /**
     * 根据id获取活动实例
     * <AUTHOR>
     * @date 2025/3/14
     * @params
     * @return
     *
    */
    @Override
    public JoblistActivityExample getJoblistActivityExampleById(String activityId) {
        return dao.queryObjectById(JoblistActivityExample.class, activityId);
    }

    /**
     * 外部修改活动状态
     *
     * @param pid
     * @param time
     * @return
     * <AUTHOR>
     * @date 2025/6/27
     * @params
     */
    @Override
    public void updateActivityByOut(String pid, String time) {
        if(StringUtils.isEmpty(pid) ||  StringUtils.isEmpty(time)){
            return;
        }
        //根据pid获取全部的子任务
        Where where = Where.create();
        where.eq(JoblistActivityExample::getTmUsed,1);
        where.eq(JoblistActivityExample::getPid, pid);
        //不是已完成的 已完成的再修改状态为已完成也没有意义
        where.ne(JoblistActivityExample::getActivityStatus, 2);
        List<JoblistActivityExample> joblistActivityExamples = dao.rawQueryListByWhere(JoblistActivityExample.class, where);
        if(StringUtils.isEmpty(joblistActivityExamples)){
            return;
        }
        List<JoblistActivityExample> actBeans = joblistActivityExamples.stream()
                .filter(item -> Objects.equals(item.getActivityDate(), time))
                .collect(Collectors.toList());
        //将符合条件的活动修改为已完成
        Map<String,Integer> actStatusMap =new HashMap<>();
        for (JoblistActivityExample item : actBeans) {
            actStatusMap.put(item.getId(), 2);
        }
        //为了加快速度将列表直接传入
        jobGeneraterService.updateActivityStatusByActivityId(actStatusMap,()-> actBeans);
    }
}
