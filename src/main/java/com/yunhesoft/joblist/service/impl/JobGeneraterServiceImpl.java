package com.yunhesoft.joblist.service.impl;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.Collator;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.TreeSet;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;

import com.yunhesoft.core.common.model.SysUser;
import com.yunhesoft.tmsf.form.entity.po.SFForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunhesoft.accountTools.entity.dto.AccountConfigQueryDto;
import com.yunhesoft.accountTools.entity.po.DigitalLedger;
import com.yunhesoft.accountTools.service.IAccountConfigService;
import com.yunhesoft.core.common.utils.DateTimeUtils;
import com.yunhesoft.core.common.utils.TMUID;
import com.yunhesoft.core.utils.ObjUtils;
import com.yunhesoft.core.utils.RedisUtil;
import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.joblist.constant.JoblistConstant;
import com.yunhesoft.joblist.entity.dto.JobAllocationDto;
import com.yunhesoft.joblist.entity.dto.JobAllocationUserDto;
import com.yunhesoft.joblist.entity.dto.JobFinishDto;
import com.yunhesoft.joblist.entity.dto.JobInstanceListDto;
import com.yunhesoft.joblist.entity.dto.JoblistGenDto;
import com.yunhesoft.joblist.entity.dto.JoblistInputDto;
import com.yunhesoft.joblist.entity.dto.JoblistInsertParamsDto;
import com.yunhesoft.joblist.entity.dto.PersonTaskDto;
import com.yunhesoft.joblist.entity.dto.StandardJobLibQueryDto;
import com.yunhesoft.joblist.entity.dto.WorkCalendarAcitityDetailDto;
import com.yunhesoft.joblist.entity.dto.WorkCalendarGroupDto;
import com.yunhesoft.joblist.entity.dto.WorkCalendarGroupTaskDto;
import com.yunhesoft.joblist.entity.dto.WorkCalendarQueryDto;
import com.yunhesoft.joblist.entity.po.CycleScheme;
import com.yunhesoft.joblist.entity.po.JobListExampleDutyPerson;
import com.yunhesoft.joblist.entity.po.JobListScore;
import com.yunhesoft.joblist.entity.po.JoblistActivityExample;
import com.yunhesoft.joblist.entity.po.JoblistActivityExampleBind;
import com.yunhesoft.joblist.entity.po.JoblistActivityProperties;
import com.yunhesoft.joblist.entity.po.JoblistClass;
import com.yunhesoft.joblist.entity.po.JoblistConfirm;
import com.yunhesoft.joblist.entity.po.JoblistExampleFormBind;
import com.yunhesoft.joblist.entity.po.JoblistExtPropertiesConfig;
import com.yunhesoft.joblist.entity.po.JoblistGeneraterReCord;
import com.yunhesoft.joblist.entity.po.JoblistInputMapping;
import com.yunhesoft.joblist.entity.po.JoblistOperCardBind;
import com.yunhesoft.joblist.entity.po.JoblistPersonBind;
import com.yunhesoft.joblist.entity.po.JoblistProgramAssMethod;
import com.yunhesoft.joblist.entity.vo.JoblistActivityExampleVo;
import com.yunhesoft.joblist.entity.vo.JoblistActivityPropertiesVo;
import com.yunhesoft.joblist.entity.vo.JoblistClassVo;
import com.yunhesoft.joblist.entity.vo.JoblistPersonTaskNumVo;
import com.yunhesoft.joblist.entity.vo.JoblistShiftBcVo;
import com.yunhesoft.joblist.entity.vo.JoblistUserPostVo;
import com.yunhesoft.joblist.generater.AbstractJobGenerater;
import com.yunhesoft.joblist.generater.GroupJobGenerater;
import com.yunhesoft.joblist.generater.PostJobGenerrater;
import com.yunhesoft.joblist.service.IJobGeneraterService;
import com.yunhesoft.joblist.service.IJoblistInputService;
import com.yunhesoft.joblist.service.IJoblistMethodService;
import com.yunhesoft.joblist.service.IStandardJobLibConfigService;
import com.yunhesoft.joblist.utils.JoblistActivityCacheUtils;
import com.yunhesoft.joblist.utils.SqlInExecutor;
import com.yunhesoft.leanCosting.unitConf.entity.dto.MethodQueryDto;
import com.yunhesoft.leanCosting.unitConf.entity.po.Costuint;
import com.yunhesoft.leanCosting.unitConf.service.IUnitMethodService;
import com.yunhesoft.qualityIndex.entity.dto.JoblistQualityIndexQueryDto;
import com.yunhesoft.qualityIndex.entity.dto.QualityIndexDto;
import com.yunhesoft.qualityIndex.entity.po.QualityIndex;
import com.yunhesoft.qualityIndex.entity.po.QualityResult;
import com.yunhesoft.qualityIndex.service.IJoblistQualityIndexService;
import com.yunhesoft.qualityIndex.service.IQualityResultService;
import com.yunhesoft.shift.shift.entity.vo.ShiftForeignVo;
import com.yunhesoft.shift.shift.service.IShiftService;
import com.yunhesoft.system.employee.entity.dto.EmpOrgPostParamDto;
import com.yunhesoft.system.employee.entity.dto.EmpParamDto;
import com.yunhesoft.system.employee.entity.po.SysEmployeeInfo;
import com.yunhesoft.system.employee.entity.po.SysEmployeeOrgPost;
import com.yunhesoft.system.employee.service.ISysEmployeeInfoService;
import com.yunhesoft.system.employee.service.ISysEmployeeOrgPostService;
import com.yunhesoft.system.kernel.config.SysUserHolder;
import com.yunhesoft.system.kernel.service.EntityService;
import com.yunhesoft.system.kernel.service.model.Order;
import com.yunhesoft.system.kernel.service.model.Pagination;
import com.yunhesoft.system.kernel.service.model.Where;
import com.yunhesoft.system.org.entity.po.SysOrg;
import com.yunhesoft.system.org.service.ISysOrgService;
import com.yunhesoft.system.post.entity.po.SysPost;
import com.yunhesoft.system.post.service.ISysPostService;
import com.yunhesoft.system.tools.components.entity.vo.ComponentVo;
import com.yunhesoft.system.tools.components.entity.vo.PanelVo;
import com.yunhesoft.system.tools.components.service.SysComponentService;
import com.yunhesoft.task.apply.entity.vo.TmTaskInfoVo;
import com.yunhesoft.task.apply.service.ITaskAddService;
import com.yunhesoft.task.flow.entity.vo.TmtaskVo;

import lombok.extern.log4j.Log4j2;

@Log4j2
@Service
public class JobGeneraterServiceImpl implements IJobGeneraterService {

    @Autowired
    private IJoblistMethodService methodService;
    @Autowired
    private EntityService dao;

    @Autowired
    private SysComponentService componentService;
    @Autowired
    private IStandardJobLibConfigService jobLibConfigService;
    @Autowired
    private ISysEmployeeOrgPostService orgPostService;
    @Autowired
    private ISysPostService postService;
    @Autowired
    private ISysEmployeeInfoService employeeInfoService;
    @Autowired
    private IUnitMethodService unitMethodService;
    @Autowired
    private ITaskAddService taskAddService;
    @Autowired
    ISysOrgService orgService;
    @Autowired
    private IJoblistInputService inputService;
    @Autowired
    private IShiftService shiftService;
    public final static String PREFIX = "JOBLIST:ACTIVITY_TASK_RECORDER:";

    @Autowired
    private RedisUtil redisUtil;
    @Autowired
    private StringRedisTemplate redisTemplate;
    private Date sbsj;
    private Date xbsj;

    /**
     * 根据岗位工位查询活动
     *
     * @param post
     * @param workStation
     * @return
     * <AUTHOR>
     * @date 2024/6/20
     * @params
     */
    @Override
    public List<JoblistActivityPropertiesVo> getActivityByPostAndWorkStation(String post, String workStation) {
        return Collections.emptyList();
    }

    /**
     * 根据岗位工位班次查询活动
     *
     * @param post
     * @param workStation
     * @param shift
     * @return
     * <AUTHOR>
     * @date 2024/6/20
     * @params
     */
    @Override
    public List<JoblistActivityPropertiesVo> getActivityByPostAndWorkStationAndShift(String post, String workStation, String shift) {
        return Collections.emptyList();
    }

    /**
     * 根据人员获取活动
     *
     * @return
     * <AUTHOR>
     * @date 2024/6/20
     * @params
     */
    @Override
    public List<JoblistActivityPropertiesVo> getActivityByCurrentUser() {
        return Collections.emptyList();
    }

    /**
     * 生成频次活动实例入口方法
     *
     * @return
     * <AUTHOR>
     * @params 预先天数 默认1
     */
    public JSONObject activityGeneraterMain(Integer beforeHandDay) {
        Long[] longs = this.logTime("任务生成", null);
        JSONObject result = new JSONObject();
        if (beforeHandDay == null) {
            beforeHandDay = 1;
        }
        //获取符合生成条件的活动列表
        StandardJobLibQueryDto queryDto = new StandardJobLibQueryDto();
        Date date = new Date();
        Date date1 = DateTimeUtils.doDate(date, beforeHandDay);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        String format = sdf.format(date1);
        queryDto.setQueryDate(format);
        queryDto.setTmUsed(1);
        List<JoblistActivityProperties> joblistActivityPropertiesList = methodService.getJoblistActivityPropertiesList(queryDto);
        //获取频次列表
        if (StringUtils.isEmpty(joblistActivityPropertiesList)) {
            result.put("error", "活动表为空，无需生成");
            return result;
        }
        //有效的配置列表
        Set<String> propertyIds = new HashSet<>();
        //根据当前的有效作业单元id
        List<String> orgList = joblistActivityPropertiesList.stream().map(i -> i.getOrgid()).distinct().collect(Collectors.toList());
        for (String s : orgList) {
            List<JoblistActivityPropertiesVo> joblistActivityListByTmSort = jobLibConfigService.getJoblistActivityListByTmSort(s, null);
            if (StringUtils.isNotEmpty(joblistActivityListByTmSort)) {
                for (JoblistActivityPropertiesVo joblistActivityPropertiesVo : joblistActivityListByTmSort) {
                    propertyIds.add(joblistActivityPropertiesVo.getId());
                }
            }
        }
        //筛选有效的配置列表
        joblistActivityPropertiesList = joblistActivityPropertiesList.stream().filter(i -> propertyIds.contains(i.getId())).collect(Collectors.toList());
        if (StringUtils.isEmpty(joblistActivityPropertiesList)) {
            result.put("error", "有效的活动表为空，无需生成");
            return result;
        }
        List<String> ids = joblistActivityPropertiesList.stream().map(JoblistActivityProperties::getId).collect(Collectors.toList());
        //查询活动名称
        MethodQueryDto dto = new MethodQueryDto();
        dto.setIdList(ids);
        List<Costuint> unitList = unitMethodService.getCostuintList(dto);
        if (StringUtils.isNotEmpty(unitList)) {
            Map<String, String> nameMap = unitList.stream().collect(Collectors.toMap(Costuint::getId, Costuint::getName));
            joblistActivityPropertiesList.stream().forEach(i -> i.setJobName(nameMap.get(i.getId())));
        }
        //区分不同任务类型  班组任务 岗位任务
        result = generaterJobByActivityProperties(beforeHandDay, joblistActivityPropertiesList, result);
        this.logTime("任务生成", longs);
        log.info("生成情况：{}", result);
        return result;
    }

    public static Long[] logTime(String startTip, Long[] time) {
        Long start = null;
        Long end = null;
        if (time != null) {
            start = time[0];
            end = time[1];
        }
        Long[] arr = new Long[2];
        Calendar calendar = Calendar.getInstance();
        if (start == null) {
            log.info("========{}=======", startTip + "开始");
            start = calendar.getTimeInMillis();
            arr[0] = start;
            log.info("开始时间:{}", arr[0]);
        } else if (end == null) {
            end = calendar.getTimeInMillis();
            arr[1] = end;
            log.info("结束时间:{}", arr[1]);
        }
        if (start != null && end != null) {
            double v = Double.valueOf(end - start) / 1000;
            log.info("时间消耗:{}秒", v);
            log.info("========{}=======", startTip + "结束");
        }
        return arr;
    }

    /**
     * 生成临时任务实例入口
     *
     * @return
     * <AUTHOR>
     * @params
     */
    @Override
    public List<JoblistActivityExampleVo> insertActivityInstanceMain(JoblistInsertParamsDto param) {
        AbstractJobGenerater abstractJobGenerater;
        if (param.getResponsibilityType() == 0) {
            //班组任务
            abstractJobGenerater = new GroupJobGenerater();
        } else {
            //例行任务
            abstractJobGenerater = new PostJobGenerrater();
        }
        abstractJobGenerater.setDataOrigin("temp");
        abstractJobGenerater.setInsertBeforeDel(true);
        List<JoblistActivityExampleVo> joblistActivityExampleVos = abstractJobGenerater.generateIrregular(param.getPropertiesIdList(), param.getShiftInfo());
        return joblistActivityExampleVos;
    }

    /**
     * 根据活动列表生成活动数据
     *
     * @return
     * <AUTHOR>
     * @date 2024/8/9
     * @params
     */
    @Override
    public JSONObject generaterJobByActivityProperties(Integer beforeHandDay, List<JoblistActivityProperties> joblistActivityPropertiesList, JSONObject result) {
        return this.generaterJobByActivityProperties(beforeHandDay, joblistActivityPropertiesList, result, true);
    }


    public JSONObject generaterJobByActivityProperties(Integer beforeHandDay, List<JoblistActivityProperties> joblistActivityPropertiesList, JSONObject result, Boolean useRecord) {
        return this.generaterJobByActivityProperties(beforeHandDay, joblistActivityPropertiesList, result, true,false);
    }
    /**
     * 这里仅提供当前天数 为基准的相对天数日期生成
     * 如果想指定某天生成，那么需要自行推算生成日期与当前时间的相对天数   推算方式   生成日期-当前日期 = 天数
     *
     * @return
     * <AUTHOR>
     * @date 2024/9/11
     * @params
     */

    @Override
    public JSONObject generaterJobByActivityProperties(Integer beforeHandDay,
                                                       List<JoblistActivityProperties> joblistActivityPropertiesList,
                                                       JSONObject result, Boolean useRecord, Boolean insertBeforeDel) {
        List<JoblistActivityProperties> postActivity = joblistActivityPropertiesList.stream().filter(item -> item.getResponsibilityType() == 1).collect(Collectors.toList());
        List<JoblistActivityProperties> groupActivity = joblistActivityPropertiesList.stream().filter(item -> item.getResponsibilityType() == 0).collect(Collectors.toList());
        if (StringUtils.isNotEmpty(postActivity)) {
            //岗位任务
            List<JoblistActivityExampleVo> instanceList = new ArrayList<>();
            PostJobGenerrater postJobGenerrater = new PostJobGenerrater();
            postJobGenerrater.setDataOrigin("inner");
            postJobGenerrater.setInsertBeforeDel(insertBeforeDel);
            try {
                instanceList = postJobGenerrater.generate(postActivity, beforeHandDay, useRecord);
            } catch (Exception e) {
                result.put("error", "岗位任务生成失败:" + e.getMessage());
            }
            result.put("postJob", "本次已生成岗位活动数量:" + instanceList.size());
        }
        if (StringUtils.isNotEmpty(groupActivity)) {
            //班组任务
            List<JoblistActivityExampleVo> instanceList = new ArrayList<>();
            GroupJobGenerater groupJobGenerater = new GroupJobGenerater();
            groupJobGenerater.setDataOrigin("inner");
            groupJobGenerater.setInsertBeforeDel(insertBeforeDel);
            try {
                instanceList = groupJobGenerater.generate(groupActivity, beforeHandDay, useRecord);
            } catch (Exception e) {
                result.put("error", "班组任务生成失败:" + e.getMessage());
            }
            result.put("groupJob", "本次已生成班组活动数量:" + instanceList.size());
        }
        return result;
    }

    /**
     * 根据活动配置id列表获取绑定关系
     *
     * @return
     * <AUTHOR>
     * @date 2024/7/5
     * @params
     */
    @Override
    public List<JoblistPersonBind> getActivityBind(List<String> idList, Integer bindType) {
        StandardJobLibQueryDto bindQueryDto = new StandardJobLibQueryDto();
        bindQueryDto.setBindtype(bindType);
        bindQueryDto.setPidList(idList);
        //获取绑定id
        List<JoblistPersonBind> bindList = jobLibConfigService.getBindIdListByBindType(bindQueryDto);
        return bindList;
    }

    /**
     * 获取表单任务列表  分页
     *
     * @param jobInstanceListDto
     * @return {@link List }<{@link JoblistActivityExampleVo }>
     */
    @Override
    public List<JoblistActivityExampleVo> getJobListFormList(JobInstanceListDto jobInstanceListDto) {
        List<JoblistActivityExampleVo> result = new ArrayList<>();
        if (ObjUtils.isEmpty(jobInstanceListDto)) {
            return result;
        }
        if (jobInstanceListDto.getJobType() == null) {
            //默认给岗位
            jobInstanceListDto.setJobType(1);
        }
        if (StringUtils.isEmpty(jobInstanceListDto.getOrgCode())) {
            if (StringUtils.isNotEmpty(jobInstanceListDto.getPersonId())) {
                List<String> ids = new ArrayList<>();
                ids.add(jobInstanceListDto.getPersonId());
                List<SysEmployeeOrgPost> employeeOrgPost = orgPostService.getEmployeeOrgPost(ids);
                if (StringUtils.isNotEmpty(employeeOrgPost)) {
                    jobInstanceListDto.setOrgCode(employeeOrgPost.get(0).getOrgcode());
                }
            } else {
                jobInstanceListDto.setOrgCode(SysUserHolder.getCurrentUser().getOrgId());
            }
        }
        if (StringUtils.isEmpty(jobInstanceListDto.getPersonId())) {
            jobInstanceListDto.setPersonId(SysUserHolder.getCurrentUser().getId());
        }
        List<JoblistActivityExample> parentList = this.getJoblistActivityExamplesByQuery(jobInstanceListDto);
        if (StringUtils.isEmpty(parentList)) {
            return result;
        }
        //根据主任务查询子任务
        List<String> parentIdList = parentList.stream().map(JoblistActivityExample::getId).collect(Collectors.toList());
        List<String> activityPropertiesIdList = parentList.stream().map(JoblistActivityExample::getActivityId).distinct().collect(Collectors.toList());
        Map<String, String> classNameMap = getClassNameMapByActivityId(activityPropertiesIdList);
        //获取子任务
        List<JoblistActivityExample> children = getActivityListByPidList(0, jobInstanceListDto.getJobType(), parentIdList);
        if (StringUtils.isEmpty(children)) {
            return result;
        }
        //获取核算对象名称
        Map<String, String> costMap = getCostUnitNameMap(activityPropertiesIdList);
        List<String> childrenIdList = children.stream().map(JoblistActivityExample::getId).collect(Collectors.toList());
        //查询实例表单绑定
        Map<String, List<JoblistExampleFormBind>> bindMap = getExampleFormBindMap(childrenIdList);
        //获取执行人 根据活动id
        List<JoblistInputMapping> workPersons = inputService.getJoblistInputDataByJobIdList(childrenIdList);
        Map<String, List<JoblistInputMapping>> jobWorkPersonMap = new HashMap<>();
        if (StringUtils.isNotEmpty(workPersons)) {
            jobWorkPersonMap = workPersons.stream().collect(Collectors.groupingBy(JoblistInputMapping::getMasterId));
        }
        //负责人员
        List<JobListExampleDutyPerson> dutyPersonList = this.getDutyPersonByActivityId(childrenIdList,
                jobInstanceListDto.getPersonId(),jobInstanceListDto);
        //全部人员组
        Map<String, List<JobListExampleDutyPerson>> allpersonGroup = new HashMap<>();
        if (StringUtils.isNotEmpty(dutyPersonList)) {
            allpersonGroup = dutyPersonList.stream().collect(Collectors.groupingBy(JobListExampleDutyPerson::getActivityExampleId));
        }
        // 与自己参与活动相关的父任务列表
        Set<String> currentParentIdList = new HashSet<>();
        for (JoblistActivityExample child : children) {
            Boolean isMine = false;
            JoblistActivityExampleVo childvo = ObjUtils.copyTo(child, JoblistActivityExampleVo.class);
            //表单绑定
            List<JoblistExampleFormBind> joblistExampleFormBinds = bindMap.get(childvo.getId());
            if (StringUtils.isNotEmpty(joblistExampleFormBinds)) {
                //获取表单实例对应的表单id
                Map<String, String> exampleFormDataIdMap = getExampleFormDataIdMap(childvo.getPid());
                for (JoblistExampleFormBind joblistExampleFormBind : joblistExampleFormBinds) {
                    String dataId = exampleFormDataIdMap.get(joblistExampleFormBind.getFormId());
                    joblistExampleFormBind.setDataId(dataId);
                }
                childvo.setFormBindList(joblistExampleFormBinds);
            } else {
                //此任务不是表单任务跳过
                continue;
            }
            //名称
            List<JobListExampleDutyPerson> jobListExampleDutyPeople = allpersonGroup.get(child.getId());
            if (StringUtils.isNotEmpty(jobListExampleDutyPeople)) {
                //仅自己可见
                currentParentIdList.add(child.getPid());
                isMine = true;
            }
            if (!isMine) continue;
            childvo.setNodeShowName(classNameMap.get(child.getActivityId()));
            //执行人 如果没有被分配则使用外部的填写人
            List<JobListExampleDutyPerson> workers = Optional.ofNullable(jobListExampleDutyPeople).orElse(new ArrayList<>())
                    .stream()
                    .filter(i -> i != null && i.getPersonType() == 1).collect(Collectors.toList());
            if (StringUtils.isNotEmpty(workers)) {
                //已分配
                List<JoblistInputMapping> workerList = new ArrayList<>();
                for (JobListExampleDutyPerson worker : workers) {
                    JoblistInputMapping mapping = new JoblistInputMapping();
                    mapping.setInputPersonId(worker.getPersonId());
                    mapping.setInputPersonName(worker.getPersonName());
                    mapping.setMasterId(worker.getActivityExampleId());
                    workerList.add(mapping);
                }
                childvo.setWorkPersonList(workerList);
            } else {
                //未分配使用实际填写人
                childvo.setWorkPersonList(jobWorkPersonMap.get(child.getId()));
            }
            mergeFrequenceInfo(child, costMap, childvo);
            //负责人
            List<JobListExampleDutyPerson> dutys = Optional.ofNullable(jobListExampleDutyPeople).orElse(new ArrayList<>())
                    .stream()
                    .filter(i -> i != null && i.getPersonType() == 0).collect(Collectors.toList());
            childvo.setDutyPersonList(dutys);
            childvo.setTempDate(childvo.getActivityDate());
            result.add(childvo);
        }

        //分页
        if (jobInstanceListDto.getPageSize() != 0 && StringUtils.isNotEmpty(result)) {
            result = result.stream().skip((jobInstanceListDto.getPageNum() - 1) * jobInstanceListDto.getPageSize())
                    .limit(jobInstanceListDto.getPageSize()).collect(Collectors.toList());
        }
        //排序整理数据
        return sortedJob(result);
    }

    /**
     * 根据用户获取其任务列表
     *
     * @param jobInstanceListDto
     * @return {@link List }<{@link JoblistActivityExampleVo }>
     */
    @Override
    public List<JoblistActivityExampleVo> getJobListByUser(JobInstanceListDto jobInstanceListDto) {
        List<JoblistActivityExampleVo> result = new ArrayList<>();
        if (ObjUtils.isEmpty(jobInstanceListDto)) {
            return result;
        }
        if (jobInstanceListDto.getJobType() == null) {
            //默认给岗位
            jobInstanceListDto.setJobType(1);
        }
        if (StringUtils.isEmpty(jobInstanceListDto.getOrgCode())) {
            if (StringUtils.isNotEmpty(jobInstanceListDto.getPersonId())) {
                List<String> ids = new ArrayList<>();
                ids.add(jobInstanceListDto.getPersonId());
                List<SysEmployeeOrgPost> employeeOrgPost = orgPostService.getEmployeeOrgPost(ids);
                if (StringUtils.isNotEmpty(employeeOrgPost)) {
                    jobInstanceListDto.setOrgCode(employeeOrgPost.get(0).getOrgcode());
                }
            } else {
                jobInstanceListDto.setOrgCode(SysUserHolder.getCurrentUser().getOrgId());
            }
        }
        if (StringUtils.isEmpty(jobInstanceListDto.getPersonId())) {
            jobInstanceListDto.setPersonId(SysUserHolder.getCurrentUser().getId());
        }
        List<JoblistActivityExample> parentList = this.getJoblistActivityExamplesByQuery(jobInstanceListDto);
        if (StringUtils.isEmpty(parentList)) {
            return result;
        }
        //根据主任务查询子任务
        List<String> parentIdList = parentList.stream().map(JoblistActivityExample::getId).collect(Collectors.toList());
        List<String> activityPropertiesIdList = parentList.stream().map(JoblistActivityExample::getActivityId).distinct().collect(Collectors.toList());
        Map<String, String> classNameMap = getClassNameMapByActivityId(activityPropertiesIdList);
        //获取子任务
        List<JoblistActivityExample> children = getActivityListByPidList(0, jobInstanceListDto.getJobType(), parentIdList);
        if (StringUtils.isEmpty(children)) {
            return result;
        }
        //获取核算对象名称
        Map<String, String> costMap = getCostUnitNameMap(activityPropertiesIdList);
        List<String> childrenIdList = children.stream().map(JoblistActivityExample::getId).collect(Collectors.toList());
        //查询实例表单绑定
        Map<String, List<JoblistExampleFormBind>> bindMap = getExampleFormBindMap(childrenIdList);
        //获取执行人 根据活动id
        List<JoblistInputMapping> workPersons = inputService.getJoblistInputDataByJobIdList(childrenIdList);
        Map<String, List<JoblistInputMapping>> jobWorkPersonMap = new HashMap<>();
        if (StringUtils.isNotEmpty(workPersons)) {
            jobWorkPersonMap = workPersons.stream().collect(Collectors.groupingBy(JoblistInputMapping::getMasterId));
        }
        //负责人员
        List<JobListExampleDutyPerson> dutyPersonList = this.getDutyPersonByActivityId(childrenIdList,
                jobInstanceListDto.getPersonId(),jobInstanceListDto);
        //全部人员组
        Map<String, List<JobListExampleDutyPerson>> allpersonGroup = new HashMap<>();
        if (StringUtils.isNotEmpty(dutyPersonList)) {
            allpersonGroup = dutyPersonList.stream().collect(Collectors.groupingBy(JobListExampleDutyPerson::getActivityExampleId));
        }
        List<JoblistActivityExampleVo> childrenVo = new ArrayList<>();
        // 与自己参与活动相关的父任务列表
        Set<String> currentParentIdList = new HashSet<>();
        for (JoblistActivityExample child : children) {
            boolean isMine = false;
            JoblistActivityExampleVo childvo = ObjUtils.copyTo(child, JoblistActivityExampleVo.class);
            //名称
            List<JobListExampleDutyPerson> jobListExampleDutyPeople = allpersonGroup.get(child.getId());
            if (StringUtils.isNotEmpty(jobListExampleDutyPeople)) {
                //仅自己可见
                currentParentIdList.add(child.getPid());
                isMine = true;
            }
            if (!isMine) {
                continue;
            }
            childvo.setNodeShowName(classNameMap.get(child.getActivityId()));
            //执行人 如果没有被分配则使用外部的填写人
            List<JobListExampleDutyPerson> workers = Optional.ofNullable(jobListExampleDutyPeople).orElse(new ArrayList<>())
                    .stream()
                    .filter(i -> i != null && i.getPersonType() == 1).collect(Collectors.toList());
            if (StringUtils.isNotEmpty(workers)) {
                //已分配
                List<JoblistInputMapping> workerList = new ArrayList<>();
                for (JobListExampleDutyPerson worker : workers) {
                    JoblistInputMapping mapping = new JoblistInputMapping();
                    mapping.setInputPersonId(worker.getPersonId());
                    mapping.setInputPersonName(worker.getPersonName());
                    mapping.setMasterId(worker.getActivityExampleId());
                    workerList.add(mapping);
                }
                childvo.setWorkPersonList(workerList);
            } else {
                //未分配使用实际填写人
                childvo.setWorkPersonList(jobWorkPersonMap.get(child.getId()));
            }
            mergeFrequenceInfo(child, costMap, childvo);
            //负责人
            List<JobListExampleDutyPerson> dutys = Optional.ofNullable(jobListExampleDutyPeople).orElse(new ArrayList<>())
                    .stream()
                    .filter(i -> i != null && i.getPersonType() == 0).collect(Collectors.toList());
            childvo.setDutyPersonList(dutys);
            //表单绑定
            mergeFormBindInfo(bindMap, childvo);

            childrenVo.add(childvo);
        }
        //按照父id 进行分组
        Map<String, List<JoblistActivityExampleVo>> map = childrenVo.stream().collect(Collectors.groupingBy(JoblistActivityExample::getPid));
        for (JoblistActivityExample parent : parentList) {
            if (!map.containsKey(parent.getId())) {
                //父活动中不包含子活动
                continue;
            }
            if (StringUtils.isEmpty(currentParentIdList)) {
                continue;
            }
            //当前父任务id 不为空
            if (!currentParentIdList.contains(parent.getId())) {
                //不包含
                continue;
            }
            JoblistActivityExampleVo pvo = ObjUtils.copyTo(parent, JoblistActivityExampleVo.class);
            //名称
            pvo.setActivityName(costMap.get(parent.getActivityId()));
            List<JoblistActivityExampleVo> childList = map.get(parent.getId());
            pvo.setChildrenTask(childList);
            //未反馈子记录的第一个
            JoblistActivityExampleVo childUnfeed = childList.stream()
                    .filter(i -> i.getActivityStatus() < 2).findFirst().orElseGet(() -> childList.get(0));
            pvo.setTempDate(childUnfeed == null ?
                    pvo.getChildrenTask().get(0).getActivityDate() : childUnfeed.getActivityDate());
            pvo.setNodeShowName(classNameMap.get(parent.getActivityId()));
            result.add(pvo);
        }
        //排序整理数据
        return sortedJob(result);
    }

    /**
     * 获取任务清单  包含子任务
     *
     * @return jobInstanceListDto:{
     * jobName : 0班组 1岗位
     * }
     * <AUTHOR>
     * @date 2024/6/27
     * @params
     */
    @Override
    public List<JoblistActivityExampleVo> getJobList(JobInstanceListDto jobInstanceListDto) {
        List<JoblistActivityExampleVo> result = new ArrayList<>();
        if (ObjUtils.isEmpty(jobInstanceListDto)) {
            return result;
        }
        if (jobInstanceListDto.getIsMine() == null) {
            jobInstanceListDto.setIsMine(0);
        }
        if (jobInstanceListDto.getJobType() == null) {
            //默认给岗位
            jobInstanceListDto.setJobType(1);
        }
        if (StringUtils.isEmpty(jobInstanceListDto.getOrgCode())) {
            jobInstanceListDto.setOrgCode(SysUserHolder.getCurrentUser().getOrgId());
        }
        if (StringUtils.isEmpty(jobInstanceListDto.getPersonId())) {
            jobInstanceListDto.setPersonId(SysUserHolder.getCurrentUser().getId());
        }
//        Map<String, String> classMap = Optional.ofNullable(classList).orElse(new ArrayList<>()).stream().collect(Collectors.toMap(JoblistClassVo::getNodeId, JoblistClassVo::getNodeShowName));
        //是否有全部权限
        Boolean isAllpermission = this.checkPermission();
        if(isAllpermission){
            //有全部权限
            SysOrg orgById = orgService.findOrgById(jobInstanceListDto.getOrgCode());
            String orgType = orgById.getOrgType();
            if(!"shiftteam".equals(orgType)){
                //如果不是是班组是活动的管理机构
                jobInstanceListDto.setP_orgCode(jobInstanceListDto.getOrgCode());
            }
        }
        //获取父任务列表
        List<JoblistActivityExample> parentList = getJoblistActivityExamplesByQuery(jobInstanceListDto);
        if (StringUtils.isEmpty(parentList)) {
            return result;
        }
        List<String> parentIdList = parentList.stream().map(JoblistActivityExample::getId).collect(Collectors.toList());
        //获取子任务列表
        List<JoblistActivityExample> children = getActivityListByPidList(0, jobInstanceListDto.getJobType(), parentIdList);
        if (StringUtils.isEmpty(children)) {
            return result;
        }
        //处理父子活动列表转换成可用数据
        result = mergeActivity(jobInstanceListDto, parentList,children, result, isAllpermission);
        //排序整理数据
        return sortedJob(result);
    }

    /**
     * 处理父子活动列表转换成可用数据
     * <AUTHOR>
     * @date 2025/6/12
     * @params
     * @return
     *
    */
    private List<JoblistActivityExampleVo> mergeActivity(JobInstanceListDto jobInstanceListDto, List<JoblistActivityExample> parentList, List<JoblistActivityExample> children, List<JoblistActivityExampleVo> result, Boolean isAllpermission) {
        //根据主任务查询子任务
        List<String> activityPropertiesIdList = parentList.stream().map(JoblistActivityExample::getActivityId).distinct().collect(Collectors.toList());
        Map<String, String> classNameMap = getClassNameMapByActivityId(activityPropertiesIdList);
        //获取核算对象名称
        Map<String, String> costMap = getCostUnitNameMap(activityPropertiesIdList);
        List<String> childrenIdList = children.stream().map(JoblistActivityExample::getId).collect(Collectors.toList());
        //查询实例表单绑定
        Map<String, List<JoblistExampleFormBind>> bindMap = getExampleFormBindMap(childrenIdList);
        //负责人员
        Map<String, List<JobListExampleDutyPerson>> allpersonGroup = getDutyPersonByActivity(jobInstanceListDto, childrenIdList);
        List<JoblistActivityExampleVo> childrenVo = new ArrayList<>();
        //合并处理父子任务数据
        // 与自己参与活动相关的父任务列表
        Set<String> currentParentIdList = new HashSet<>();
        //构建子活动信息
        buildChildrenActivityInfo(jobInstanceListDto, children, allpersonGroup, isAllpermission, currentParentIdList, costMap, classNameMap, bindMap, childrenVo);
        //按照父id 进行分组
        Map<String, List<JoblistActivityExampleVo>> map = childrenVo.stream().collect(Collectors.groupingBy(JoblistActivityExample::getPid));
        //构建父活动信息
        buildParentActivityInfo(parentList, map, isAllpermission, currentParentIdList, costMap, classNameMap, result);
        //获取活动相关操作卡信息
        getOperCardInfoWithInActivity(result);
        return result;
    }

    /**
     * 构建父活动数据
     * <AUTHOR>
     * @date 2025/6/12
     * @params
     * @return
     *
    */
    private static void buildParentActivityInfo(List<JoblistActivityExample> parentList, Map<String, List<JoblistActivityExampleVo>> map, Boolean isAllpermission, Set<String> currentParentIdList, Map<String, String> costMap, Map<String, String> classNameMap, List<JoblistActivityExampleVo> result) {
        for (JoblistActivityExample parent : parentList) {
            if (!map.containsKey(parent.getId())) {
                //父活动中不包含子活动
                continue;
            }
            if (!isAllpermission) {
                //没有全部权限
                if (StringUtils.isNotEmpty(currentParentIdList)) {
                    //当前父任务id 不为空
                    if (!currentParentIdList.contains(parent.getId())) {
                        //不包含
                        continue;
                    }
                }
            }
            JoblistActivityExampleVo vo = buildParentActivityData(map, costMap, classNameMap, parent);
            result.add(vo);
        }
    }

    /**
     * 构建父活动详细数据
     * <AUTHOR>
     * @date 2025/6/12
     * @params
     * @return
     *
    */
    private static JoblistActivityExampleVo buildParentActivityData(Map<String, List<JoblistActivityExampleVo>> map, Map<String, String> costMap, Map<String, String> classNameMap, JoblistActivityExample parent) {
        JoblistActivityExampleVo vo = ObjUtils.copyTo(parent, JoblistActivityExampleVo.class);
        //名称
        vo.setActivityName(costMap.get(parent.getActivityId()));
        List<JoblistActivityExampleVo> childList = map.get(parent.getId());
        vo.setChildrenTask(childList);
        //未反馈子记录的第一个
        JoblistActivityExampleVo childUnfeed = childList.stream()
                .filter(i -> i.getActivityStatus()!=null && i.getActivityStatus() < 2).findFirst().orElseGet(() -> childList.get(0));
        vo.setTempDate(childUnfeed == null ?
                vo.getChildrenTask().get(0).getActivityDate() : childUnfeed.getActivityDate());
        vo.setNodeShowName(classNameMap.get(parent.getActivityId()));
        return vo;
    }

    /**
     * 构建子活动数据
     * <AUTHOR>
     * @date 2025/6/12
     * @params
     * @return
    */
    private void buildChildrenActivityInfo(JobInstanceListDto jobInstanceListDto, List<JoblistActivityExample> children, Map<String, List<JobListExampleDutyPerson>> allpersonGroup, Boolean isAllpermission, Set<String> currentParentIdList, Map<String, String> costMap, Map<String, String> classNameMap, Map<String, List<JoblistExampleFormBind>> bindMap, List<JoblistActivityExampleVo> childrenVo) {
        for (JoblistActivityExample child : children) {
            JoblistActivityExampleVo childvo = ObjUtils.copyTo(child, JoblistActivityExampleVo.class);
            if (jobInstanceListDto.getIsMine() == 1) {
                //仅我相关  如果没找到就是
                if (!allpersonGroup.containsKey(child.getId()))
                    continue;
            }
            //合并参与人员信息
            mergeDutyPersonInfo(jobInstanceListDto, child, allpersonGroup, isAllpermission, currentParentIdList, childvo);
            //合并频次信息
            mergeFrequenceInfo(child, costMap, childvo);
            childvo.setNodeShowName(classNameMap.get(child.getActivityId()));
            //合并表单绑定信息
            mergeFormBindInfo(bindMap, childvo);
            childrenVo.add(childvo);
        }
    }

    /**
     * 活动责任人信息合并
     * <AUTHOR>
     * @date 2025/6/12
     * @params
     * @return
     *
    */
    private static void mergeDutyPersonInfo(JobInstanceListDto jobInstanceListDto, JoblistActivityExample child, Map<String, List<JobListExampleDutyPerson>> allpersonGroup, Boolean isAllpermission, Set<String> currentParentIdList, JoblistActivityExampleVo childvo) {
        List<JobListExampleDutyPerson> jobListExampleDutyPeople = allpersonGroup.get(child.getId());
        if (!isAllpermission) {
            //没有全部权限 //
            if (StringUtils.isEmpty(jobListExampleDutyPeople)) {
                //未配置责任人全部人可见
                currentParentIdList.add(child.getPid());
            } else if (child.getResponsibilityType() != 0) {
                //岗位工作
                //已配置责任人应该只有责任人可见
                long count = Optional.ofNullable(jobListExampleDutyPeople)
                        .orElse(new ArrayList<>())
                        .stream()
                        .filter(i -> jobInstanceListDto.getPersonId().equals(i.getPersonId())).count();
                if (count > 0) {
                    currentParentIdList.add(child.getPid());
                }
            } else {
                //班组工作直接可见
                currentParentIdList.add(child.getPid());
            }
        }
        //反馈人
        List<JobListExampleDutyPerson> workers = Optional.ofNullable(jobListExampleDutyPeople).orElse(new ArrayList<>())
                .stream()
                .filter(i -> i != null && i.getPersonType() == 1)
                .sorted(Comparator.comparing(JobListExampleDutyPerson::getCreateTime))
                .collect(Collectors.toList());
        if (StringUtils.isNotEmpty(workers)) {
            //已分配
            List<JoblistInputMapping> workerList = new ArrayList<>();
            JobListExampleDutyPerson worker = workers.get(workers.size() - 1);
            JoblistInputMapping mapping = new JoblistInputMapping();
            mapping.setInputPersonId(worker.getPersonId());
            mapping.setInputPersonName(worker.getPersonName());
            mapping.setMasterId(worker.getActivityExampleId());
            workerList.add(mapping);
            childvo.setWorkPersonList(workerList);
        }
        List<JobListExampleDutyPerson> dutys = Optional.ofNullable(jobListExampleDutyPeople).orElse(new ArrayList<>())
                .stream()
                .filter(i -> i != null && i.getPersonType() == 0).collect(Collectors.toList());
        childvo.setDutyPersonList(dutys);
    }

    /**
     * 合并活动频次信息
     * <AUTHOR>
     * @date 2025/6/12
     * @params
     * @return
     *
    */
    private static void mergeFrequenceInfo(JoblistActivityExample child, Map<String, String> costMap, JoblistActivityExampleVo childvo) {
        String activityName = "";
        if (StringUtils.isNotEmpty(costMap) && costMap.containsKey(child.getActivityId())) {
            Integer fType = childvo.getFrequencyType() == null ? 0 : childvo.getFrequencyType();
            if (1 == fType) {
                //次数频次
                activityName = costMap.get(child.getActivityId()) + "(第" + childvo.getJobNo() + "次)";
            } else {
                //时间频次
                activityName = costMap.get(child.getActivityId());
            }
        }
        childvo.setActivityName(activityName);
    }

    /**
     * 合并活动表单绑定信息
     * <AUTHOR>
     * @date 2025/6/12
     * @params
     * @return
     *
    */
    private void mergeFormBindInfo(Map<String, List<JoblistExampleFormBind>> bindMap, JoblistActivityExampleVo childvo) {
        //活动表单绑定信息
        List<JoblistExampleFormBind> joblistExampleFormBinds = bindMap.get(childvo.getId());
        if (StringUtils.isNotEmpty(joblistExampleFormBinds)) {
            //获取表单实例对应的表单id
            Map<String, String> exampleFormDataIdMap = getExampleFormDataIdMap(childvo.getPid());
            for (JoblistExampleFormBind joblistExampleFormBind : joblistExampleFormBinds) {
                String dataId = exampleFormDataIdMap.get(joblistExampleFormBind.getFormId());
                joblistExampleFormBind.setDataId(dataId);
            }
            childvo.setFormBindList(joblistExampleFormBinds);
        }
    }
    /**
     * 获取活动操作卡绑定信息  将活动操作卡绑定信息开发代码进行原样封装
     * <AUTHOR>
     * @date 2025/6/12
     * @params
     * @return
     *
    */
    private void getOperCardInfoWithInActivity(List<JoblistActivityExampleVo> result) {
        //获取任务活动操作卡绑定关系列表
        List<JoblistOperCardBind> operCardBindList = methodService.getJoblistOperCardBindList(null);
        Map<String, JoblistOperCardBind> targetCardMap = operCardBindList.stream()
                .collect(Collectors.toMap(JoblistOperCardBind::getActivityId, target -> target));

        Where where = Where.create();
        where.eq(JoblistInputMapping::getMapType,"opercard_activity");
//        where.eq(JoblistInputMapping::getMasterId,dto.getPid());
//        where.eq(JoblistInputMapping::getSlaveId,initResult.getExecId());
        List<JoblistInputMapping> inputList=dao.queryData(JoblistInputMapping.class,where,Order.create(),null);
        Map<String, JoblistInputMapping> targetMap = inputList.stream()
                .collect(Collectors.toMap(JoblistInputMapping::getMasterId, target -> target));

        List<JoblistActivityExampleBind> operJoblistBindList = methodService.getJoblistExampleBinds();
        Map<String, JoblistActivityExampleBind> targetExampleBindMap = operJoblistBindList.stream()
                .collect(Collectors.toMap(JoblistActivityExampleBind::getActivityExampleId, target -> target));
        // 使用Lambda表达式遍历sourceList并赋值
        result.forEach(source -> {
            JoblistInputMapping target = targetMap.get(source.getId());
            JoblistOperCardBind jlocb=targetCardMap.get(source.getActivityId());
            JoblistActivityExampleBind targetExampleBind = targetExampleBindMap.get(source.getId());
            if (jlocb != null) {
                source.setCardMainId(jlocb.getId());
                source.setCardId(jlocb.getCardId()); // 假设将Target.value赋值给Source.name
                source.setCardName(jlocb.getCardName());
                source.setCatalogAlias(jlocb.getCatalogAlias());
                source.setCatalogName(jlocb.getCatalogName());
                source.setExecType(jlocb.getExecType());
                if(target!=null){
                    source.setCardInstanceId(target.getSlaveId());
                }

                if(source.getChildrenTask()!=null&&!source.getChildrenTask().isEmpty()){
                    source.getChildrenTask().forEach(childTask -> {
                        childTask.setCardMainId(jlocb.getId());
                        childTask.setCardId(jlocb.getCardId()); // 假设将Target.value赋值给Source.name
                        childTask.setCardName(jlocb.getCardName());
                        childTask.setCatalogAlias(jlocb.getCatalogAlias());
                        childTask.setCatalogName(jlocb.getCatalogName());
                        childTask.setExecType(jlocb.getExecType());
                        if(target!=null){
                            childTask.setCardInstanceId(target.getSlaveId());
                        }

//                        childTask.setExecType(target.getExecType());
                    });
                }
            }
            if (targetExampleBind != null) {
                source.setStatusKey(targetExampleBind.getStatusKey()); // 假设将Target.value赋值给Source.name
                source.setStatusKeyName(targetExampleBind.getStatusKeyName());
                source.setStatusValue(targetExampleBind.getStatusValue()); // 假设将Target.value赋值给Source.name
                source.setStatusValueName(targetExampleBind.getStatusValueName());
                if(source.getChildrenTask()!=null&&!source.getChildrenTask().isEmpty()){
                    source.getChildrenTask().forEach(childTask -> {
                        childTask.setStatusKey(targetExampleBind.getStatusKey()); // 假设将Target.value赋值给Source.name
                        childTask.setStatusKeyName(targetExampleBind.getStatusKeyName());
                        childTask.setStatusValue(targetExampleBind.getStatusValue()); // 假设将Target.value赋值给Source.name
                        childTask.setStatusValueName(targetExampleBind.getStatusValueName());
                    });
                }
            }

        });
    }

    /**
     * 获取活动相关责任人列表
     * <AUTHOR>
     * @date 2025/6/12
     * @params
     * @return
     *
    */
    private Map<String, List<JobListExampleDutyPerson>> getDutyPersonByActivity(JobInstanceListDto jobInstanceListDto, List<String> childrenIdList) {
        List<JobListExampleDutyPerson> dutyPersonList;
        if (jobInstanceListDto.getIsMine() == 0) {
            dutyPersonList = this.getDutyPersonByActivityId(childrenIdList, null, jobInstanceListDto);
        } else {
            dutyPersonList = this.getDutyPersonByActivityId(childrenIdList, jobInstanceListDto.getPersonId(), jobInstanceListDto);
        }

        //全部人员组
        Map<String, List<JobListExampleDutyPerson>> allpersonGroup = new HashMap<>();
        if (StringUtils.isNotEmpty(dutyPersonList)) {
            allpersonGroup =
                    dutyPersonList.stream().collect(Collectors.groupingBy(JobListExampleDutyPerson::getActivityExampleId
                            ,Collectors.collectingAndThen(
                                    Collectors.toList(),
                                    list-> list
                                            .stream().sorted(Comparator.comparing(JobListExampleDutyPerson::getTmSort
            )).collect(Collectors.toList())))
                    );
        }
        return allpersonGroup;
    }

    /**
     * 获取活动名称映射
     * <AUTHOR>
     * @date 2025/6/12
     * @params
     * @return
     *
    */
    private Map<String, String> getCostUnitNameMap(List<String> activityPropertiesIdList) {
        //获取核算对象名称
        List<Costuint> costuintList = new ArrayList<>();
        List<String> unFindId = new ArrayList<>();
        for (String id : activityPropertiesIdList) {
            String redisKey = JoblistMethodServiceImpl.ACTIVITYPROPERTIES_COST_PREKEY+id;
            if(redisUtil.hasKey(redisKey)){
                Costuint bean = redisUtil.getClassObject(Costuint.class, redisKey);
                if(bean!=null){
                    costuintList.add(bean);
                }
            }else{
                //可能数据库里有
                unFindId.add(id);
            }
        }
        if(StringUtils.isNotEmpty(unFindId)){
            MethodQueryDto queryDto = new MethodQueryDto();
            queryDto.setIdList(unFindId);
            List<Costuint> dataList = unitMethodService.getCostuintList(queryDto);
            if(StringUtils.isNotEmpty(dataList)){
                costuintList.addAll(dataList);
                for (Costuint costuint : costuintList) {
                    String redisKey = JoblistMethodServiceImpl.ACTIVITYPROPERTIES_COST_PREKEY+costuint.getId();
                    redisUtil.setObject( redisKey, costuint,JoblistMethodServiceImpl.CACHE_TIME_OUT);
                }
            }
        }
        Map<String, String> costMap = new HashMap<>();
        if (StringUtils.isNotEmpty(costuintList)) {
            costMap = costuintList.stream().collect(Collectors.toMap(Costuint::getId, Costuint::getName, (k1, k2) -> k1));
        }
        return costMap;
    }

    /**
     * 查询工作任务情况
     *
     * @return
     * <AUTHOR>
     * @date 2025/3/7
     * @params
     */
    @Override
    public JobFinishDto getJobFinishCase(JobInstanceListDto jobInstanceListDto) {
        JobFinishDto result = new JobFinishDto();
        result.setFinished(0);
        result.setTotal(0);
        result.setUnfinish(0);
        if (ObjUtils.isEmpty(jobInstanceListDto)) {
            return result;
        }
        if (jobInstanceListDto.getIsMine() == null) {
            jobInstanceListDto.setIsMine(0);
        }
        if (jobInstanceListDto.getJobType() == null) {
            //默认给岗位
            jobInstanceListDto.setJobType(1);
        }
        if (StringUtils.isEmpty(jobInstanceListDto.getOrgCode())) {
            jobInstanceListDto.setOrgCode(SysUserHolder.getCurrentUser().getOrgId());
        }
        if (StringUtils.isEmpty(jobInstanceListDto.getPersonId())) {
            jobInstanceListDto.setPersonId(SysUserHolder.getCurrentUser().getId());
        }
        //是否有全部权限
        Boolean isAllpermission = this.checkPermission();
        if(isAllpermission){
            //有全部权限
            SysOrg orgById = orgService.findOrgById(jobInstanceListDto.getOrgCode());
            String orgType = orgById.getOrgType();
            if(!"shiftteam".equals(orgType)){
                //如果不是是班组是活动的管理机构
                jobInstanceListDto.setP_orgCode(jobInstanceListDto.getOrgCode());
            }
        }
//        long i =System.currentTimeMillis();
        List<JoblistActivityExample> parentList = getJoblistActivityExamplesByQuery(jobInstanceListDto);
        if (StringUtils.isEmpty(parentList)) {
            return result;
        }
//        System.out.println("步骤1："+(System.currentTimeMillis()-i));
//        i =System.currentTimeMillis();
        //根据主任务查询子任务
        List<String> parentIdList = parentList.stream().map(JoblistActivityExample::getId).collect(Collectors.toList());
        //获取子任务
        List<JoblistActivityExample> children = getActivityListByPidList(0, jobInstanceListDto.getJobType(), parentIdList);
        if (StringUtils.isEmpty(children)) {
            return result;
        }
//        System.out.println("步骤2："+(System.currentTimeMillis()-i));
//        i =System.currentTimeMillis();
        List<String> childrenIdList = children.stream().map(JoblistActivityExample::getId).collect(Collectors.toList());
        //获取活动相关人员信息
        Map<String, List<JobListExampleDutyPerson>> allpersonGroup = getDutyPersonForExample(jobInstanceListDto, childrenIdList);
        List<JoblistActivityExampleVo> childrenVo = new ArrayList<>();
        // 与自己参与活动相关的父任务列表
//        System.out.println("步骤3："+(System.currentTimeMillis()-i));
//        i =System.currentTimeMillis();
        Set<String> currentParentIdList = getMinePExampleList(jobInstanceListDto, children, allpersonGroup, isAllpermission, childrenVo);
        //按照父id 进行分组
        Map<String, List<JoblistActivityExampleVo>> map = childrenVo.stream().collect(Collectors.groupingBy(JoblistActivityExample::getPid));
        List<JoblistActivityExample> finalList = getMineExampleListExcluedEmpty(parentList, map, isAllpermission, currentParentIdList);
       // System.out.println("步骤4："+(System.currentTimeMillis()-i));
        //统计数量
        result.setTotal(finalList.size());
        result.setFinished((int) finalList.stream().filter(item -> item.getActivityStatus() != null && item.getActivityStatus() == 2).count());
        result.setUnfinish(result.getTotal() - result.getFinished());
        return result;
    }

    private static List<JoblistActivityExample> getMineExampleListExcluedEmpty(List<JoblistActivityExample> parentList, Map<String, List<JoblistActivityExampleVo>> map, Boolean isAllpermission, Set<String> currentParentIdList) {
        List<JoblistActivityExample> finalList = new ArrayList<>();
        for (JoblistActivityExample parent : parentList) {
            if (!map.containsKey(parent.getId())) {
                //父活动中不包含子活动
                continue;
            }
            if (!isAllpermission) {
                //没有全部权限
                if (StringUtils.isNotEmpty(currentParentIdList)) {
                    //当前父任务id 不为空
                    if (!currentParentIdList.contains(parent.getId())) {
                        //不包含
                        continue;
                    }
                }
            }
            finalList.add(parent);
        }
        return finalList;
    }

    /**
     * 与自己参与活动相关的父任务列表
     * <AUTHOR>
     * @date 2025/6/13
     * @params
     * @return
     *
    */
    private static Set<String> getMinePExampleList(JobInstanceListDto jobInstanceListDto, List<JoblistActivityExample> children, Map<String, List<JobListExampleDutyPerson>> allpersonGroup, Boolean isAllpermission, List<JoblistActivityExampleVo> childrenVo) {
        Set<String> currentParentIdList = new HashSet<>();
        for (JoblistActivityExample child : children) {
            JoblistActivityExampleVo childvo = ObjUtils.copyTo(child, JoblistActivityExampleVo.class);
            if (jobInstanceListDto.getIsMine() == 1) {
                //仅我相关  如果没找到就是
                if (!allpersonGroup.containsKey(child.getId()))
                    continue;
            }
            //名称
            List<JobListExampleDutyPerson> jobListExampleDutyPeople = allpersonGroup.get(child.getId());
            if (!isAllpermission) {
                //没有全部权限 //
                if (StringUtils.isEmpty(jobListExampleDutyPeople)) {
                    //未配置责任人全部人可见
                    currentParentIdList.add(child.getPid());
                } else {
                    //已配置责任人应该只有责任人可见
                    long count = Optional.ofNullable(jobListExampleDutyPeople)
                            .orElse(new ArrayList<>())
                            .stream()
                            .filter(i -> jobInstanceListDto.getPersonId().equals(i.getPersonId())).count();
                    if (count > 0) {
                        currentParentIdList.add(child.getPid());
                    }
                }
            }
            childrenVo.add(childvo);
        }
        return currentParentIdList;
    }

    /**
     * 获取活动相关人员信息
     * <AUTHOR>
     * @date 2025/6/13
     * @params
     * @return
     *
    */
    private Map<String, List<JobListExampleDutyPerson>> getDutyPersonForExample(JobInstanceListDto jobInstanceListDto, List<String> childrenIdList) {
        //负责人员
        List<JobListExampleDutyPerson> dutyPersonList;
        if (jobInstanceListDto.getIsMine() == 0) {
            dutyPersonList = this.getDutyPersonByActivityId(childrenIdList, null, jobInstanceListDto);
        } else {
            dutyPersonList = this.getDutyPersonByActivityId(childrenIdList, jobInstanceListDto.getPersonId(), jobInstanceListDto);
        }
        //全部人员组
        Map<String, List<JobListExampleDutyPerson>> allpersonGroup = new HashMap<>();
        if (StringUtils.isNotEmpty(dutyPersonList)) {
            allpersonGroup = dutyPersonList.stream().collect(Collectors.groupingBy(JobListExampleDutyPerson::getActivityExampleId));
        }
        return allpersonGroup;
    }

    /**
     * 通过班次等查询条件查询主任务
     *
     * @param jobInstanceListDto
     * @return {@link List }<{@link JoblistActivityExample }>
     */
    @Override
    public List<JoblistActivityExample> getJoblistActivityExamplesByQuery(JobInstanceListDto jobInstanceListDto) {
        if(StringUtils.isNotEmpty(jobInstanceListDto.getSpecial())){
            //根据专业查询
            StringBuffer sql = new StringBuffer();
            sql.append("select DISTINCT A.ID AS id from joblist_activityproperties A INNER JOIN JOBLIST_PERSONBIND B " +
                    "on B.bindtype=3 and B.pid = A.ID and B.tmused=1 ");
            sql.append("INNER JOIN JOBLIST_EXTPROPERTIESCONFIG C ON C.PTYPE = 0 and C.tmused =1  and C.id = B.BINDID " +
                    "and C.PNAME  like ?");
            List<Object> values = new ArrayList<>();
            values.add("%" + jobInstanceListDto.getSpecial()+"%");
            List<LinkedHashMap<String, Object>> query = dao.query(sql.toString(), values);
            List<String> collect = query.stream().filter(item -> item.get("id") != null)
                    .map(item -> String.valueOf(item.get("id"))).collect(Collectors.toList());
            if(StringUtils.isNotEmpty(collect)){
                jobInstanceListDto.setActivityIdList(collect);
            }
        }
        List<String> ledgerActivityIds = null;
        if (jobInstanceListDto.getJobType() != null) {
            if (jobInstanceListDto.getJobType() == 2) {
                //需要仅查询运行台账
                StandardJobLibQueryDto queryDto = new StandardJobLibQueryDto();
                queryDto.setTmUsed(null);
                queryDto.setIsAccountActivity(1);
                List<JoblistActivityProperties> joblistActivityPropertiesList = methodService.getJoblistActivityPropertiesList(queryDto);
                if (StringUtils.isNotEmpty(joblistActivityPropertiesList)) {
                    ledgerActivityIds =
                            joblistActivityPropertiesList.stream().map(JoblistActivityProperties::getId).distinct().collect(Collectors.toList());
                }
            }
        }
        //从缓存中获取
        List<JoblistActivityExample> jobList = loadPActivityFromCache(jobInstanceListDto, ledgerActivityIds);
        if(StringUtils.isNotEmpty(jobList)){
            return jobList;
        }else{
            //从数据库中获取
            return getJoblistActivityExamplesFromDb(jobInstanceListDto,ledgerActivityIds);
        }
    }

    private List<JoblistActivityExample> loadPActivityFromCache(JobInstanceListDto jobInstanceListDto, List<String> ledgerActivityIds) {
//        long i = System.currentTimeMillis();
    	String redisKey =
                JoblistActivityCacheUtils.ACTIVITY_BEAN_KEY_PREFIX+jobInstanceListDto.getOrgCode()+":*:"+ jobInstanceListDto.getShiftId()+":"+ jobInstanceListDto.getSbsj()+":*";
    	Collection<String> keys = redisUtil.keys(redisKey);
        List<JoblistActivityExample> jobList = new ArrayList<>();
        if(StringUtils.isNotEmpty(keys)){
//            for (String key : keys) {
//                Object object = redisUtil.getObject(key);
//                JoblistActivityExample joblistActivityExample = ObjUtils.convertTo(object, JoblistActivityExample.class);
//                jobList.add(joblistActivityExample);
//            }
        	  List<String> keyList = new ArrayList<String>();
        	  keyList.addAll(keys);
        	  Map<String,JoblistActivityExample> map = redisUtil.executePipelinedGet(keyList, JoblistActivityExample.class);
              if(StringUtils.isNotEmpty(map)){
              	for(String temp:keyList){
              		JoblistActivityExample bean = map.get(temp);
              		if(bean!=null) {
              			jobList.add(bean);
              		}
              	}
              }
        }else{
            return null;
        }
        //过滤
        List<String> finalLedgerActivityIds = ledgerActivityIds;
        jobList = Optional.ofNullable(jobList).orElse(new  ArrayList<>())
                .stream()
                .filter(item -> {
                            return Objects.equals(item.getTmUsed(), 1)
                                    //主活动
                                    && Objects.equals(item.getIsParent(), 1)
                                    && (StringUtils.isNotEmpty(jobInstanceListDto.getActivityIdList())?
                                    jobInstanceListDto.getActivityIdList().contains(item.getActivityId()) : true)
                                    //活动配置id
                                    && (StringUtils.isNotEmpty(jobInstanceListDto.getActivityId()) ?
                                    Objects.equals(item.getActivityId(), jobInstanceListDto.getActivityId()) : true)
                                    //机构
                                    && orgCompare(item, jobInstanceListDto)
                                    //班次
                                    && (StringUtils.isNotEmpty(jobInstanceListDto.getShiftId()) ?
                                    Objects.equals(item.getShiftClassCode(), jobInstanceListDto.getShiftId()) : true)
                                    //台账
                                    && ledergerCompare(item, jobInstanceListDto, finalLedgerActivityIds)
                                    //上班时间
                                    && dateCompare("sbsj",item, jobInstanceListDto)
                                    && dateCompare("xbsj",item, jobInstanceListDto)
                                    && dateCompare("start",item, jobInstanceListDto);
                        }
                )
                .sorted(Comparator.comparing(JoblistActivityExample::getTmSort))
                .collect(Collectors.toList());
        return jobList;
    }

    private boolean dateCompare(String type, JoblistActivityExample item, JobInstanceListDto jobInstanceListDto) {
        SimpleDateFormat sdf =  new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        if("xbsj".equals(type)){
            if (StringUtils.isNotEmpty(jobInstanceListDto.getXbsj())) {
                try {
                    Date date1 = sdf.parse(item.getSbsj());
                    Date date2 = sdf.parse(jobInstanceListDto.getXbsj());
                    if (date1.equals(date2) || date1.before(date2)){
                        return true;
                    }else {
                        return false;
                    }
                } catch (ParseException e) {
                    throw new RuntimeException(e);
                }
            }else{
                return true;
            }
        }else if("sbsj".equals(type)){
            if (StringUtils.isNotEmpty(jobInstanceListDto.getSbsj())) {
                try {
                    Date date1 = sdf.parse(item.getSbsj());
                    Date date2 = sdf.parse(jobInstanceListDto.getSbsj());
                    if (date1.equals(date2) || date1.before(date2)){
                        return true;
                    }else {
                        return false;
                    }
                } catch (ParseException e) {
                    throw new RuntimeException(e);
                }
            }else{
                return true;
            }
        }else if("start".equals(type)){
            if (StringUtils.isNotEmpty(jobInstanceListDto.getStartDate()) && StringUtils.isNotEmpty(jobInstanceListDto.getEndDate())) {
                try {
                    Date date1 = sdf.parse(item.getBeginDate());
                    Date date2 = sdf.parse(jobInstanceListDto.getEndDate());
                    Date date3 = sdf.parse(jobInstanceListDto.getStartDate());
                    if ((date1.equals(date2) || date1.before(date2)) && (date1.equals(date2) || date1.after(date3))){
                        return true;
                    }else {
                        return false;
                    }
                } catch (ParseException e) {
                    throw new RuntimeException(e);
                }
            }else{
                return true;
            }
        }else{
            return true;
        }
    }

    private boolean ledergerCompare(JoblistActivityExample item, JobInstanceListDto jobInstanceListDto,List<String> ledgerActivityIds) {
        if(StringUtils.isNotEmpty(ledgerActivityIds)){
            return ledgerActivityIds.contains(item.getActivityId());
        }else{
            return Objects.equals(item.getResponsibilityType(), jobInstanceListDto.getJobType());
        }
    }

    private boolean orgCompare(JoblistActivityExample item, JobInstanceListDto jobInstanceListDto) {
        //岗位查询
        if (StringUtils.isNotEmpty(jobInstanceListDto.getP_orgCode())) {
            return Objects.equals(item.getPOrgCode(),jobInstanceListDto.getP_orgCode());
        }else{
            //岗位查询
            if (StringUtils.isNotEmpty(jobInstanceListDto.getOrgCode())) {
                return Objects.equals(item.getOrgCode(),jobInstanceListDto.getOrgCode());
            }else{
                return true;
            }
        }
    }

    private List<JoblistActivityExample> getJoblistActivityExamplesFromDb(JobInstanceListDto jobInstanceListDto, List<String> ledgerActivityIds) {
        //查询主任务
        Where where = Where.create();
        where.eq(JoblistActivityExample::getTmUsed, 1);
        //班次id查询
        if (StringUtils.isNotEmpty(jobInstanceListDto.getShiftId())) {
            where.eq(JoblistActivityExample::getShiftClassCode, jobInstanceListDto.getShiftId());
        }
        //上下班查询
        if (StringUtils.isNotEmpty(jobInstanceListDto.getXbsj())) {
            where.le(JoblistActivityExample::getSbsj, jobInstanceListDto.getXbsj());
        }
        if (StringUtils.isNotEmpty(jobInstanceListDto.getSbsj())) {
            where.ge(JoblistActivityExample::getSbsj, jobInstanceListDto.getSbsj());
        }
        //父数据
        Order order = Order.create();
        order.orderByAsc(JoblistActivityExample::getTmSort);
        List<JoblistActivityExample> joblistActivityExamples = dao.rawQueryListByWhere(JoblistActivityExample.class, where, order);
        //载入缓存
        if (StringUtils.isNotEmpty(joblistActivityExamples)) {
            JoblistActivityCacheUtils.loadActivityExampleInCache(joblistActivityExamples);
        }
        return loadPActivityFromCache(jobInstanceListDto, ledgerActivityIds);
    }

    private List<JoblistActivityExampleVo> sortedJob(List<JoblistActivityExampleVo> jobList) {
        if (StringUtils.isEmpty(jobList)) {
            return jobList;
        }
        StandardJobLibQueryDto queryDto = new StandardJobLibQueryDto();
        queryDto.setPtype(1);
        queryDto.setOrgid(jobList.get(0).getPOrgCode());
        List<JoblistExtPropertiesConfig> joblistExtPropertiesConfigList = methodService.getJoblistExtPropertiesConfigList(queryDto);
        if (StringUtils.isNotEmpty(joblistExtPropertiesConfigList)) {
            Map<String, Integer> sortMap = joblistExtPropertiesConfigList.stream().collect(Collectors.toMap(JoblistExtPropertiesConfig::getId, JoblistExtPropertiesConfig::getTmsort));
            if (!sortMap.isEmpty()) {
                jobList.stream().forEach(i -> i.setPriority(sortMap.containsKey(i.getPriorityid()) ? sortMap.get(i.getPriorityid()) : 0));
            } else {
                jobList.stream().forEach(i -> i.setPriority(0));
            }
        }
        jobList = jobList.stream()
                //按照活动状态
                .sorted(Comparator.comparing(JoblistActivityExampleVo::getActivityStatus)
                        //按优先级 设置优先级
                        .thenComparing(Comparator.comparing(JoblistActivityExampleVo::getPriority))
                        //按活动时间排序
                        .thenComparing((o1, o2) -> {
                            if (StringUtils.isEmpty(o1.getTempDate())) {
                                return -1;
                            } else if (StringUtils.isEmpty(o2.getTempDate())) {
                                return 1;
                            } else {
                                return Collator.getInstance().compare(o1.getTempDate(), o2.getTempDate());
                            }
                        })
                        //全部列表先按照字母顺序进行排序
                        .thenComparing((o1, o2) -> {
                            if (StringUtils.isEmpty(o1.getActivityName())) {
                                return -1;
                            } else if (StringUtils.isEmpty(o2.getActivityName())) {
                                return 1;
                            } else {
                                return Collator.getInstance(Locale.CHINESE).compare(o1.getActivityName(), o2.getActivityName());
                            }
                        }))
                .collect(Collectors.toList());
        return jobList;
    }

    private Map<String, String> getExampleFormDataIdMap(String id) {
        Map<String, String> result = new HashMap<>();
        // 注意如果按照人去区分录入需要增加查询结果集 人员id  然后组建 key加以区分
        List<LinkedHashMap<String, Object>> query = dao.query("select MASTER_ID,SLAVE_ID from JOBLIST_INPUT_MAPPING where MASTER_ID=?", Collections.singletonList(id));
        for (LinkedHashMap<String, Object> dataMap : query) {
            String formDataId = String.valueOf(dataMap.get("SLAVE_ID"));
            if (StringUtils.isNotEmpty(formDataId)) {
                String[] split = formDataId.split(":");
                String formId = split[0];
                result.put(formId, formDataId);
            }
        }
        return result;
    }

    private Map<String, List<JoblistExampleFormBind>> getExampleFormBindMap(List<String> exampleIdList) {
        Map<String, List<JoblistExampleFormBind>> bingMap = new HashMap<>();
        if(StringUtils.isEmpty(exampleIdList)){
            return bingMap;
        }
        exampleIdList = exampleIdList.stream().distinct().collect(Collectors.toList());
        //从缓存中读取绑定关系
        //冲数据库中读取绑定关系
        List<JoblistExampleFormBind> joblistExampleFormBinds = SqlInExecutor.inSql("JOBLIST_EXAMPLE_FORM_BIND",
                "ACTIVITY_EXAMPLE_ID",
                exampleIdList,
                null,null,
                JoblistExampleFormBind.class,null);
        List<String> formIds =
                Optional.ofNullable(joblistExampleFormBinds).orElse(new ArrayList<>())
                        .stream().map(JoblistExampleFormBind::getFormId).distinct().collect(Collectors.toList());
        if(StringUtils.isNotEmpty(formIds)) {
            //根据formId查询表单
            List<SFForm> formList =
                    SqlInExecutor.inSql("SF_FORM",
                            "ID",
                            formIds,
                            null, null,
                            SFForm.class, null);
            Map<String, String> nameMap = Optional.ofNullable(formList).orElse(new ArrayList<>())
                    .stream().collect(Collectors.toMap(SFForm::getId, SFForm::getName));
            joblistExampleFormBinds.forEach(item-> {
               item.setFormName(nameMap.getOrDefault(item.getFormId(),item.getFormName()));
            });
        }

        if (StringUtils.isNotEmpty(joblistExampleFormBinds)) {
            bingMap = joblistExampleFormBinds.stream().collect(Collectors.groupingBy(JoblistExampleFormBind::getActivityExampleId));
        }
        return bingMap;
    }

    private Map<String, String> getClassNameMapByActivityId(List<String> activityPropertiesIdList) {
        Map<String, String> map = new HashMap<>();
        if (StringUtils.isEmpty(activityPropertiesIdList)) {
            return map;
        }
        StandardJobLibQueryDto queryDto = new StandardJobLibQueryDto();
        queryDto.setIdList(activityPropertiesIdList);
        queryDto.setTmUsed(null);
        //查询 活动属性表
        List<JoblistActivityProperties> joblistActivityPropertiesList =
                methodService.getJoblistActivityPropertiesListSql(queryDto);
        if (StringUtils.isEmpty(joblistActivityPropertiesList)) {
            return map;
        }
        for (JoblistActivityProperties joblistActivityProperties : joblistActivityPropertiesList) {
            StandardJobLibQueryDto queryNodePath = new StandardJobLibQueryDto();
            queryNodePath.setOrgid(joblistActivityProperties.getOrgid());
            String classKey = JoblistMethodServiceImpl.ACTIVITYPROPERTIES_CLASS_BEAN_PREKEY +joblistActivityProperties.getOrgid();
            List<JoblistClassVo> classList = redisUtil.getClassList(JoblistClassVo.class,classKey);
            if(StringUtils.isEmpty(classList)){
                classList = jobLibConfigService.getJoblistClassCombList(queryNodePath, "root");
                redisUtil.setList(classKey,classList);
                redisUtil.expire(classKey,JoblistMethodServiceImpl.CACHE_TIME_OUT);
            }
            if (StringUtils.isNotEmpty(classList)) {
                classList = classList.stream().filter(i -> i.getId().equals(joblistActivityProperties.getClassid())).collect(Collectors.toList());
                if (StringUtils.isNotEmpty(classList)) {
                    JoblistClassVo joblistClassVo = classList.get(0);
                    map.put(joblistActivityProperties.getId(), joblistClassVo.getNodeShowName());
                }
            }
        }
        return map;
    }

    /**
     * 通过人员查询任务
     *
     * @return
     * <AUTHOR>
     * @date 2024/7/5
     * @params
     */
    private List<JoblistActivityExample> getActivityByUserIdList(List<String> userList) {
        //按照人员查询任务
        List<JoblistActivityExample> result = new ArrayList<>();
        Where where = Where.create();
        where.in(JobListExampleDutyPerson::getPersonId, userList.toArray());
        List<JobListExampleDutyPerson> jobListExampleDutyPeople = dao.rawQueryListByWhere(JobListExampleDutyPerson.class, where);
        if (StringUtils.isEmpty(jobListExampleDutyPeople)) {
            return result;
        }
        //获取与人有关系的活动实例id
        List<String> activityExampleIdList = jobListExampleDutyPeople.stream().map(JobListExampleDutyPerson::getActivityExampleId).collect(Collectors.toList());
        //根据id列表获取全部的活动实例
        result = getJoblistActivityExamplesByIds(activityExampleIdList, null);
        return result;
    }

    /**
     * 获取班组岗位任务总数
     *
     * @return
     * <AUTHOR>
     * @date 2024/6/27
     * @params type 0班组1岗位
     */
    public Integer getActivityNumberByType(Integer type) {
        Where where = Where.create();
        where.eq(JoblistActivityExample::getTmUsed, 1);
        where.eq(JoblistActivityExample::getResponsibilityType, type);
        Long num = dao.countByWhere(JoblistActivityExample.class, where);
        if (num == null) {
            num = 0L;
        }
        return num.intValue();
    }

    /**
     * 任务分配
     *
     * @return
     * <AUTHOR>
     * @date 2024/6/27
     * @params {
     */
    @Override
    public List<JoblistActivityExample> activityAllocation(JobAllocationDto allocationDto) {
        List<JoblistActivityExample> result = new ArrayList<>();
        if (allocationDto == null || StringUtils.isEmpty(allocationDto.getJobId()) || allocationDto.getUserList() == null) {
            return result;
        }
        //根据id获取活动实例
        JoblistActivityExample example = this.getActivityExampleById(allocationDto.getJobId());
        if (ObjUtils.isEmpty(example)) {
            return result;
        }
        List<JobAllocationUserDto> userList = allocationDto.getUserList();
        List<JoblistActivityExample> children = new ArrayList<>();
        if (example.getIsParent() == 1) {
            //主任务
            //获取其子任务列表
            List<String> pidList = new ArrayList<>();
            pidList.add(example.getId());
            children = getActivityListByPidList(0, example.getResponsibilityType(), pidList);
        } else {
            //子任务列表e
            children.add(example);
        }
        if (StringUtils.isEmpty(children)) {
            return result;
        }
        List<String> exampleIdList = children.stream().map(JoblistActivityExample::getId).collect(Collectors.toList());
        //获取任务全部的人员列表数据
        List<JobListExampleDutyPerson> dutyPersonList = getListExampleDutyPeople(exampleIdList);
        List<JobListExampleDutyPerson> deletePersonList = new ArrayList<>();
        if (allocationDto.getIsUpdateDutyPerson() != null && allocationDto.getIsUpdateDutyPerson() == 1) {
            //更新责任人 删除全部责任人
            deletePersonList.addAll(dutyPersonList.stream().filter(i -> i.getPersonType() == 0).collect(Collectors.toList()));
        }
        if (allocationDto.getIsUpdateDutyPerson() != null && allocationDto.getIsUpdateFeedBackPerson() == 1) {
            //更新反馈人 删除全部反馈人
            deletePersonList.addAll(dutyPersonList.stream().filter(i -> i.getPersonType() == 1).collect(Collectors.toList()));
        }
        //删除原来的人
        Optional.of(deletePersonList).ifPresent(list -> dao.deleteByIdBatch(list));
        //分配新的人
        Date date = new Date();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        String format = sdf.format(date);
        List<JoblistActivityExample> saveExampleList = new ArrayList<>();
        List<JobListExampleDutyPerson> savePersonList = new ArrayList<>();
        //负责人员的机构岗位信息
        List<String> userIds = userList.stream().map(JobAllocationUserDto::getId).collect(Collectors.toList());
        List<SysEmployeeOrgPost> employeeOrgPost = orgPostService.getEmployeeOrgPost(userIds);
        //人员机构岗位
        Map<String, String> orgpostMap = Optional.ofNullable(employeeOrgPost).orElse(new ArrayList<>()).stream()
                .collect(Collectors
                        .toMap(SysEmployeeOrgPost::getEmpid, i -> StringUtils.isNotEmpty(i.getPostid())?i.getPostid():"",
                                (v1, v2) -> v1));
        for (int i = 0; i < children.size(); i++) {
            //更新实例分配情况
            JoblistActivityExample child = children.get(i);
            child.setDeliverDate(format);
            saveExampleList.add(child);
            //分配责任人
            List<JobAllocationUserDto> dutyPersons = userList.stream()
                    .filter(item -> item.getPersonType() == 0).collect(Collectors.toList());
            List<JobAllocationUserDto> feedPersons = userList.stream()
                    .filter(item -> item.getPersonType() == 1).collect(Collectors.toList());
            if (StringUtils.isNotEmpty(dutyPersons)) {
                if (allocationDto.getAllocationType() == 1) {
                    //离散方式
                    int userIndex = i % dutyPersons.size();
                    JobAllocationUserDto userDto = dutyPersons.get(userIndex);
                    String name = userDto.getName();
                    Integer personType = userDto.getPersonType();
                    String id = userDto.getId();
                    String orgpost = orgpostMap.get(id);
                    JobListExampleDutyPerson person = newActivityDutyPerson(example, child, name,
                            personType, id, orgpost);
                    savePersonList.add(person);
                } else {
                    //聚簇方式
                    // 生成责任人
                    for (JobAllocationUserDto userDto : dutyPersons) {
                        String name = userDto.getName();
                        String id = userDto.getId();
                        Integer personType = userDto.getPersonType();
                        String orgpost = orgpostMap.get(id);
                        JobListExampleDutyPerson person = newActivityDutyPerson(example, child, name,
                                personType, id, orgpost);
                        savePersonList.add(person);
                    }
                }
            }
            if (StringUtils.isNotEmpty(feedPersons)) {
                JobAllocationUserDto userDto = feedPersons.get(0);
                String name = userDto.getName();
                String id = userDto.getId();
                Integer personType = userDto.getPersonType();
                String orgpost = orgpostMap.get(id);
                JobListExampleDutyPerson person = newActivityDutyPerson(example, child, name,
                        personType, id, orgpost);
                savePersonList.add(person);
            }
        }
        JoblistActivityCacheUtils.clearActivityDutyPersonCache(children);
        //保存分配时间
        dao.updateByIdBatch(saveExampleList);
        //保存人员列表
        this.saveDutyPerson(savePersonList);
        //重新查询人员信息
        List<JobListExampleDutyPerson> jobListExampleDutyPeople = getListExampleDutyPeople(exampleIdList);
        if(StringUtils.isNotEmpty(jobListExampleDutyPeople)){
            JoblistActivityCacheUtils.loadDutyPersonInCache(jobListExampleDutyPeople);
        }
        return children;
    }

    private JobListExampleDutyPerson newActivityDutyPerson(JoblistActivityExample example,
                                                           JoblistActivityExample child, String name, Integer personType,
                                                           String id, String orgpost) {
        JobListExampleDutyPerson person = new JobListExampleDutyPerson();
        person.setOrgCode(child.getOrgCode());
        person.setShiftCode(child.getShiftClassCode());
        person.setTbrq(child.getTbrq());
        person.setPersonType(personType);
        person.setPostId(orgpost);
        person.setPersonName(name);
        person.setPersonId(id);
        person.setOrgCode(child.getOrgCode());
        person.setShiftCode(child.getShiftClassCode());
        person.setTbrq(child.getTbrq());
        person.setActivityExampleId(child.getId());
        person.setActivityPropertiesId(child.getActivityId());
        return person;
    }

    /**
     * 分配人员生成人员对象
     *
     * @return
     * <AUTHOR>
     * @date 2024/6/27
     * @params
     */
    @Override
    public List<JobListExampleDutyPerson> getListExampleDutyPeople(List<String> exampleIdList) {
        List<JobListExampleDutyPerson> dutyPersonList = SqlInExecutor.inSql("JOBLIST_EXAMPLE_DUTYPERSON",
                "ACTIVITY_EXAMPLE_ID", exampleIdList,null,null,JobListExampleDutyPerson.class,null);
        return dutyPersonList;
    }

    /**
     * 根据实例获取活动
     *
     * @return
     * <AUTHOR>
     * @date 2024/6/27
     * @params
     */
    private JoblistActivityExample getActivityExampleById(String id) {
        return dao.queryObjectById(JoblistActivityExample.class, id);
    }

    /**
     * 活动配置下有没有活动实例
     *
     * @return
     * <AUTHOR>
     * @date 2024/9/11
     * @params
     */
    @Override
    public Boolean haveJoblistActivityExample(String id) {
        Where where = Where.create();
        where.eq(JoblistActivityExample::getActivityId, id);
        return dao.queryCount(JoblistActivityExample.class, where) > 0;

    }

    /**
     * 根据当前用户获取其机构下所有岗位的人及其任务数
     *
     * @return
     * <AUTHOR>
     * @date 2024/6/28
     * @params
     */
    @Override
    public List<JoblistUserPostVo> getPersonTaskNumCurrentUserOrg(JoblistGenDto param) {
        List<JoblistUserPostVo> res = new ArrayList<>();
        //获取当前人
        String orgId = SysUserHolder.getCurrentUser().getOrgId();
        if (StringUtils.isNotEmpty(param.getOrgCode())) {
            orgId = param.getOrgCode();
        }
        if (StringUtils.isEmpty(orgId)) {
            return res;
        }
        //获取当前机构下人员列表
        List<ComponentVo> personList = getPersonsByorg(orgId);
        if (StringUtils.isEmpty(personList) || StringUtils.isEmpty(personList.get(0).getChildren())) {
            return res;
        }
        List<JSONObject> persons = personList.get(0).getChildren().stream().map(PanelVo::getParams).collect(Collectors.toList());
        //根据当前查询岗位
        List<ComponentVo> post = componentService.getPost(orgId, null, null);
        //查验是否具有查看全部岗位数据权限
        if (!this.checkPermission()) {
            //不具有全部岗位权限
            //获取当前人岗位
            String postIds = SysUserHolder.getCurrentUser().getPostId();
            if (StringUtils.isEmpty(post) || StringUtils.isEmpty(post.get(0).getChildren())) {
                return res;
            }
            //过滤只剩下本用户岗位
            List<PanelVo> collect = post.get(0).getChildren().stream().filter(i -> i.getCode().equals(postIds)).collect(Collectors.toList());
            post.get(0).setChildren(collect);
        }
        if (StringUtils.isEmpty(post) || StringUtils.isEmpty(post.get(0).getChildren())) {
            return res;
        }
        //查询例外任务
        TmTaskInfoVo parm = new TmTaskInfoVo();
        //parm.setUserIdList(personIds);
        //parm.setUserIdList(new ArrayList<>());
        parm.setStartTime(param.getSbsj());
        parm.setOrgCode(orgId);
        Map<String, Map<String, Integer>> taskPersonTaskMap = taskAddService.mobileUserTaskInfo(parm);
        for (PanelVo componentVo : post.get(0).getChildren()) {
            //岗位
            JoblistUserPostVo postVo = new JoblistUserPostVo();
            postVo.setPostId(componentVo.getCode());
            postVo.setPostName(componentVo.getLable());
            List<JSONObject> postPersons =
                    persons.stream()
                            .filter(i ->
                                    StringUtils.isNotEmpty(i.getString("postid")) && i.getString("postid").equals(componentVo.getCode()))
                            .collect(Collectors.toList());
            //岗位的人员
            List<JoblistPersonTaskNumVo> personTaskNumVos = new ArrayList<>();
            if (!postPersons.isEmpty()) {
                List<String> personIds = postPersons.stream().map(i -> i.getString("empTmuid")).collect(Collectors.toList());
                Map<String, Map<String, Integer>> personTaskMap = null;
                if (taskPersonTaskMap != null) {
                    personTaskMap = taskPersonTaskMap.entrySet().stream().filter(i -> personIds.contains(i.getKey())).collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
                }
                //根据人员查询活动 ，活动需要在班次机构范围内
                JSONObject activityListByUser = getActivityListByUser(personIds, param);
                if (activityListByUser == null) {
                    continue;
                }
                List<JoblistActivityExample> activityList = activityListByUser.getObject("activityList", List.class);
                if (StringUtils.isEmpty(activityList)) {
                    continue;
                }
                Map<String, List<JoblistActivityExample>> activityMap = activityListByUser.getObject("personGroup", Map.class);
//                构建人
                //按照人员分组
                if (StringUtils.isEmpty(activityList)) {
                    return res;
                }
                for (int i = 0; i < postPersons.size(); i++) {
                    JSONObject person = postPersons.get(i);
                    if (!this.checkPermission()) {
                        //没有权限 判断当前用户
                        String id = SysUserHolder.getCurrentUser().getId();
                        if (!id.equals(person.getString("empTmuid"))) {
                            continue;
                        }
                    }
                    JoblistPersonTaskNumVo personTaskNumVo = new JoblistPersonTaskNumVo();
                    personTaskNumVo.setUserId(person.getString("empTmuid"));
                    personTaskNumVo.setUserName(person.getString("empname"));
                    //例行任务
                    List<JoblistActivityExample> joblistActivityExamples = activityMap.get(person.getString("empTmuid"));
                    Integer sumTaskNum = 0;
                    Integer doneTaskNum = 0;
                    Integer groupSumTaskNum = 0;
                    Integer groupDoneTaskNum = 0;
                    if (StringUtils.isNotEmpty(joblistActivityExamples)) {
                        sumTaskNum = (int) joblistActivityExamples.stream()
                                .filter(item -> item.getResponsibilityType() == 1)
                                .map(JoblistActivityExample::getId).distinct().count();
                        doneTaskNum = (int) joblistActivityExamples.stream()
                                .filter(item -> item.getResponsibilityType() == 1 && item.getActivityStatus() == 2)
                                .map(JoblistActivityExample::getId).distinct().count();
                        groupSumTaskNum = (int) joblistActivityExamples.stream()
                                .filter(item -> item.getResponsibilityType() == 0)
                                .map(JoblistActivityExample::getId).distinct().count();
                        groupDoneTaskNum = (int) joblistActivityExamples.stream()
                                .filter(item -> item.getResponsibilityType() == 0 && item.getActivityStatus() == 2)
                                .map(JoblistActivityExample::getId).distinct().count();
                    }
                    Integer sum = 0;
                    Integer done = 0;
                    if (ObjUtils.notEmpty(personTaskMap)) {
                        Map<String, Integer> taskMap = personTaskMap.get(personTaskNumVo.getUserId());
                        sum = taskMap.get("sum");
                        done = taskMap.get("done");
                    }
                    personTaskNumVo.setExceptionalWorkAllNum(sum);
                    personTaskNumVo.setExceptionalWorkDoneNum(done);
                    personTaskNumVo.setRoutineWorkDoneNum(doneTaskNum);
                    personTaskNumVo.setRoutineWorkAllNum(sumTaskNum);
                    personTaskNumVo.setGroupWorkDoneNum(groupDoneTaskNum);
                    personTaskNumVo.setGroupWorkAllNum(groupSumTaskNum);
                    if (personTaskNumVo.getExceptionalWorkAllNum() > 0 || personTaskNumVo.getRoutineWorkAllNum() > 0) {
                        personTaskNumVos.add(personTaskNumVo);
                        postVo.setPersonTaskNumData(personTaskNumVos);
                    }
                }
                if (StringUtils.isNotEmpty(postVo.getPersonTaskNumData())) {
                    res.add(postVo);
                }
            }
        }
        return res;
    }

    private List<ComponentVo> getPersonsByorg(String orgId) {
        EmpParamDto paramDto = new EmpParamDto();
        paramDto.setOrgcode(orgId);
        List<ComponentVo> personList = componentService.getEmployee(paramDto, null);
        return personList;
    }

    private JSONObject getActivityListByUser(List<String> personIds, JoblistGenDto param) {
        //根据人员查询表
        List<JobListExampleDutyPerson> jobListExampleDutyPersons = getJobListExampleDutyPeople(personIds);
        if (StringUtils.isEmpty(jobListExampleDutyPersons)) return null;
        List<String> activityIds = jobListExampleDutyPersons.stream().map(JobListExampleDutyPerson::getActivityExampleId).distinct().collect(Collectors.toList());
        //查询只子任务
        List<JoblistActivityExample> activityList = getJoblistActivityExamplesByIds(activityIds, 0);
        if (param != null && StringUtils.isNotEmpty(activityList)) {
            activityList = activityList.stream().filter(item -> item.getShiftClassCode().equals(param.getShiftId()) && item.getSbsj().equals(param.getSbsj()) && item.getXbsj().equals(param.getXbsj())).collect(Collectors.toList());
        }
        List<String> pidList = activityList.stream().map(JoblistActivityExample::getPid).distinct().collect(Collectors.toList());
        List<JoblistActivityExample> pactivityList = getJoblistActivityExamplesByIds(pidList, 1);
        Map<String, JoblistActivityExample> pexampleMap = Optional.ofNullable(pactivityList).orElse(new ArrayList<>()).stream().collect(Collectors.toMap(JoblistActivityExample::getId, Function.identity()));
        // 活动列表
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("activityList", activityList);
        //活动分组
        Map<String, List<JoblistActivityExample>> groups = new HashMap<>();
        for (JobListExampleDutyPerson personExample : jobListExampleDutyPersons) {
            List<JoblistActivityExample> orDefault = groups.getOrDefault(personExample.getPersonId(), new ArrayList<>());
            List<JoblistActivityExample> collect = Optional.ofNullable(activityList)
                    .orElse(new ArrayList<>()).stream()
                    .filter(item -> item.getId().equals(personExample.getActivityExampleId()))
                    .map(JoblistActivityExample::getPid)
                    .distinct()
                    .map(item -> pexampleMap.get(item))
                    .collect(Collectors.toList());
            orDefault.addAll(collect);
            groups.put(personExample.getPersonId(), orDefault);
        }
        jsonObject.put("personGroup", groups);

        return jsonObject;
    }

    /**
     * 根据人员查询活动列表
     *
     * @return
     * <AUTHOR>
     * @date 2024/7/10
     * @params
     */

    private List<JobListExampleDutyPerson> getJobListExampleDutyPeople(List<String> personIds) {
        List<JobListExampleDutyPerson> jobListExampleDutyPersons = SqlInExecutor.inSql("JOBLIST_EXAMPLE_DUTYPERSON",
                "PERSON_ID",personIds,null,null,JobListExampleDutyPerson.class,null);
        for (JobListExampleDutyPerson jobListExampleDutyPerson : jobListExampleDutyPersons) {
            if (jobListExampleDutyPerson.getScore() == null) {
                jobListExampleDutyPerson.setScore(0d);
            }
        }
        return jobListExampleDutyPersons;
    }

    /**
     * 通过活动id更新状态
     *
     * @return
     * <AUTHOR>
     * @date 2024/6/28
     * @params map   活动id -》 状态
     */
    @Override
    public Boolean updateActivityStatusByActivityId(Map<String, Integer> map, Supplier<List<JoblistActivityExample>> supplier) {
        return this.updateActivityStatusByActivityId(map, true, supplier);
    }

    @Override
    public Boolean updateActivityStatusByActivityId(Map<String, Integer> map, Boolean isUpdateFeedPerson, Supplier<List<JoblistActivityExample>> supplier) {
        if (map == null || map.isEmpty()) {
            return false;
        }
        //获取活动实例的id列表
        List<String> activityIds = new ArrayList<>();
        for (Map.Entry<String, Integer> entry : map.entrySet()) {
            activityIds.add(entry.getKey());
        }
        //查询活动
        List<JoblistActivityExample> joblistActivityExamples;
        if (supplier == null) {
            joblistActivityExamples = getJoblistActivityExamplesByIds(activityIds, null);
        } else {
            joblistActivityExamples = supplier.get();
        }
        if (StringUtils.isEmpty(joblistActivityExamples)) {
            return false;
        }
        //根据活动实例获取活动相关人员
        List<JobListExampleDutyPerson> listExampleDutyPeople = getListExampleDutyPeople(activityIds);
        Map<String, List<JobListExampleDutyPerson>> dutyPersonMapByExampleId = new HashMap<>();
        Map<String, List<JobListExampleDutyPerson>> feedPersonMapByExampleId = new HashMap<>();
        if (StringUtils.isNotEmpty(listExampleDutyPeople)) {
            dutyPersonMapByExampleId = listExampleDutyPeople.stream().filter(item -> item.getPersonType() == 0).collect(Collectors.groupingBy(JobListExampleDutyPerson::getActivityExampleId));
            feedPersonMapByExampleId = listExampleDutyPeople.stream().filter(item -> item.getPersonType() == 1).collect(Collectors.groupingBy(JobListExampleDutyPerson::getActivityExampleId));
        }
        List<JoblistActivityExample> saveList = new ArrayList<>();
        List<JobListExampleDutyPerson> saveDutyPersonList = new ArrayList<>();
        // 父任务状态
        Map<String, Integer> parentSumMap = new HashMap<>();
        Map<String, Integer> parentFinishMap = new HashMap<>();
        Map<String, Integer> parentNoStartMap = new HashMap<>();
        for (JoblistActivityExample joblistActivityExample : joblistActivityExamples) {
            //处理子任务实例
            Integer i = map.get(joblistActivityExample.getId());
            if (i == null) {
                continue;
            }
            //取得新状态
            joblistActivityExample.setActivityStatus(i);

            //更新最新反馈人
            if (isUpdateFeedPerson) {
                //更新默认负责人
                if (!dutyPersonMapByExampleId.containsKey(joblistActivityExample.getId())) {
                    //如果没有分配责任人
                    JobListExampleDutyPerson person = getJobListExampleDutyPersonByCurrentUser(joblistActivityExample, 0);
                    saveDutyPersonList.add(person);
                } else {
                    if (joblistActivityExample.getResponsibilityType() == 0) {
                        //班组任务时
                        List<JobListExampleDutyPerson> jobListExampleDutyPeople = dutyPersonMapByExampleId.get(joblistActivityExample.getId());
                        //获取责任人
                        List<JobListExampleDutyPerson> dutyPerson =
                                Optional.ofNullable(jobListExampleDutyPeople)
                                        .orElse(new ArrayList<>())
                                        .stream()
                                        .filter(item -> item.getPersonType() == 0  && item.getPersonId().equals(SysUserHolder.getCurrentUser().getId()))
                                        .collect(Collectors.toList());
                        if (StringUtils.isEmpty(dutyPerson)) {
                            //责任人没有自己
                            //添加当前填写人为责任人
                            JobListExampleDutyPerson person = getJobListExampleDutyPersonByCurrentUser(joblistActivityExample, 0);
                            saveDutyPersonList.add(person);
                        }
                        //反馈人
                        List<JobListExampleDutyPerson> jobListExamplefeedPeople =
                                feedPersonMapByExampleId.get(joblistActivityExample.getId());
                        List<JobListExampleDutyPerson> feedPerson =
                                Optional
                                        .ofNullable(jobListExamplefeedPeople)
                                        .orElse(new ArrayList<>())
                                .stream()
                                .filter(item -> item.getPersonType() == 1  && item.getPersonId().equals(SysUserHolder.getCurrentUser().getId()))
                                .collect(Collectors.toList());
                        if (StringUtils.isEmpty(feedPerson)) {
                            //反馈人没有自己
                            //当责任人没有自己时 添加当前填写人为责任人
                            Long count = Optional.ofNullable(jobListExampleDutyPeople).orElse(new ArrayList<>()).stream().filter(item -> item.getPersonType() == 0 && item.getPersonId().equals(SysUserHolder.getCurrentUser().getId())).count();
                            if (count == 0L) {
                                JobListExampleDutyPerson person = getJobListExampleDutyPersonByCurrentUser(joblistActivityExample, 1);
                                saveDutyPersonList.add(person);
                            }
                        }

                    }
                }
                if (feedPersonMapByExampleId.containsKey(joblistActivityExample.getId())) {
                    //有反馈人
                    List<JobListExampleDutyPerson> feedPersons = feedPersonMapByExampleId.get(joblistActivityExample.getId());
                    for (JobListExampleDutyPerson feedPerson : feedPersons) {
                        feedPerson.setPersonId(SysUserHolder.getCurrentUser().getId());
                        feedPerson.setPersonName(SysUserHolder.getCurrentUser().getRealName());
                        feedPerson.setRowFlag(1);
                        boolean b = saveDutyPersonList.stream()
                                .anyMatch(item ->
                                        Objects.equals(item.getPersonId(), feedPerson.getPersonId())
                                                && Objects.equals(item.getPersonType(), feedPerson.getPersonType()));
                        if(!b){
                            saveDutyPersonList.add(feedPerson);
                        }
                    }
                } else {
                    //无反馈人
                    JobListExampleDutyPerson person = getJobListExampleDutyPersonByCurrentUser(joblistActivityExample, 1);
                    saveDutyPersonList.add(person);
                }
            }
            saveList.add(joblistActivityExample);
        }
        List<JoblistActivityExample> saveParentList = null;
        if (StringUtils.isNotEmpty(saveList)) {
            dao.updateByIdBatch(saveList);
            JoblistActivityCacheUtils.clearActivityExampleCache(saveList, true);
            //获取父记录
            List<String> pids = saveList.stream().map(JoblistActivityExample::getPid).distinct().collect(Collectors.toList());
            List<JoblistActivityExample> parents = getJoblistActivityExamplesByIds(pids, 1);
            //子数据
            List<JoblistActivityExample> activityListByPidList = getActivityListByPidList(0, null, pids);
            if (StringUtils.isNotEmpty(activityListByPidList)) {
                Map<String, List<JoblistActivityExample>> pMap = activityListByPidList.stream().collect(Collectors.groupingBy(JoblistActivityExample::getPid));
                for (Map.Entry<String, List<JoblistActivityExample>> stringListEntry : pMap.entrySet()) {
                    List<JoblistActivityExample> value = stringListEntry.getValue();
                    String key = stringListEntry.getKey();
                    //计数
                    if (StringUtils.isEmpty(value)) {
                        continue;
                    }
                    for (JoblistActivityExample joblistActivityExample : value) {
                        Integer sum = parentSumMap.getOrDefault(key, 0);
                        parentSumMap.put(key, sum + 1);
                        if (joblistActivityExample.getActivityStatus() == 2) {
                            //已完成
                            Integer finish = parentFinishMap.getOrDefault(key, 0);
                            parentFinishMap.put(key, finish + 1);
                        }
                        if (joblistActivityExample.getActivityStatus() == 0) {
                            //未开始的
                            Integer noStart = parentNoStartMap.getOrDefault(key, 0);
                            parentNoStartMap.put(key, noStart + 1);
                        }
                    }
                }
            }
            if (StringUtils.isNotEmpty(parents)) {
                saveParentList = new ArrayList<>();
                //更新父记录
                for (JoblistActivityExample parent : parents) {
                    Integer sum = parentSumMap.get(parent.getId());
                    Integer finish = parentFinishMap.get(parent.getId());
                    Integer noStart = parentNoStartMap.get(parent.getId());
                    if (Objects.equals(sum, finish)) {
                        //全是已开始
                        parent.setActivityStatus(2);
                    } else if (Objects.equals(sum, noStart)) {
                        //全是未开始
                        parent.setActivityStatus(0);
                    } else {
                        parent.setActivityStatus(1);
                    }
                    saveParentList.add(parent);
                }
                if (StringUtils.isNotEmpty(saveParentList)) {
                    dao.updateByIdBatch(saveParentList);
                    JoblistActivityCacheUtils.clearActivityExampleCache(saveParentList, true);
                }
            }
        }
        if (StringUtils.isNotEmpty(saveList) || StringUtils.isNotEmpty(saveParentList)) {
            //重新加载父任务中的子任务
            List<String> pidList =
                    Optional.ofNullable(saveParentList).orElse(Collections.emptyList()).stream().map(JoblistActivityExample::getId).collect(Collectors.toList());
            List<String> pids =
                    Optional.ofNullable(saveList).orElse(new ArrayList<>())
                            .stream().map(
                            item -> {
                                if(item.getIsParent()==1){
                                    //父任务
                                    return item.getId();
                                }else{
                                    //子任务
                                    return  item.getPid();
                                }
                            }
                    ).collect(Collectors.toList());
            pidList.addAll(pids);
            pidList = pidList.stream().distinct().collect(Collectors.toList());
            List<JoblistActivityExample> activityListByPidList = new ArrayList<>();
            List<JoblistActivityExample> plist = this.getJoblistActivityExamplesByIds(pidList, 1);
            if(StringUtils.isNotEmpty(plist)){
                activityListByPidList.addAll(plist);
            }
            List<JoblistActivityExample> list = this.getActivityListByPidList(0, null, pidList);
            if(StringUtils.isNotEmpty(list)){
                activityListByPidList.addAll(list);
            }
            if(StringUtils.isNotEmpty(saveParentList)){
                saveList.addAll(saveParentList);
            }
            Map<String, JoblistActivityExample> dataMap =
                    Optional.ofNullable(activityListByPidList).orElse(Collections.emptyList())
                            .stream().collect(Collectors.toMap(JoblistActivityExample::getId,
                            Function.identity()));
            for (JoblistActivityExample joblistActivityExample : saveList) {
                dataMap.put(joblistActivityExample.getId(), joblistActivityExample);
            }
            List<JoblistActivityExample> cacheList = new ArrayList<>();
            for (JoblistActivityExample value : dataMap.values()) {
                cacheList.add(value);
            }
            JoblistActivityCacheUtils.loadActivityExampleInCache(cacheList);
        }

        if (StringUtils.isNotEmpty(saveDutyPersonList)) {
            this.saveDutyPerson(saveDutyPersonList, true);
        }
        List<JobListExampleDutyPerson> jobListExampleDutyPeople = getListExampleDutyPeople(activityIds);
        JoblistActivityCacheUtils.loadDutyPersonInCache(jobListExampleDutyPeople);
        return true;

    }

    private static JobListExampleDutyPerson getJobListExampleDutyPersonByCurrentUser(JoblistActivityExample joblistActivityExample, Integer personType) {
        JobListExampleDutyPerson person = new JobListExampleDutyPerson();
        SysUser currentUser = SysUserHolder.getCurrentUser();
        if(currentUser!=null){
            person.setPersonName(currentUser.getRealName());
            person.setPersonId(currentUser.getId());
            person.setPostId(currentUser.getPostId());
        }
        person.setActivityPropertiesId(joblistActivityExample.getActivityId());
        person.setActivityExampleId(joblistActivityExample.getId());
        person.setTbrq(joblistActivityExample.getTbrq());
        person.setOrgCode(joblistActivityExample.getOrgCode());
        person.setShiftCode(joblistActivityExample.getShiftClassCode());
        if (personType == null) {
            personType = 0;
        }
        person.setPersonType(personType);
        return person;
    }

    @Override
    public Boolean updateActivityStatusByActivityId(Map<String, Integer> map) {
        return updateActivityStatusByActivityId(map, null);
    }

    /**
     * 通过主任务实例id列表获取子任务列表
     *
     * @return
     * <AUTHOR>
     * @date 2024/7/6
     * @params
     */
    @Override
    public List<JoblistActivityExample> getActivityListByPidList(Integer isParent, Integer type, List<String> pidList) {
    	List<JoblistActivityExample> result = new ArrayList<>();
        //从缓存中获取
        List<Object> values = new ArrayList<>();
        List<JoblistActivityExample> finalResult = result;
        List<String> keyList = new ArrayList<String>();
        pidList.forEach(pid->{
            values.add(pid);
            String key= null;
            if(isParent==0){
                key= JoblistActivityCacheUtils.ACTIVITY_GROUP_KEY_PREFIX + pid +":*";
            }else{
                key= JoblistActivityCacheUtils.ACTIVITY_BEAN_KEY_PREFIX +"*:"+pid;
            }
            Collection<String> keys = redisUtil.keys(key);
//            if(StringUtils.isNotEmpty(keys)){
//                for (String s : keys) {
//                    JoblistActivityExample classObject = redisUtil.getClassObject(JoblistActivityExample.class, s);
//                    if(classObject!=null){
//                        finalResult.add(classObject);
//                    }
//                }
//            }
            keyList.addAll(keys);
        });
	    Map<String,JoblistActivityExample> map = redisUtil.executePipelinedGet(keyList, JoblistActivityExample.class);
	    if(StringUtils.isNotEmpty(map)){
	      	for(String temp:keyList){
	      		JoblistActivityExample bean = map.get(temp); 
	      		if(bean!=null) {
	      			finalResult.add(bean);
	      		}
	      	}
	    }
        if(StringUtils.isNotEmpty(result)){
            result =
                    result.stream().sorted(Comparator.comparing(JoblistActivityExample::getTmSort)).collect(Collectors.toList());
           return result;
        }else{
            //从数据库中获取
            Where where = Where.create();
            if (type != null) {
                if (type != 2) {
                    where.eq(JoblistActivityExample::getResponsibilityType, type);
                }
            }
            if (isParent != null) {
                where.eq(JoblistActivityExample::getIsParent, isParent);
            }
            where.eq(JoblistActivityExample::getTmUsed,1);
            result = dao.queryListIn(JoblistActivityExample.class,JoblistActivityExample::getPid,  values,where,null);
            JoblistActivityCacheUtils.loadActivityExampleInCache(result);
            return result;
        }
    }


    /**
     * 通过实例id获取实例
     *
     * @return
     * <AUTHOR>
     * @date 2024/7/4k
     * @params
     */
    @Override
    public List<JoblistActivityExample> getJoblistActivityExamplesByIds(List<String> activityIds, Integer isParent) {
        List<JoblistActivityExample> joblistActivityExamples = new ArrayList<>();
        CopyOnWriteArrayList<JoblistActivityExample> currentList = new CopyOnWriteArrayList<>();
        if (StringUtils.isEmpty(activityIds)) {
            return null;
        }
        int maxInRecordNum = 1000;
        int batchSize = 1;
        if (activityIds.size() > maxInRecordNum) {
            batchSize = activityIds.size() / maxInRecordNum + (activityIds.size() % maxInRecordNum != 0 ? 1 : 0);
        }
        //并行查询不同的数据集
        ExecutorService executor = Executors.newFixedThreadPool(2);
        CountDownLatch count = new CountDownLatch(batchSize);
        //进行数据分片
        for (int i = 0; i < batchSize; i++) {
            List<String> tempIds = activityIds.stream().skip(i * maxInRecordNum).limit(maxInRecordNum).collect(Collectors.toList());
            executor.execute(() -> {
                Long[] longs = this.logTime(Thread.currentThread().getName() + "查询活动实例", null);
                StringBuffer otherWhereStr = new StringBuffer();
                List<Object> paramList = new ArrayList<>();
                otherWhereStr.append(" TMUSED = ?");
                paramList.add(1);
                if (isParent != null) {
                    otherWhereStr.append(" and IS_PARENT = ?");
                    paramList.add(isParent);
                }
                List<JoblistActivityExample> joblistActivityExamplesBatch = SqlInExecutor.inSql(
                        "JOBLIST_ACTIVITYEXAMPLE","id",tempIds,otherWhereStr.toString(),null,
                        JoblistActivityExample.class,paramList);
                currentList.addAll(joblistActivityExamplesBatch);
                count.countDown();
                this.logTime(Thread.currentThread().getName() + " 查询活动" + "结束", longs);
            });
        }
        try {
            count.await();
            executor.shutdown();
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
        for (JoblistActivityExample example : currentList) {
            joblistActivityExamples.add(example);
        }
        return joblistActivityExamples;
    }



    /**
     * 任务记录
     *
     * @return
     * <AUTHOR>
     * @date 2024/7/2
     * @params
     */
    @Override
    public void taskGeneraterRecorder(JSONObject activityIds, List<JoblistActivityExample> saveList) {
        //记录的时候进行持久化
        Map<String, Integer> map = new HashMap<>();
        Map<String, String> mapDate = new HashMap<>();
        //缓存信息redis  key
        for (Map.Entry<String, Object> entry : activityIds.entrySet()) {
            String activityId = entry.getKey();
            JSONObject config = (JSONObject) entry.getValue();
            JSONArray shiftList = config.getJSONArray("shiftList");
            for (int i = 0; i < shiftList.size(); i++) {
                JSONObject shift = shiftList.getJSONObject(i);
                String sbsj = shift.getString("sbsj").substring(0, 10);
                String xbsj = shift.getString("xbsj").substring(0, 10);
                JSONArray frequency = shift.getJSONArray("frequency");
                for (int k = 0; k < frequency.size(); k++) {
                    JSONObject frequencyConfig = frequency.getJSONObject(k);
                    String frequncyTime = frequencyConfig.getString("frequecyNo");
                    String key = PREFIX + activityId + ":" + frequencyConfig.getString("beginDate") + "_" + frequencyConfig.getString("endDate") + ":" + frequncyTime;
                    mapDate.put(key, sbsj + "_" + xbsj + ":_:" + frequencyConfig.getString("beginDate") + "_" + frequencyConfig.getString("endDate"));
                    map.put(key, map.getOrDefault(key, 0) + 1);
                }

            }
        }
        Map<String, List<JoblistActivityExample>> group = saveList
                .stream().collect(Collectors.groupingBy(JoblistActivityExample::getActivityId));
        List<JoblistGeneraterReCord> insertList = new ArrayList<>();
        List<JoblistGeneraterReCord> updateList = new ArrayList<>();
        //查询数据库
        Where where = Where.create();
        where.eq(JoblistGeneraterReCord::getTmUsed, 1);
        List<JoblistGeneraterReCord> joblistGeneraterReCords = dao.rawQueryListByWhere(JoblistGeneraterReCord.class, where);
        if (!map.isEmpty()) {
            for (Map.Entry<String, Integer> entry : map.entrySet()) {
                //持久化
                String key = entry.getKey();
                Integer sum;
                String numString = redisUtil.getString(key);
                if (StringUtils.isEmpty(numString)) {
                    //直接放入
                    redisUtil.set(key, String.valueOf(entry.getValue()));
                    sum = entry.getValue();
                } else {
                    Integer num = Integer.valueOf(numString);
                    num = entry.getValue() + num;
                    redisUtil.set(key, String.valueOf(num));
                    sum = num;
                }
                //查找数据库对象
                List<JoblistActivityExample> joblistActivityExamples = group.get(key.split(":")[2]);
                if (StringUtils.isNotEmpty(joblistGeneraterReCords)) {
                    List<JoblistGeneraterReCord> dataList = joblistGeneraterReCords.stream().filter(i -> key.equals(i.getActivityRecordKey())).collect(Collectors.toList());
                    if (StringUtils.isNotEmpty(dataList)) {
                        //数据库里有 将缓存数据更新到数据库
                        JoblistGeneraterReCord joblistGeneraterReCord = dataList.get(0);
                        if (StringUtils.isNotEmpty(joblistActivityExamples)) {
                            String date = mapDate.get(key);
                            List<JoblistActivityExample> examples = joblistActivityExamples.stream()
                                    .filter(item -> date.contains(item.getSbsj() + "_" + item.getSbsj() + ":") || date.contains(":" + item.getBeginDate() + "_" + item.getEndDate()))
                                    .collect(Collectors.toList());
                            List<String> ids = examples.stream().map(JoblistActivityExample::getId).collect(Collectors.toList());
                            if (StringUtils.isNotEmpty(joblistGeneraterReCord.getExampleIdList())) {
                                List<String> dataIds = Arrays.asList(joblistGeneraterReCord.getExampleIdList().split(","));
                                ids.addAll(dataIds);
                                ids = ids.stream().sorted().distinct().collect(Collectors.toList());
                                String joined = StringUtils.join(ids, ",");
                                joblistGeneraterReCord.setExampleIdList(joined);
                            }
                        }
                        joblistGeneraterReCord.setSum(sum);
                        updateList.add(joblistGeneraterReCord);
                    } else {
                        //数据库里没有
                        JoblistGeneraterReCord reCord = new JoblistGeneraterReCord();
                        reCord.setId(TMUID.getUID());
                        reCord.setTmUsed(1);
                        reCord.setActivityRecordKey(key);
                        if (StringUtils.isNotEmpty(joblistActivityExamples)) {
                            String date = mapDate.get(key);
                            List<JoblistActivityExample> examples = joblistActivityExamples.stream()
                                    .filter(item -> date.contains(item.getSbsj() + "_" + item.getSbsj() + ":") || date.contains(":" + item.getBeginDate() + "_" + item.getEndDate()))
                                    .collect(Collectors.toList());
                            List<String> ids = examples.stream().map(JoblistActivityExample::getId).collect(Collectors.toList());
                            ids = ids.stream().sorted().distinct().collect(Collectors.toList());
                            String joined = StringUtils.join(ids, ",");
                            reCord.setExampleIdList(joined);
                        }
                        reCord.setSum(sum);
                        insertList.add(reCord);
                    }

                } else {
                    //数据库里没有
                    JoblistGeneraterReCord reCord = new JoblistGeneraterReCord();
                    reCord.setId(TMUID.getUID());
                    reCord.setTmUsed(1);
                    reCord.setActivityRecordKey(key);
                    if (StringUtils.isNotEmpty(joblistActivityExamples)) {
                        List<String> ids = joblistActivityExamples.stream().map(JoblistActivityExample::getId).collect(Collectors.toList());
                        ids = ids.stream().sorted().distinct().collect(Collectors.toList());
                        String joined = StringUtils.join(ids, ",");
                        reCord.setExampleIdList(joined);
                    }
                    reCord.setSum(sum);
                    insertList.add(reCord);
                }
            }
            if (StringUtils.isNotEmpty(insertList)) {
                dao.insertBatch(insertList);
            }
            if (StringUtils.isNotEmpty(updateList)) {
                dao.updateByIdBatch(updateList);
            }
        }
    }

    /**
     * 根据活动配置获取活动属性id列表
     *
     * @return
     * <AUTHOR>
     * @date 2024/7/4
     * @params
     */
    @Override
    public void getActivityIdListByGeneraterConfig(JSONObject activityIds, List<String> activityIdList) {
        for (Map.Entry<String, Object> key : activityIds.entrySet()) {
            activityIdList.add(key.getKey());
        }
    }

    /**
     * 根据频次是否可以生成任务
     *
     * @return
     * <AUTHOR>
     * @date 2024/7/2
     * @params
     */
    @Override
    public Boolean taskCanGenerater(String activityId, String beginDate, String endDate, String frequncyTime, Integer total) {
        String key = PREFIX + activityId + ":" + beginDate + "_" + endDate + ":" + frequncyTime;
//        String key = PREFIX + activityId + ":" + frequncyTime;
        Boolean has = redisUtil.hasKey(key);
        String num = redisUtil.getString(key);
        if (!has) {
            //缓存里没有尝试从数据库 ，暂时不解决数据库里肯定没有的情况
            num = this.taskRecordRecover(activityId, beginDate, endDate, frequncyTime);
        }else{
            if(num==null){
                return true;
            }
        }
        if (StringUtils.isEmpty(num)) {
            if (total > 0) {
                return true;
            }
        } else {
            if (total > Integer.valueOf(num)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 从数据库里取出生成记录并且缓存
     *
     * @return
     * <AUTHOR>
     * @date 2024/7/6
     * @params
     */
    private String taskRecordRecover(String activityId, String beginDate, String endDate, String frequncyTime) {
        Where where = Where.create();
        where.eq(JoblistGeneraterReCord::getTmUsed, 1);
        String key = PREFIX + activityId + ":" + beginDate + "_" + endDate + ":" + frequncyTime;
        where.eq(JoblistGeneraterReCord::getActivityRecordKey, key);
        List<JoblistGeneraterReCord> joblistGeneraterReCords = dao.rawQueryListByWhere(JoblistGeneraterReCord.class, where);
        if (StringUtils.isNotEmpty(joblistGeneraterReCords)) {
            JoblistGeneraterReCord joblistGeneraterReCord = joblistGeneraterReCords.get(0);
            Integer sum = joblistGeneraterReCord.getSum();
            if(sum==null){
                sum=0;
            }
            //写入缓存
            redisUtil.set(key, String.valueOf(sum));
            return String.valueOf(sum);
        }
        return null;
    }


    /**
     * 根据活动实例查询责任人
     *
     * @return
     * <AUTHOR>
     * @date 2024/7/3
     * @params
     */
    private List<JobListExampleDutyPerson> getDutyPersonByActivityId(List<String> activityIds, String userId, JobInstanceListDto jobInstanceListDto) {
        //从缓存中获取
        JSONObject result = loadDutyFromCache(activityIds, userId);
        List<JobListExampleDutyPerson> jobListExampleDutyPeople = new ArrayList<>();
        JSONArray dataList = result.getJSONArray("dataList");
        if(!result.getBoolean("cacheFail")) {
            if (dataList != null) {
                for (int i = 0; i < dataList.size(); i++) {
                    JobListExampleDutyPerson javaObject = dataList.getJSONObject(i).toJavaObject(JobListExampleDutyPerson.class);
                    if (!"null_object".equals(javaObject.getId())) {
                        jobListExampleDutyPeople.add(javaObject);
                    }
                }
            }
        } else {
            //从数据库中获取
            loadDutyFromDb(jobInstanceListDto,activityIds);
            result = loadDutyFromCache(activityIds, userId);
            dataList = result.getJSONArray("dataList");
            if(dataList!=null){
                for (int i = 0; i < dataList.size(); i++) {
                    JobListExampleDutyPerson javaObject = dataList.getJSONObject(i).toJavaObject(JobListExampleDutyPerson.class);
                    if(!"null_object".equals(javaObject.getId())){
                        jobListExampleDutyPeople.add(javaObject);
                    }
                }
            }
        }
        return jobListExampleDutyPeople;
    }

    private JSONObject loadDutyFromCache(List<String> activityIds, String userId) {
        JSONObject res = new JSONObject();
        List<JobListExampleDutyPerson> result = new ArrayList<>();
        if(StringUtils.isEmpty(activityIds)){
            res.put("cacheFail",false);
            return res;
        }
        //记录查询总数
        Integer total = activityIds.size();
        //记录命中次数
        Integer sum = 0;
        List<JobListExampleDutyPerson> finalResult = result;
       
        if (StringUtils.isNotEmpty(activityIds)) {
            //按照活动从缓存查询
        	List<String> keyList = new ArrayList<String>();
            for (String activityId : activityIds) {
                String dutyGroupKey = JoblistActivityCacheUtils.DUTY_GROUP_KEY_PREFIX + activityId + ":*";
                Collection<String> keys = redisUtil.keys(dutyGroupKey);
                if (StringUtils.isNotEmpty(keys)) {
                    keyList.addAll(keys);
                    //命中次数累加
                    sum = sum + 1;
//                    List<JobListExampleDutyPerson> finalResult1 = finalResult;
//                    keys.forEach(key -> {
//                        finalResult1.add(redisUtil.getClassObject(JobListExampleDutyPerson.class, key));
//                    });  
//                    if (StringUtils.isNotEmpty(finalResult1)) {
//                        if (StringUtils.isNotEmpty(userId)) {
//                            finalResult =
//                                    finalResult1.stream().filter(item -> Objects.equals(item.getPersonId(),userId))
//                                            .collect(Collectors.toList());
//                        }
//                    }
                }
            }
            Map<String,JobListExampleDutyPerson> map = redisUtil.executePipelinedGet(keyList, JobListExampleDutyPerson.class);
            if(StringUtils.isNotEmpty(map)){
	          	for(String temp:keyList){
	          		JobListExampleDutyPerson bean = map.get(temp); 
	          		if(bean!=null) {
	          			finalResult.add(bean);
	          		}
	          	}
            }
            if (StringUtils.isNotEmpty(finalResult)) {
	            if (StringUtils.isNotEmpty(userId)) {
	                  finalResult =
	                		  finalResult.stream().filter(item -> Objects.equals(item.getPersonId(),userId))
	                                  .collect(Collectors.toList());
	            }
            }      
        }else {
           
            //按照人从缓存查询
            String dutyGroupKey = null;
            if  (StringUtils.isNotEmpty(userId)) {
                dutyGroupKey = JoblistActivityCacheUtils.DUTY_GROUP_KEY_PREFIX + "*:" + userId+":*";
            } else {
                dutyGroupKey = JoblistActivityCacheUtils.DUTY_GROUP_KEY_PREFIX + "*";
            }
//            List<JobListExampleDutyPerson> finalResult2 = finalResult;
//            redisUtil.keys(dutyGroupKey).forEach(key -> {
//                finalResult2.add(redisUtil.getClassObject(JobListExampleDutyPerson.class, key));
//            });
            Collection<String> keys = redisUtil.keys(dutyGroupKey);
            if (StringUtils.isNotEmpty(keys)) {
            	List<String> keyList = new ArrayList<String>();
            	keyList.addAll(keys);
	            Map<String,JobListExampleDutyPerson> map = redisUtil.executePipelinedGet(keyList, JobListExampleDutyPerson.class);
	            if(StringUtils.isNotEmpty(map)){
	            	for(String temp:keyList){
	            		JobListExampleDutyPerson bean = map.get(temp); 
	            		if(bean!=null) {
	            			finalResult.add(bean);
	            		}
	            	}
	            }
            }   
        }
        res.put("dataList",finalResult);
        //通过总数和命中次数来确定缓存是否有效
        res.put("cacheFail",Objects.equals(total,sum)?false:true);
        return res;
    }

    private void loadDutyFromDb(JobInstanceListDto jobInstanceListDto,List<String> activityIds) {

        if(StringUtils.isNotEmpty(jobInstanceListDto.getOrgCode())){
        	String lockKey = "Duty";
            Where where = Where.create();
            where.eq(JobListExampleDutyPerson::getOrgCode, jobInstanceListDto.getOrgCode());
            lockKey+=":"+jobInstanceListDto.getOrgCode();
            if(StringUtils.isNotEmpty(jobInstanceListDto.getShiftId())){
                where.eq(JobListExampleDutyPerson::getShiftCode, jobInstanceListDto.getShiftId());
                lockKey+=":"+jobInstanceListDto.getShiftId();
            }
            if(StringUtils.isNotEmpty(jobInstanceListDto.getTbrq())){
                where.eq(JobListExampleDutyPerson::getTbrq, jobInstanceListDto.getTbrq());
                lockKey+=":"+jobInstanceListDto.getTbrq();
            }
            boolean lock = JoblistActivityCacheUtils.isDataLock(lockKey,true);//判断锁(读取时如果无锁则加锁)
            boolean isRead =!lock;//如果未锁，则需要读取数据
//            long i =System.currentTimeMillis();
            do {
            	if(isRead) {
            		Order order = Order.create();
                    order.orderByAsc(JobListExampleDutyPerson::getTmSort);
                    List<JobListExampleDutyPerson> result = dao.rawQueryListByWhere(JobListExampleDutyPerson.class,where,order);
                    if(StringUtils.isNotEmpty(result)){
                        if(StringUtils.isNotEmpty(activityIds)){
                            //数据库存在责任人的任务
                            List<String> dataBaseHave = result.stream().map(JobListExampleDutyPerson::getActivityExampleId).collect(Collectors.toList());
                            activityIds =
                                    activityIds.stream().filter(item-> !dataBaseHave.contains(item)).collect(Collectors.toList());
                            //这种数据没有责任人缓存空标识
                            if(StringUtils.isNotEmpty(activityIds)){
                                for (String activityId : activityIds) {
                                    JobListExampleDutyPerson bean = new JobListExampleDutyPerson();
                                    bean.setId("null_object");
                                    bean.setActivityExampleId(activityId);
                                    result.add(bean);
                                }
                            }

                        }
                        JoblistActivityCacheUtils.loadDutyPersonInCache(result);
                    }
                    JoblistActivityCacheUtils.unLockData(lockKey);//缓存加载完毕后，清除缓存key
            		break;       	
            	}
            	try {
	    			Thread.sleep(100);//休眠100毫秒
	    		} catch (InterruptedException e) {
	    			// TODO Auto-generated catch block
	    			e.printStackTrace();
	    		}
            	lock = JoblistActivityCacheUtils.isDataLock(lockKey,false);//锁开了即停止(读锁时不自动加锁)
            }while(lock);//判断redis锁，有锁就循环
          //  System.out.println("Duty执行锁"+isRead+(System.currentTimeMillis()-i)+":"+System.currentTimeMillis());
        }
    }

    /**
     * 保存责任人
     *
     * @return
     * <AUTHOR>
     * @date 2024/7/3
     * @params
     */
    @Override
    public List<JobListExampleDutyPerson> saveDutyPerson(List<JobListExampleDutyPerson> saveList) {
        return this.saveDutyPerson(saveList,false);
    }
    @Override
    @Transactional
    public List<JobListExampleDutyPerson> saveDutyPerson(List<JobListExampleDutyPerson> saveList, Boolean insertBeforeDel) {
        Boolean flag = false;
        List<JobListExampleDutyPerson> result = new ArrayList<>();
        if (StringUtils.isEmpty(saveList)) {
            return null;
        }
        int max = 0;
        Long maxSort = dao.findMaxId(JobListExampleDutyPerson.class, JobListExampleDutyPerson::getTmSort);
        if (maxSort != null) {
            max = maxSort.intValue();
        }
        List<JobListExampleDutyPerson> insertList = new ArrayList<>();
        List<JobListExampleDutyPerson> deleteList = new ArrayList<>();
        List<JobListExampleDutyPerson> updateList = new ArrayList<>();
        for (JobListExampleDutyPerson person : saveList) {
            Integer rowFlag = person.getRowFlag();
            JobListExampleDutyPerson person1 = ObjUtils.copyTo(person, JobListExampleDutyPerson.class);
            if (rowFlag == null || rowFlag == 0) {
                person1.setId(TMUID.getUID());
                person1.setTmSort(++max);
                insertList.add(person1);
            } else if (rowFlag == 1) {
                updateList.add(person);
            } else {
                deleteList.add(person);
            }
        }
        if (StringUtils.isNotEmpty(insertList)) {
            //插入列表去重
            insertList = insertList.stream()
                    .collect(Collectors.collectingAndThen(
                            Collectors.toCollection(() ->
                                    new TreeSet<>(
                                            Comparator.comparing(
                                                    item -> item.getActivityExampleId() + "_" + item.getPersonId()+"_"+item.getPersonType()
                                            ))),  ArrayList::new));
            flag = this.insertDutyPersonToDb(insertList);
        }
        if (StringUtils.isNotEmpty(updateList)) {
            JoblistActivityCacheUtils.loadDutyPersonInCache(updateList);
            flag = dao.updateByIdBatch(updateList) > 0;
        }
        if (StringUtils.isNotEmpty(deleteList)) {
            flag = dao.deleteByIdBatch(deleteList) > 0;
        }
        if(flag){
            result.addAll(insertList);
            result.addAll(updateList);
            return result;
        }
        return Collections.emptyList();
    }

    private boolean insertDutyPersonToDb(List<JobListExampleDutyPerson> insertList) {
        //持久化到数据库
        boolean flag = dao.insertBatch(insertList,200) > 0;
        return flag;
    }

    /**
     * 查验当前用户是否具有查询全部权限
     *
     * @return
     * <AUTHOR>
     * @date 2024/7/8
     * @params
     */
    private Boolean checkPermission() {
        List<String> permissions = SysUserHolder.getCurrentUser().getPermissions();
        if (StringUtils.isEmpty(permissions)) {
            return false;
        }
        List<String> permisses = permissions.stream().filter(i -> i.contains(JoblistConstant.OPS_CLASS_MONITOR) || i.contains(JoblistConstant.OPS_CLASS_MANAGE)).collect(Collectors.toList());
        if (StringUtils.isNotEmpty(permisses)) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * 获取工作台历数据
     *
     * @return
     * <AUTHOR>
     * @params
     */
    @Override
    public List<JoblistActivityPropertiesVo> getJobListDeskCalendar(JoblistGenDto joblistGenDto, Pagination<?> page) {
        List<JoblistActivityPropertiesVo> list = new ArrayList<>();
        StandardJobLibQueryDto queryDto = new StandardJobLibQueryDto();
        queryDto.setOrgid(joblistGenDto.getOrgCode());
        queryDto.setUnitid(joblistGenDto.getUnitid());
        queryDto.setTypeid(joblistGenDto.getTypeid());
        //优先级
        Map<String, String> priorityNameMap = new HashMap<>();
        Map<String, Integer> prioritySortMap = new HashMap<>();
        StandardJobLibQueryDto priorityQueryDto = new StandardJobLibQueryDto();
        priorityQueryDto.setPtype(1);
        priorityQueryDto.setOrgid(queryDto.getOrgid());
        List<JoblistExtPropertiesConfig> initExtPropertiesData = jobLibConfigService.getExtPropertiesListByInit(priorityQueryDto);
        if (StringUtils.isNotEmpty(initExtPropertiesData)) {
            priorityNameMap = initExtPropertiesData.stream()
                    .collect(Collectors.toMap(JoblistExtPropertiesConfig::getId, JoblistExtPropertiesConfig::getPname));
            prioritySortMap = initExtPropertiesData.stream()
                    .collect(Collectors.toMap(JoblistExtPropertiesConfig::getId, JoblistExtPropertiesConfig::getTmsort));
        }
        //分类
        StandardJobLibQueryDto classQueryDto = new StandardJobLibQueryDto();
        classQueryDto.setOrgid(queryDto.getOrgid());
        classQueryDto.setTypeid("0");
        List<JoblistClassVo> joblistClassCombList = jobLibConfigService.getJoblistClassCombList(classQueryDto, "root");
        Map<String, String> classMap = new HashMap<>();
        if (StringUtils.isNotEmpty(joblistClassCombList)) {
            classMap = joblistClassCombList.stream().collect(Collectors.toMap(JoblistClassVo::getId, JoblistClassVo::getNodeShowName));
        }
        //获取当前分类
        //有效的配置列表
        Set<String> propertyIds = new HashSet<>();
        //根据当前的有效作业单元id
        List<JoblistActivityPropertiesVo> joblistActivityListByTmSort = jobLibConfigService.getJoblistActivityListByTmSort(joblistGenDto.getOrgCode(), null);
        if (StringUtils.isNotEmpty(joblistActivityListByTmSort)) {
            for (JoblistActivityPropertiesVo joblistActivityPropertiesVo : joblistActivityListByTmSort) {
                propertyIds.add(joblistActivityPropertiesVo.getId());
            }
        }
        if (0 == joblistGenDto.getScope()) {
            list = jobLibConfigService.getJoblistActivityList(queryDto, page);
            if (StringUtils.isNotEmpty(list)) {
                list = list.stream().filter(item -> propertyIds.contains(item.getId())).collect(Collectors.toList());
                for (JoblistActivityPropertiesVo vo : list) {
                    vo.setPriorityName(priorityNameMap.get(vo.getPriorityid()));
                    vo.setPrioritySort(prioritySortMap.get(vo.getPriorityid()));
                    vo.setClassName(classMap.get(vo.getClassid()));
                }
                //重排序
                list = list.stream()
                        .sorted(Comparator.comparing(JoblistActivityPropertiesVo::getPrioritySort)
                                .thenComparing((o1, o2) -> {
                                    if (StringUtils.isEmpty(o1.getClassName())) {
                                        return 1;
                                    } else if (StringUtils.isEmpty(o2.getClassName())) {
                                        return -1;
                                    } else {
                                        return Collator.getInstance(Locale.CHINESE).compare(o1.getClassName(), o2.getClassName());
                                    }
                                })
                                .thenComparing((o1, o2) -> Collator.getInstance(Locale.CHINESE).compare(o1.getUnitName(), o2.getUnitName())))
                        .collect(Collectors.toList());
            }
        } else {
            String format = joblistGenDto.getSelectDate();
            queryDto.setQueryDate(format);
            queryDto.setTmUsed(1);
            List<JoblistActivityProperties> joblistActivityPropertiesList = methodService.getJoblistActivityPropertiesList(queryDto);
            if (StringUtils.isNotEmpty(joblistActivityPropertiesList)) {
                joblistActivityPropertiesList = joblistActivityPropertiesList.stream().filter(item -> propertyIds.contains(item.getId())).collect(Collectors.toList());
                List<String> ids = joblistActivityPropertiesList.stream().map(JoblistActivityProperties::getId).collect(Collectors.toList());
                //获取核算对象名称
                MethodQueryDto queryDto1 = new MethodQueryDto();
                queryDto.setIdList(ids);
                List<Costuint> costuintList = unitMethodService.getCostuintList(queryDto1);
                Map<String, String> costMap = new HashMap<>();
                if (StringUtils.isNotEmpty(costuintList)) {
                    costMap = costuintList.stream().collect(Collectors.toMap(Costuint::getId, Costuint::getName, (k1, k2) -> k1));
                }
                //频次
                StandardJobLibQueryDto cycleQuery = new StandardJobLibQueryDto();
                cycleQuery.setOrgid(joblistGenDto.getOrgCode());
                cycleQuery.setQueryType("orgSys");
                List<CycleScheme> schemelist = jobLibConfigService.getCycleSchemeList(cycleQuery);
                Map<String, String> cycleMap = Optional.ofNullable(schemelist).orElse(new ArrayList<>()).stream().collect(Collectors.toMap(CycleScheme::getId, CycleScheme::getSname, (k1, k2) -> k1));
                //获取活动配置和岗位映射
                List<Object> paramList = new ArrayList<>();
                StringBuffer otherWhereStr = new StringBuffer();
                otherWhereStr.append(" BINDTYPE = ?");
                paramList.add(1);
                List<JoblistPersonBind> bindList = SqlInExecutor.inSql("JOBLIST_PERSONBIND","PID",ids,
                        otherWhereStr.toString(),null,JoblistPersonBind.class,
                        paramList);
                Map<String, List<JoblistPersonBind>> bindMap = Optional.ofNullable(bindList).orElse(new ArrayList<>()).stream()
                        .collect(Collectors.groupingBy(JoblistPersonBind::getPid));
                AbstractJobGenerater generater = new AbstractJobGenerater() {
                    @Override
                    public List<JoblistActivityExampleVo> generateActivityInstance(JSONObject activityIds, Boolean useRecord, List<JoblistActivityProperties> activityProperties) {
                        return Collections.emptyList();
                    }

                    //这两个方法暂时不需要重写
                    @Override
                    public void allocationPersonToActivityExample(List<JobListExampleDutyPerson> dutyPersonList, List<JobListExampleDutyPerson> saveDutyPersonList, JoblistActivityExample activityExample, String orgcode) {

                    }

                    @Override
                    public List<JoblistActivityExampleVo> generateActivityInstance(JSONObject activityIds, List<JoblistActivityProperties> activityProperties) {
                        return Collections.emptyList();
                    }
                };
                JSONObject activityIds = generater.builderGeneraterConfig(joblistActivityPropertiesList, joblistGenDto.getOrgCode(), 0, joblistGenDto.getSelectDate());
                if (activityIds != null) {
                    //配置中可以按照班次生成的所有活动属性id
                    List<String> activityIdList = new ArrayList<>();
                    for (Map.Entry<String, Object> key : activityIds.entrySet()) {
                        //判断班次
                        if (StringUtils.isNotEmpty(joblistGenDto.getShiftId())) {
                            JSONObject obj = (JSONObject) key.getValue();
                            JSONArray shiftList = obj.getJSONArray("shiftList");
                            Boolean flag = false;
                            for (int i = 0; i < shiftList.size(); i++) {
                                JSONObject shift = shiftList.getJSONObject(i);
                                if (shift.getString("shiftClassCode").equals(joblistGenDto.getShiftId())) {
                                    //如果符合班次
                                    flag = true;
                                    break;
                                }
                            }
                            if (!flag) {
                                //都不符合查询的班次  此活动不需要被查询
                                continue;
                            }
                        }
                        if (StringUtils.isNotEmpty(joblistGenDto.getPostId())) {
                            List<JoblistPersonBind> binds = bindMap.get(key.getKey());
                            List<String> bindids = Optional.ofNullable(binds).orElse(new ArrayList<>()).stream()
                                    .map(JoblistPersonBind::getBindid).distinct().collect(Collectors.toList());
                            if (!bindids.contains(joblistGenDto.getPostId())) {
                                //不符合查询的岗位条件
                                continue;
                            }
                        }
                        activityIdList.add(key.getKey());
                    }
                    for (JoblistActivityProperties joblistActivityProperties : joblistActivityPropertiesList) {
                        if (activityIdList.contains(joblistActivityProperties.getId())) {
                            //符合条件的活动
                            JoblistActivityPropertiesVo vo = ObjUtils.copyTo(joblistActivityProperties, JoblistActivityPropertiesVo.class);
                            vo.setUnitName(costMap.get(joblistActivityProperties.getId()));
                            vo.setCalcSchemeName(cycleMap.get(joblistActivityProperties.getId()));
                            vo.setPriorityName(priorityNameMap.get(vo.getPriorityid()));
                            vo.setPrioritySort(prioritySortMap.get(vo.getPriorityid()));
                            vo.setClassName(StringUtils.isNotEmpty(classMap.get(vo.getClassid())) ? classMap.get(vo.getClassid()) : "");
                            list.add(vo);
                        }
                    }
                    //重排序
                    list = list.stream()
                            .sorted(Comparator.comparing(JoblistActivityPropertiesVo::getPrioritySort)
                                    .thenComparing((o1, o2) -> {
                                        if (StringUtils.isEmpty(o1.getClassName())) {
                                            return 1;
                                        } else if (StringUtils.isEmpty(o2.getClassName())) {
                                            return -1;
                                        } else {
                                            return Collator.getInstance(Locale.CHINESE).compare(o1.getClassName(), o2.getClassName());
                                        }
                                    })
                                    .thenComparing((o1, o2) -> Collator.getInstance(Locale.CHINESE).compare(o1.getUnitName(), o2.getUnitName())))
                            .collect(Collectors.toList());

                }
            }
            if (page != null) {
                if (StringUtils.isNotEmpty(list)) {
                    page.setTotal(list.size());
                    list = this.listPage(list, page);
                } else {
                    page.setTotal(0);
                }
            }
        }
        return list;
    }

    private List<JoblistActivityPropertiesVo> listPage(List<JoblistActivityPropertiesVo> list, Pagination<?> page) {
        int intPage = page.getPage();
        int number = page.getSize();
        int start = (intPage - 1) * number;
        int totalCount = list.size();
        int pageCount = totalCount % number == 0 ? totalCount / number : totalCount / number + 1;
        List<JoblistActivityPropertiesVo> result = null;
        if (intPage == pageCount) {
            result = list.subList(start, totalCount);
        } else {
            result = list.subList(start, start + number);
        }
        return result;
    }

    /**
     * 查询台历班组
     *
     * @param param
     * @return
     * <AUTHOR>
     * @date 2024/7/9
     * @params
     */
    @Override
    public List<WorkCalendarGroupDto> getWorkCalendarGroup(WorkCalendarQueryDto param) {
        List<WorkCalendarGroupDto> result = new ArrayList<>();
        //通过机构获取所有班组
        if (StringUtils.isEmpty(param.getOrgCode())) {
            return result;
        }
        List<SysOrg> orgList = orgService.getOrgList(param.getOrgCode());
        List<String> shiftOrgList = new ArrayList<>();
        //获取倒班
        if (param.getType() == 1) {
            //按日查询
            List<ShiftForeignVo> allShiftDataByrq = shiftService.getAllShiftDataByrq(param.getSelectDate());
            if (StringUtils.isNotEmpty(allShiftDataByrq)) {
                shiftOrgList = allShiftDataByrq.stream().map(ShiftForeignVo::getOrgCode).distinct().collect(Collectors.toList());
            }
        }
        if (StringUtils.isNotEmpty(orgList)) {
            orgList = orgList.stream().filter(item -> "shiftteam".equals(item.getOrgType())).collect(Collectors.toList());
        }
        //获取所有班组
        if (StringUtils.isEmpty(orgList)) {
            return result;
        }
        for (SysOrg sysOrg : orgList) {
            if (param.getType() == 1 && !shiftOrgList.contains(sysOrg.getId())) {
                continue;
            }
            String orgpath = sysOrg.getOrgpath().replace("/" + sysOrg.getId(), "");
            String[] parentList = orgpath.split("/");
            if (parentList.length > 0) {
                String pid = parentList[parentList.length - 1];
                SysOrg porg = orgService.findOrgById(pid);
                WorkCalendarGroupDto bean = new WorkCalendarGroupDto();
                bean.setOrgCode(sysOrg.getId());
                bean.setOrgName(sysOrg.getOrgname());
                bean.setPorgName(porg.getOrgname());
                bean.setPorgCode(porg.getOrgcode());
                bean.setShowOrgName(porg.getOrgname() + " - " + sysOrg.getOrgname());
                bean.setTmSort(porg.getTmSort());
                result.add(bean);
            }
        }
        if (StringUtils.isNotEmpty(result)) {
            result = result.stream().sorted(Comparator.comparing(WorkCalendarGroupDto::getTmSort)).collect(Collectors.toList());
        }
        return result;
    }

    /**
     * 通过班组查询 人员及其活动信息
     *
     * @param param
     * @return
     * <AUTHOR>
     * @date 2024/7/10
     * @params
     */
    @Override
    public List<PersonTaskDto> getPersonTaskByGroup(WorkCalendarQueryDto param) {
        //根据班组获取全部人员
        List<PersonTaskDto> result = new ArrayList<>();
        if (StringUtils.isEmpty(param.getOrgCode())) {
            return result;
        }
        List<ComponentVo> personsByorg = this.getPersonsByorg(param.getOrgCode());
        if (StringUtils.isEmpty(personsByorg) || StringUtils.isEmpty(personsByorg.get(0).getChildren())) {
            return result;
        }
        //本机构下人员
        Map<String, PanelVo> personNameMap = personsByorg.get(0).getChildren().stream().collect(Collectors.toMap(PanelVo::getCode, Function.identity()));
        //本机构人员id
        List<String> personIds = personsByorg.get(0).getChildren().stream().map(PanelVo::getCode).sorted().collect(Collectors.toList());
        //查询负责人列表
//        Long[] longs = this.logTime("查询责任人", null);
        List<JobListExampleDutyPerson> activityListByUser = getJobListExampleDutyPeople(personIds);
//        this.logTime("查询个人任务情况结束", longs);
        if (StringUtils.isEmpty(activityListByUser)) return null;
        activityListByUser = activityListByUser.stream().filter(item -> item.getPersonType() == 0).collect(Collectors.toList());
        List<String> activityIds = activityListByUser.stream().map(JobListExampleDutyPerson::getActivityExampleId).distinct().collect(Collectors.toList());
        Long[] longs = this.logTime("查询活动实例", null);
        List<JoblistActivityExample> activityList = getJoblistActivityExamplesByIds(activityIds, 0);
        this.logTime("查询活动" + "结束", longs);
        if (param != null && StringUtils.isNotEmpty(activityList)) {
            if (param.getType() == 1) {
                getShiftTimeScopeByDate(param);
            }
            activityList = activityList.stream().filter(item -> isSameDate(param.getType() == 1 ? item.getSbsj() : item.getBeginDate(), param.getSelectDate(), param.getType())).collect(Collectors.toList());
        }
        List<String> propertiesIds = Optional.ofNullable(activityList).orElse(new ArrayList<>()).stream().map(JoblistActivityExample::getActivityId).distinct().collect(Collectors.toList());
        //过滤掉已经无效的配置
        StandardJobLibQueryDto queryDto = new StandardJobLibQueryDto();
        queryDto.setIdList(propertiesIds);
        queryDto.setTmUsed(null);
        List<JoblistActivityProperties> joblistActivityPropertiesList = methodService.getJoblistActivityPropertiesList(queryDto);
        if (StringUtils.isNotEmpty(joblistActivityPropertiesList)) {
            propertiesIds = joblistActivityPropertiesList.stream().map(JoblistActivityProperties::getId).collect(Collectors.toList());
            List<String> finalPropertiesIds = propertiesIds;
            activityList = Optional.ofNullable(activityList).orElse(new ArrayList<>()).stream().filter(item -> finalPropertiesIds.contains(item.getActivityId())).collect(Collectors.toList());
            activityListByUser = activityListByUser.stream().filter(item -> finalPropertiesIds.contains(item.getActivityPropertiesId())).collect(Collectors.toList());
        }
        //将活动按照人员分组
        Map<String, List<JoblistActivityExample>> groups = new HashMap<>();
        for (JobListExampleDutyPerson personExample : activityListByUser) {
            List<JoblistActivityExample> orDefault = groups.getOrDefault(personExample.getPersonId(), new ArrayList<>());
            List<JoblistActivityExample> collect = activityList.stream().filter(item -> item.getId().equals(personExample.getActivityExampleId())).collect(Collectors.toList());
            for (JoblistActivityExample joblistActivityExample : collect) {
                List<String> ids = orDefault.stream().map(JoblistActivityExample::getId).collect(Collectors.toList());
                if (!ids.contains(joblistActivityExample.getId())) {
                    orDefault.add(joblistActivityExample);
                }
            }
            groups.put(personExample.getPersonId(), orDefault);
        }
        //调整分任务数
        StringBuffer otherWhere = new StringBuffer();
         List<Object> paramList = new ArrayList<>();
        otherWhere.append(" TASK_TYPE = 'ls' ");
        otherWhere.append(" and MONTH = ? ");
        paramList.add(param.getSelectDate());
        List<JobListScore> jobAdjustListScores = SqlInExecutor.inSql("JOBLIST_SCORE","PERSON_ID",personIds,
                otherWhere.toString(),null,JobListScore.class,paramList);

        //获取例外任务
        Map<String, Double> exceptionScoreMap = Optional.ofNullable(jobAdjustListScores).orElse(new ArrayList<>())
                .stream().collect(Collectors.toMap(i -> i.getActivityId() + "_" + i.getPersonId(), i -> i.getAdjustScore(), (v1, v2) -> v1));
        TmTaskInfoVo infoVo = new TmTaskInfoVo();
        if (new Integer(1).equals(param.getType())) {//日
            infoVo.setStartTime(param.getSelectDate() + " 00:00:00");
            infoVo.setEndTime(param.getSelectDate() + " 23:59:59");
        } else {//月
            String monthEnd = DateTimeUtils.getMonthEnd(param.getSelectDate());
            infoVo.setEndTime(monthEnd + " 23:59:59");
            infoVo.setStartTime(param.getSelectDate() + "-01 00:00:00");
        }
        infoVo.setUserIdList(personIds);
        infoVo.setAdjustScoreMap(exceptionScoreMap);
        Map<String, Map<String, Object>> exceptionScore = taskAddService.taskUserScoreSum(infoVo);
        //根据分组进行统计
        for (Map.Entry<String, List<JoblistActivityExample>> entry : groups.entrySet()) {
            PanelVo componentVo = personNameMap.get(entry.getKey());
            PersonTaskDto bean = new PersonTaskDto();
            List<JoblistActivityExample> taskList = entry.getValue();
            List<JoblistActivityExample> doneTaskList = taskList.stream().filter(item -> item.getActivityStatus() == 2).collect(Collectors.toList());
            //完成的工作
            List<String> completedJobs = doneTaskList.stream().map(JoblistActivityExample::getId).collect(Collectors.toList());
            List<JobListExampleDutyPerson> persons = activityListByUser.stream().filter(i -> completedJobs.contains(i.getActivityExampleId()) && entry.getKey().equals(i.getPersonId()) && i.getPersonType() == 0).collect(Collectors.toList());
            bean.setPersonId(entry.getKey());
            bean.setName(componentVo.getLable());
            bean.setPostName(componentVo.getParams().getString("postname"));
            bean.setPostId(componentVo.getParams().getString("postid"));
            bean.setActivityDone(doneTaskList.size());
            bean.setActivitySum(taskList.size());
            //调整分记录
            Map<String, List<JobListScore>> jobListScoreByPersonIdAndDateAndActivityId = this.getJobListScoreByPersonIdAndDateAndActivityId(componentVo.getCode(), param.getSelectDate());
            //对已完成任务按照活动配置id 分组计算每组的总分
            Map<String, Double> originScore = Optional.ofNullable(persons).orElse(new ArrayList<>()).stream()
                    .collect(
                            Collectors.groupingBy(JobListExampleDutyPerson::getActivityPropertiesId,
                                    Collectors.mapping(JobListExampleDutyPerson::getScore,
                                            Collectors.collectingAndThen(Collectors.toList(),
                                                    x -> x.stream().reduce(0d, (a, b) -> {
                                                        return new BigDecimal(String.valueOf(a)).add(new BigDecimal(String.valueOf(b))).doubleValue();
                                                    }))
                                    ))
                    );
            for (Map.Entry<String, Double> stringDoubleEntry : originScore.entrySet()) {
                List<JobListScore> jobListScores = jobListScoreByPersonIdAndDateAndActivityId.get(stringDoubleEntry.getKey());
                if (StringUtils.isNotEmpty(jobListScores)) {
                    JobListScore jobListScore = jobListScores.get(0);
                    //将分组得分替换为调整分
                    originScore.put(stringDoubleEntry.getKey(), jobListScore.getAdjustScore());
                }
            }
            //替换完调整分以后 合计完成得分
            Double completeScore = 0d;
            for (Map.Entry<String, Double> stringDoubleEntry : originScore.entrySet()) {

                completeScore = new BigDecimal(String.valueOf(completeScore)).add(new BigDecimal(String.valueOf((stringDoubleEntry.getValue() == null ? 0 : stringDoubleEntry.getValue())))).doubleValue();
            }
            //例外任务数
            Map<String, Object> v = exceptionScore.get(entry.getKey());
            if (v != null) {
                double score = v.get("score") == null ? 0d : (double) v.get("score");
                completeScore = new BigDecimal(String.valueOf(completeScore)).add(new BigDecimal(String.valueOf(score))).doubleValue();
                int done = v.get("done") == null ? 0 : (int) v.get("done");
                bean.setActivityDone(bean.getActivityDone() + done);
                int total = v.get("total") == null ? 0 : (int) v.get("total");
                bean.setActivitySum(bean.getActivitySum() + total);
            }
            bean.setScore(completeScore);
            String rateStr = "0";
            int activityDone = bean.getActivityDone() == null ? 0 : bean.getActivityDone();
            int activitySum = bean.getActivitySum() == null ? 0 : bean.getActivitySum();
            if (activitySum != 0) {
                rateStr = new BigDecimal(Double.valueOf(Double.valueOf(activityDone) / activitySum * 100)).setScale(2, RoundingMode.HALF_UP).stripTrailingZeros().toPlainString();
            }
            bean.setRate(rateStr + "%");
            result.add(bean);
        }
        //排序
        if (StringUtils.isNotEmpty(result)) {
            result = result.stream()
                    .sorted(Comparator.comparing(PersonTaskDto::getScore).reversed().thenComparing(
                            (o1, o2) -> Collator.getInstance().compare(o1.getName(), o2.getName())
                    ))
                    .collect(Collectors.toList());
        }
        return result;
    }

    private void getShiftTimeScopeByDate(WorkCalendarQueryDto param) {
        //获取选择指定日期的班次 及其 时间范围
        JoblistInputDto bcparam = new JoblistInputDto();
        bcparam.setOrgCode(param.getOrgCode());
        bcparam.setWorkDate(param.getSelectDate());
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        try {
            List<JoblistShiftBcVo> shiftBcList = inputService.getShiftBcList(bcparam);
            List<JoblistShiftBcVo> joblistShiftBcVoList = Optional.ofNullable(shiftBcList).orElse(new ArrayList<>()).stream().filter(item -> item.getOrgCode().equals(param.getOrgCode())).collect(Collectors.toList());
            if (StringUtils.isNotEmpty(joblistShiftBcVoList)) {
                JoblistShiftBcVo joblistShiftBcVo = joblistShiftBcVoList.get(0);
                if (joblistShiftBcVo != null) {
                    String sbsjstr = joblistShiftBcVo.getSbsj();
                    sbsj = sdf.parse(sbsjstr);
                    String xbsjstr = joblistShiftBcVo.getXbsj();
                    xbsj = sdf.parse(xbsjstr);
                }
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 根据人员 日期 活动配置id  获取调整分
     *
     * @return
     * <AUTHOR>
     * @params
     */
    private Map<String, List<JobListScore>> getJobListScoreByPersonIdAndDateAndActivityId(String personId, String date) {
        Where where = Where.create();
        where.eq(JobListScore::getPersonId, personId);
        where.eq(JobListScore::getMonth, date);
        List<JobListScore> jobListScores = dao.rawQueryListByWhere(JobListScore.class, where);
        Map<String, List<JobListScore>> collect = Optional.ofNullable(jobListScores).orElse(new ArrayList<>()).stream().collect(Collectors.groupingBy(JobListScore::getActivityId));
        return collect;

    }


    /**
     * 获取活动得分方案
     *
     * @return
     * <AUTHOR>
     * @params
     */
    @Override
    public Map<String, Double> getScoreByActivityId(List<JoblistActivityProperties> properties) {
        Map<String, Double> result = new HashMap<>();
        if (StringUtils.isEmpty(properties)) {
            return result;
        }
        //查询方案
        List<String> programIds = properties.stream().filter(i -> StringUtils.isNotEmpty(i.getCalcSchemeid())).map(JoblistActivityProperties::getCalcSchemeid).collect(Collectors.toList());
        if (StringUtils.isEmpty(programIds)) {
            return result;
        }
        StandardJobLibQueryDto queryDto = new StandardJobLibQueryDto();
        queryDto.setPidList(programIds);
        List<JoblistProgramAssMethod> joblistProgramList = methodService.getJoblistProgramAssMethodList(queryDto);
        if (StringUtils.isEmpty(joblistProgramList)) {
            return result;
        }
        return getPostScoreByProgram(properties, joblistProgramList);
    }

    private Map<String, Double> getPostScoreByProgram(List<JoblistActivityProperties> properties, List<JoblistProgramAssMethod> joblistProgramList) {
        Map<String, Double> result = new HashMap<>();
        //方案数据分组
        Map<String, List<JoblistProgramAssMethod>> map = joblistProgramList.stream().collect(Collectors.groupingBy(JoblistProgramAssMethod::getPid));
        for (JoblistActivityProperties property : properties) {
            if (StringUtils.isEmpty(property.getCalcSchemeid())) {
                continue;
            }
            //获取方案详细
            List<JoblistProgramAssMethod> program = map.get(property.getCalcSchemeid());
            this.getPostScoreProgramDetail(property.getId(),property.getBasicScore(),property.getCalcType(), program,
                    result);
        }
        return result;
    }

    @Override
    public void getPostScoreProgramDetail(String groupId, Double baseScore, Integer calcType,
                                          List<JoblistProgramAssMethod> program, Map<String,
                    Double> result) {
        if (StringUtils.isEmpty(program)) {
            return;
        }
        for (JoblistProgramAssMethod joblistProgramAssMethod : program) {
            Double score;
            if (calcType == 1) {
                //按照权重计算分
                Double basicScore = baseScore == null ? 0d : baseScore;
                Double assessWeight = joblistProgramAssMethod.getAssessWeight() == null ? 0d : joblistProgramAssMethod.getAssessWeight();
                BigDecimal bd1 = new BigDecimal(Double.toString(basicScore));
                BigDecimal bd2 = new BigDecimal(Double.toString(assessWeight));
                score = bd1.multiply(bd2).doubleValue();
            } else {
                score = joblistProgramAssMethod.getAssessScore() == null ? 0d : joblistProgramAssMethod.getAssessScore();
            }
            String key = groupId + "_" + joblistProgramAssMethod.getAssessId();
            result.put(key, score);
        }
    }


    /**
     * 判断时间是否相等
     *
     * @return
     * <AUTHOR>
     * @date 2024/7/10
     * @params beginDate是日期时间，selectdate可能是月也可能是日期 type 标志日期还是月
     */
    private Boolean isSameDate(String beginDate, String selectDate, Integer type) {
        if (StringUtils.isEmpty(beginDate) || "null".equals(beginDate)) {
            return false;
        }
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String date;
        if (type == 0) {
            //月
            date = beginDate.substring(0, 7);
            return date.equals(selectDate);
        } else {
            //日
            Date bdate;
            try {
                bdate = sdf.parse(beginDate);
            } catch (ParseException e) {
                throw new RuntimeException(e);
            }
            return sbsj.equals(bdate);
        }
    }

    /**
     * 查询台历任务
     *
     * @param param
     * @return
     * <AUTHOR>
     * @date 2024/7/9
     * @params
     */
    @Override
    public List<WorkCalendarGroupTaskDto> getWorkCalendarTaskByGroup(WorkCalendarQueryDto param) {
        List<WorkCalendarGroupTaskDto> result = new ArrayList<>();
        //通过人员id根据责任人查询活动
        if (StringUtils.isEmpty(param.getPersonId())) {
            return result;
        }
        List<String> personIds = new ArrayList<>();
        personIds.add(param.getPersonId());
        List<SysEmployeeOrgPost> employeeOrgPost = orgPostService.getEmployeeOrgPost(personIds);
        String orgId = null;
        if (StringUtils.isNotEmpty(employeeOrgPost)) {
            orgId = employeeOrgPost.get(0).getOrgcode();
        }
        List<JobListExampleDutyPerson> activityListByUser = getJobListExampleDutyPeople(personIds);
        if (StringUtils.isEmpty(activityListByUser)) return null;
        //只统计负责人数据
        activityListByUser = activityListByUser.stream().filter(item -> item.getPersonType() == 0).collect(Collectors.toList());
        Map<String, JobListExampleDutyPerson> exampleDutyPersonMap = activityListByUser.stream().filter(i -> i.getPersonType() == 0).collect(Collectors.toMap(JobListExampleDutyPerson::getActivityExampleId, Function.identity(), (v1, v2) -> v1));
        List<String> propertiesIds = activityListByUser.stream().map(JobListExampleDutyPerson::getActivityPropertiesId).distinct().collect(Collectors.toList());
        StandardJobLibQueryDto queryDto = new StandardJobLibQueryDto();
        queryDto.setIdList(propertiesIds);
        queryDto.setSortType(1);
        queryDto.setTmUsed(null);
        List<JoblistActivityProperties> joblistActivityPropertiesList = methodService.getJoblistActivityPropertiesList(queryDto);
        if (StringUtils.isEmpty(joblistActivityPropertiesList)) {
            return result;
        }
        List<String> idList = joblistActivityPropertiesList.stream().map(JoblistActivityProperties::getId).collect(Collectors.toList());
        //对配置进行排序
        //获取核算对象名称
        MethodQueryDto queryDto1 = new MethodQueryDto();
        queryDto1.setIdList(idList);
        queryDto1.setUsedCase("all");
        List<Costuint> costuintList = unitMethodService.getCostuintList(queryDto1);
        Map<String, String> costMap = new HashMap<>();
        if (StringUtils.isNotEmpty(costuintList)) {
            costMap = costuintList.stream().collect(Collectors.toMap(Costuint::getId, (item) -> {
                if(item.getTmused()==1){
                    return item.getName();
                }else{
                    return item.getName()+"(已删除)";
                }
            }, (k1, k2) -> k1));
        }
        //优先级
        Map<String, String> priorityNameMap = new HashMap<>();
        Map<String, Integer> prioritySortMap = new HashMap<>();
        StandardJobLibQueryDto priorityQueryDto = new StandardJobLibQueryDto();
        priorityQueryDto.setPtype(1);
        priorityQueryDto.setOrgid(param.getPorgCode());
        List<JoblistExtPropertiesConfig> initExtPropertiesData = jobLibConfigService.getExtPropertiesListByInit(priorityQueryDto);
        if (StringUtils.isNotEmpty(initExtPropertiesData)) {
            priorityNameMap = initExtPropertiesData.stream()
                    .collect(Collectors.toMap(JoblistExtPropertiesConfig::getId, JoblistExtPropertiesConfig::getPname));
            prioritySortMap = initExtPropertiesData.stream()
                    .collect(Collectors.toMap(JoblistExtPropertiesConfig::getId, JoblistExtPropertiesConfig::getTmsort));
        }
        //分类
        StandardJobLibQueryDto classQueryDto = new StandardJobLibQueryDto();
        classQueryDto.setOrgid(param.getPorgCode());
        classQueryDto.setUseCase("all");
        List<JoblistClassVo> joblistClassCombList = jobLibConfigService.getJoblistClassCombList(classQueryDto, "root");
        Map<String, String> classMap = new HashMap<>();
        if (StringUtils.isNotEmpty(joblistClassCombList)) {
            classMap = joblistClassCombList.stream().collect(Collectors.toMap(JoblistClassVo::getId, JoblistClassVo::getNodeShowName));
        }
        for (JoblistActivityProperties joblistActivityProperties : joblistActivityPropertiesList) {
            joblistActivityProperties.setPriorityName(priorityNameMap.getOrDefault(joblistActivityProperties.getPriorityid(), "无效优先级"));
            joblistActivityProperties.setPrioritySort(prioritySortMap.getOrDefault(joblistActivityProperties.getPriorityid(), -1));
            joblistActivityProperties.setClassName(classMap.size() == 0 ? "无效分类" : classMap.getOrDefault(joblistActivityProperties.getClassid(), "无效分类"));
            joblistActivityProperties.setUnitName(costMap.size() == 0 ? "无效活动名称" : costMap.getOrDefault(joblistActivityProperties.getId(), "无效活动名称"));

        }
        //重排序
        joblistActivityPropertiesList = joblistActivityPropertiesList.stream()
                .sorted(Comparator.comparing(JoblistActivityProperties::getPrioritySort)
                        .thenComparing((o1, o2) -> {
                            if (StringUtils.isEmpty(o1.getClassName())) {
                                return 1;
                            } else if (StringUtils.isEmpty(o2.getClassName())) {
                                return -1;
                            } else {
                                return Collator.getInstance(Locale.CHINESE).compare(o1.getClassName(), o2.getClassName());
                            }
                        })
                        .thenComparing((o1, o2) -> {
                                    if (StringUtils.isEmpty(o1.getUnitName())) {
                                        return 1;
                                    } else if (StringUtils.isEmpty(o2.getUnitName())) {
                                        return -1;
                                    } else {
                                        return Collator.getInstance(Locale.CHINESE).compare(o1.getUnitName(), o2.getUnitName());
                                    }
                                }
                        ))
                .collect(Collectors.toList());

        List<String> propertiesId = joblistActivityPropertiesList.stream().map(JoblistActivityProperties::getId).collect(Collectors.toList());
        Map<String, Double> scoreByActivityId = getScoreByActivityId(joblistActivityPropertiesList);
        //通过属性id查询配置
        List<String> activityIds = activityListByUser.stream().map(JobListExampleDutyPerson::getActivityExampleId).distinct().collect(Collectors.toList());
        List<JoblistActivityExample> activityList = getJoblistActivityExamplesByIds(activityIds, 0);
        //计算活动得分
        //筛选指定日期 活动
        if (param.getType() == 1) {
            getShiftTimeScopeByDate(param);
        }
        activityList = Optional.ofNullable(activityList).orElse(new ArrayList<>())
                .stream()
                .filter(item -> isSameDate(param.getType() == 1 ? item.getSbsj() : item.getBeginDate(), param.getSelectDate(), param.getType()))
                .collect(Collectors.toList());
        //按照配置分组
        if (StringUtils.isNotEmpty(activityList)) {

            Map<String, List<JoblistActivityExample>> doneList = activityList.stream().filter(item -> item.getActivityStatus() == 2).collect(Collectors.groupingBy(JoblistActivityExample::getActivityId));
            Map<String, List<JoblistActivityExample>> allList = activityList.stream().collect(Collectors.groupingBy(JoblistActivityExample::getActivityId));
            //频次
            StandardJobLibQueryDto cycleQuery = new StandardJobLibQueryDto();
            cycleQuery.setOrgid(param.getPorgCode());
            cycleQuery.setQueryType("orgSys");
            List<CycleScheme> list = jobLibConfigService.getCycleSchemeList(cycleQuery);
            Map<String, String> cycleMap = Optional.ofNullable(list).orElse(new ArrayList<>()).stream().collect(Collectors.toMap(CycleScheme::getId, CycleScheme::getSname, (k1, k2) -> k1));
            //获取当前班次
            //人员获取全部的工作
            for (JoblistActivityProperties joblistActivityProperties : joblistActivityPropertiesList) {
                WorkCalendarGroupTaskDto workCalendarGroupTaskDto = new WorkCalendarGroupTaskDto();
                List<JoblistActivityExample> dones = doneList.get(joblistActivityProperties.getId());
                List<JoblistActivityExample> alls = allList.get(joblistActivityProperties.getId());
                Double doneScore = 0d;
                Double adjustScore = 0d;
                Double allScore = 0d;
                int allSize = Optional.ofNullable(alls).orElse(new ArrayList<>()).size();
                //没有这个任务时跳过
                if (allSize == 0) continue;
                //有这个任务时
                workCalendarGroupTaskDto.setFinishCase(Optional.ofNullable(dones).orElse(new ArrayList<>()).size() + " / " + allSize);
                //实际得分
                Map<String, List<JobListScore>> jobListScoreByPersonIdAndDateAndActivityId = this.getJobListScoreByPersonIdAndDateAndActivityId(param.getPersonId(), param.getSelectDate());
                List<JobListScore> jobListScores = jobListScoreByPersonIdAndDateAndActivityId.get(joblistActivityProperties.getId());
                JobListScore jobListScore = null;
                //计算应得总分
                if (StringUtils.isNotEmpty(dones) && exampleDutyPersonMap != null) {
                    for (JoblistActivityExample done : dones) {
                        JobListExampleDutyPerson person = exampleDutyPersonMap.get(done.getId());
                        doneScore = new BigDecimal(String.valueOf(doneScore)).add(new BigDecimal(String.valueOf((person == null ? 0 : (person.getScore() == null ? 0 : person.getScore()))))).doubleValue();
                    }
                }
                if (StringUtils.isNotEmpty(jobListScores)) {
                    //获取调整分
                    jobListScore = jobListScores.get(0);
//                    doneScore = jobListScore.getScore();
                    adjustScore = jobListScore.getAdjustScore();
                } else {
                    //调整分为空  默认初始值
                    adjustScore = doneScore;
                }
                //预期得分
                if (StringUtils.isNotEmpty(alls) && exampleDutyPersonMap != null) {
                    for (JoblistActivityExample all : alls) {
                        JobListExampleDutyPerson person = exampleDutyPersonMap.get(all.getId());
                        allScore = new BigDecimal(String.valueOf(allScore)).add(new BigDecimal(String.valueOf((person == null ? 0 : (person.getScore() == null ? 0 : person.getScore()))))).doubleValue();
                    }
                }
                //单项分
                String scoreKey = joblistActivityProperties.getId() + "_" + param.getPostId();
                Double score = scoreByActivityId.get(scoreKey);
                workCalendarGroupTaskDto.setScore(score);
                //原总分 计算得
                workCalendarGroupTaskDto.setOriginScore(allScore);
                //实际总分
                workCalendarGroupTaskDto.setSumScore(adjustScore);
                workCalendarGroupTaskDto.setFactScore(doneScore);
                workCalendarGroupTaskDto.setMonth(param.getSelectDate());
                workCalendarGroupTaskDto.setPersonId(param.getPersonId());
                workCalendarGroupTaskDto.setActivityId(joblistActivityProperties.getId());
                workCalendarGroupTaskDto.setPostId(param.getPostId());
                workCalendarGroupTaskDto.setNodeShowName(joblistActivityProperties.getClassName());
                workCalendarGroupTaskDto.setUnitName(costMap.size() == 0 ? "无效活动名称" : costMap.getOrDefault(joblistActivityProperties.getId(), "无效活动名称"));
                workCalendarGroupTaskDto.setSname("none".equals(joblistActivityProperties.getFrequencyid()) ? "不定期" : cycleMap.get(joblistActivityProperties.getFrequencyid()));
                workCalendarGroupTaskDto.setStandardDuration(joblistActivityProperties.getStandardDuration());
                //标记为岗位任务
                workCalendarGroupTaskDto.setTaskType("gw");
                if (jobListScore != null) {
                    //调整原因
                    workCalendarGroupTaskDto.setAdjustReason(jobListScore.getAdjustReason());
                    //调整人id
                    workCalendarGroupTaskDto.setAdjustPersonCode(jobListScore.getAdjustPersonCode());
                    //调整人
                    workCalendarGroupTaskDto.setAdjustPerson(jobListScore.getAdjustPerson());
                    //调整时间
                    workCalendarGroupTaskDto.setAdjustTime(jobListScore.getAdjustTime());
                    workCalendarGroupTaskDto.setSumScore(jobListScore.getAdjustScore());
                    //调整分id
                    workCalendarGroupTaskDto.setAdjustId(jobListScore.getId());
                }
                result.add(workCalendarGroupTaskDto);
            }
        }
        //查询例外任务
        TmTaskInfoVo infoVo = new TmTaskInfoVo();
        if (param.getType() == 0) {
            //月统计
            String monthEnd = DateTimeUtils.getMonthEnd(param.getSelectDate());
            infoVo.setEndTime(monthEnd + " 23:59:59");
            infoVo.setStartTime(param.getSelectDate() + "-01 00:00:00");
        } else {
            //日统计
            infoVo.setStartTime(param.getSelectDate() + " 00:00:00");
            infoVo.setEndTime(param.getSelectDate() + " 23:59:59");
        }
        infoVo.setUserIdList(personIds);
        Map<String, List<TmtaskVo>> exceptionList = taskAddService.taskUserScoreList(infoVo);
        if (exceptionList != null && (!exceptionList.isEmpty())) {
            for (Map.Entry<String, List<TmtaskVo>> tmtaskVoEntry : exceptionList.entrySet()) {
                List<TmtaskVo> taskList = tmtaskVoEntry.getValue();
                if (StringUtils.isEmpty(taskList)) {
                    continue;
                }
                List<String> taskIds = taskList.stream().map(TmtaskVo::getId).distinct().collect(Collectors.toList());
                StringBuffer whereStr = new StringBuffer();
                List<Object> paramList = new ArrayList<>();
                whereStr.append(" PERSON_ID = ? ");
                paramList.add(param.getPersonId());
                whereStr.append(" and POST_ID = ? ");
                paramList.add(param.getPostId());
                whereStr.append(" and MONTH = ? ");
                paramList.add(param.getSelectDate());
                List<JobListScore> jobListScores = SqlInExecutor.inSql("JOBLIST_SCORE","ACTIVITY_ID",taskIds,whereStr.toString()
                        ,null,JobListScore.class,paramList);
                Map<String, JobListScore> exceptionScoreMap = jobListScores.stream().collect(Collectors.toMap(JobListScore::getActivityId, Function.identity(), (v1, v2) -> v1));
                for (TmtaskVo tmtaskVo : taskList) {
                    WorkCalendarGroupTaskDto workCalendarGroupTaskDto = new WorkCalendarGroupTaskDto();
                    workCalendarGroupTaskDto.setNodeShowName("例外任务");
                    workCalendarGroupTaskDto.setUnitName(tmtaskVo.getTitle());
                    workCalendarGroupTaskDto.setFinishCase(tmtaskVo.getTaskStatus() == 2 ? "1 / 1" : "0 / 1");
                    workCalendarGroupTaskDto.setOriginScore(tmtaskVo.getAssessCompleteVal() == null ? 0d : tmtaskVo.getAssessCompleteVal());
                    workCalendarGroupTaskDto.setScore(tmtaskVo.getAssessCompleteVal() == null ? 0d : tmtaskVo.getAssessCompleteVal());
                    workCalendarGroupTaskDto.setFactScore(tmtaskVo.getTaskStatus() == 2 ? (tmtaskVo.getAssessCompleteVal() == null ? 0d : tmtaskVo.getAssessCompleteVal()) : 0d);
                    workCalendarGroupTaskDto.setSumScore(tmtaskVo.getAssessVal());
                    workCalendarGroupTaskDto.setActivityId(tmtaskVo.getId());
                    workCalendarGroupTaskDto.setMonth(param.getSelectDate());
                    workCalendarGroupTaskDto.setPersonId(param.getPersonId());
                    workCalendarGroupTaskDto.setPostId(param.getPostId());
                    //标记为临时任务
                    workCalendarGroupTaskDto.setTaskType("ls");
                    if (exceptionScoreMap.containsKey(tmtaskVo.getId())) {
                        JobListScore jobListScore = exceptionScoreMap.get(tmtaskVo.getId());
                        //调整原因
                        workCalendarGroupTaskDto.setAdjustReason(jobListScore.getAdjustReason());
                        //调整人id
                        workCalendarGroupTaskDto.setAdjustPersonCode(jobListScore.getAdjustPersonCode());
                        //调整人
                        workCalendarGroupTaskDto.setAdjustPerson(jobListScore.getAdjustPerson());
                        //调整时间
                        workCalendarGroupTaskDto.setAdjustTime(jobListScore.getAdjustTime());
                        workCalendarGroupTaskDto.setSumScore(jobListScore.getAdjustScore());
                        //调整分id
                        workCalendarGroupTaskDto.setAdjustId(jobListScore.getId());
                    }
                    result.add(workCalendarGroupTaskDto);
                }
            }
        }
        return result;
    }

    /**
     * 保存调整分
     *
     * @return
     * <AUTHOR>
     * @date 2024/7/11
     * @params
     */
    @Override
    public Boolean saveAdjustScore(List<WorkCalendarGroupTaskDto> workCalendarGroupTaskDtos) {
        Boolean flag = false;
        if (StringUtils.isEmpty(workCalendarGroupTaskDtos)) {
            return flag;
        }
        //查询全部
        Where where = Where.create();
        List<JobListScore> jobListScores = dao.rawQueryListByWhere(JobListScore.class, where);
        Map<String, JobListScore> dataMap = new HashMap<>();
        if (StringUtils.isNotEmpty(jobListScores)) {
            dataMap = jobListScores.stream().collect(Collectors.toMap(JobListScore::getId, Function.identity()));
        }
        List<JobListScore> insertList = new ArrayList<>();
        List<JobListScore> updateList = new ArrayList<>();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date now = new Date();
        String nowStr = sdf.format(now);
        for (WorkCalendarGroupTaskDto workCalendarGroupTaskDto : workCalendarGroupTaskDtos) {
            if (StringUtils.isEmpty(workCalendarGroupTaskDto.getAdjustId())) {
                JobListScore jobListScore = new JobListScore();
                jobListScore.setId(TMUID.getUID());
                jobListScore.setScore(workCalendarGroupTaskDto.getOriginScore());
                jobListScore.setMonth(workCalendarGroupTaskDto.getMonth());
                jobListScore.setPersonId(workCalendarGroupTaskDto.getPersonId());
                jobListScore.setActivityId(workCalendarGroupTaskDto.getActivityId());
                jobListScore.setPostId(workCalendarGroupTaskDto.getPostId());
                jobListScore.setAdjustScore(workCalendarGroupTaskDto.getSumScore());
                jobListScore.setAdjustPerson(SysUserHolder.getCurrentUser().getRealName());
                jobListScore.setAdjustPersonCode(SysUserHolder.getCurrentUser().getId());
                jobListScore.setAdjustReason(workCalendarGroupTaskDto.getAdjustReason());
                jobListScore.setAdjustTime(nowStr);
                jobListScore.setTaskType(workCalendarGroupTaskDto.getTaskType());
                insertList.add(jobListScore);
            } else {
                JobListScore jobListScore = dataMap.get(workCalendarGroupTaskDto.getAdjustId());
                jobListScore.setScore(workCalendarGroupTaskDto.getOriginScore());
                jobListScore.setAdjustScore(workCalendarGroupTaskDto.getSumScore());
                jobListScore.setAdjustPerson(SysUserHolder.getCurrentUser().getRealName());
                jobListScore.setAdjustPersonCode(SysUserHolder.getCurrentUser().getId());
                jobListScore.setAdjustReason(workCalendarGroupTaskDto.getAdjustReason());
                jobListScore.setAdjustTime(nowStr);
                jobListScore.setTaskType(workCalendarGroupTaskDto.getTaskType());
                updateList.add(jobListScore);
            }
        }
        if (StringUtils.isNotEmpty(insertList)) {
            flag = dao.insertBatch(insertList) > 0;
        }
        if (StringUtils.isNotEmpty(updateList)) {
            flag = dao.updateByIdBatch(updateList) > 0;
        }
        return flag;
    }


    /**
     * 频次记录清理   按照活动结束时间清
     *
     * @return
     * <AUTHOR>
     * @date 2024/7/3
     * @params
     */
    public void clearRecord() {
        Date date = new Date();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        String format = sdf.format(date);
        Where where = Where.create();
        where.lt(JoblistActivityProperties::getEndDate, format);
        where.ne(JoblistActivityProperties::getNoEndDate, 1);
        List<JoblistActivityProperties> joblistActivityProperties = dao.rawQueryListByWhere(JoblistActivityProperties.class, where);
        if (StringUtils.isNotEmpty(joblistActivityProperties)) {
            //取出已经失效的活动id
            List<String> unvaildIds = joblistActivityProperties.stream().map(JoblistActivityProperties::getId).collect(Collectors.toList());
            //清除redis中的记录
            Set<String> keys = new HashSet<>();
            for (String unvaildId : unvaildIds) {
                keys = redisUtil.redisTemplate.keys(PREFIX + unvaildId + "*");
                redisUtil.delete(keys);
            }
            //清除数据库持久数据
            Where where1 = Where.create();
            where1.like(JoblistGeneraterReCord::getActivityRecordKey, keys.toArray());
            List<JoblistGeneraterReCord> joblistGeneraterReCords = dao.rawQueryListByWhere(JoblistGeneraterReCord.class, where1);
            if (StringUtils.isNotEmpty(joblistGeneraterReCords)) {
                for (JoblistGeneraterReCord joblistGeneraterReCord : joblistGeneraterReCords) {
                    joblistGeneraterReCord.setTmUsed(0);
                }
                dao.updateByIdBatch(joblistGeneraterReCords);
            }
        }
    }
    @Override
    public void saveActivityStatusHandler(JSONArray jsonArray) {
        this.saveActivityStatusHandler(jsonArray,true);
    }
    /**
     * 台账保存触发活动状态更新
     *
     * @param jsonArray
     * @return
     * <AUTHOR>
     * @date 2024/8/5
     * @params [{taskId,rowFlag}]
     */
    @Override
    public void saveActivityStatusHandler(JSONArray jsonArray, Boolean isUpdateFeedPerson) {
        if (jsonArray == null || jsonArray.size() == 0) {
            return;
        }
        Set<String> idList = new HashSet<>();
        for (int i = 0; i < jsonArray.size(); i++) {
            JSONObject jsonObject = jsonArray.getJSONObject(i);
            String taskId = jsonObject.getString("taskId");
            idList.add(taskId);
        }
        //获取活动
        List<String> idListArray = new ArrayList<>(idList);
        List<JoblistActivityExample> exampleList = this.getJoblistActivityExamplesByIds(idListArray, null);
        Map<String, Integer> map = new HashMap<>();
        if (StringUtils.isNotEmpty(exampleList)) {
            Map<String, List<JoblistActivityExample>> groupMap = exampleList.stream().collect(Collectors.groupingBy(JoblistActivityExample::getActivityId));
            List<String> propertiesIds = exampleList.stream().map(JoblistActivityExample::getActivityId).distinct().collect(Collectors.toList());
            StandardJobLibQueryDto queryDto = new StandardJobLibQueryDto();
            queryDto.setIdList(propertiesIds);
            queryDto.setTmUsed(null);
            List<JoblistActivityProperties> list = methodService.getJoblistActivityPropertiesList(queryDto);
            if (StringUtils.isNotEmpty(list)) {
                List<JoblistConfirm> confirmList = new ArrayList<>();
                for (JoblistActivityProperties joblistActivityPropertiesVo : list) {
                    Integer confirmType = joblistActivityPropertiesVo.getConfirmType();
                    String id = joblistActivityPropertiesVo.getId();
                    List<JoblistActivityExample> joblistActivityExamples = groupMap.get(id);
                    if (StringUtils.isNotEmpty(joblistActivityExamples)) {
                        for (JoblistActivityExample example : joblistActivityExamples) {
                            String exampleId = example.getId();
                            Integer status = null;
                            if (confirmType == 0) {
                                //自动确认  直接已完成
                                status = 2;
                                //生成确认信息
                                JoblistConfirm confirm = new JoblistConfirm();
                                confirm.setActivityPropertiesId(id);
                                confirm.setActivityInstanceId(exampleId);
                                confirm.setUpdateType(0);
                                //获取自动确认人
                                SysEmployeeInfo autoConfirmPerson = getAutoConfirmPerson(example);
                                if (autoConfirmPerson != null) {
                                    confirm.setConfirmPerson(autoConfirmPerson.getEmpname());
                                    confirm.setConfirmPersonId(autoConfirmPerson.getId());
                                }
                                confirm.setActivityFeedContent("自动确认，确认人："
                                        + (StringUtils.isNotEmpty(confirm.getConfirmPerson()) ?
                                        confirm.getConfirmPerson() : ""));
                                confirmList.add(confirm);
                            } else
                                //手动确认
                                status = 1;
                            map.put(exampleId, status);
                        }
                    }
                }
                if (StringUtils.isNotEmpty(confirmList)) {
                    inputService.autoConfirm(confirmList);
                }
                this.updateActivityStatusByActivityId(map, isUpdateFeedPerson, () -> {
                    return exampleList;
                });

            }
        }
    }

    private SysEmployeeInfo getAutoConfirmPerson(JoblistActivityExample example) {
        //自动确认 自动设置班长为确认人
        EmpOrgPostParamDto paramDto = new EmpOrgPostParamDto();
        paramDto.setUsed(1);
        paramDto.setOrgcode(example.getOrgCode());
        List<SysEmployeeOrgPost> employeeOrgPost = orgPostService.getEmployeeOrgPost(paramDto);
        if (StringUtils.isNotEmpty(employeeOrgPost)) {
            //根据机构获取岗位代码列表
            List<String> postIds = employeeOrgPost.stream().map(SysEmployeeOrgPost::getPostid).distinct().collect(Collectors.toList());
            String responsiblePost = null;
            for (String postId : postIds) {
                SysPost postById = postService.findPostById(postId);
                if (ObjUtils.notEmpty(postById)) {
                    //获取到岗位
                    if (postById.getIsHead() != null && postById.getIsHead() == 1) {
                        //找到负责人岗位
                        responsiblePost = postId;
                        break;
                    }
                }
            }
            if (StringUtils.isNotEmpty(responsiblePost)) {
                paramDto.setPostid(responsiblePost);
                List<SysEmployeeOrgPost> employeeList = orgPostService.getEmployeeOrgPost(paramDto);
                String empid = employeeList.get(0).getEmpid();
                return employeeInfoService.findEmployeeById(empid);
            }
        }
        return null;
    }

    /**
     * 获取活动明细统计
     *
     * @param dto
     * @return
     * <AUTHOR>
     * @date 2025/3/17
     * @params
     */
    @Override
    public List<WorkCalendarGroupTaskDto> activityDetail(WorkCalendarAcitityDetailDto dto) {
        List<WorkCalendarGroupTaskDto> result = new ArrayList<>();
        //查询日期范围内的活动实例主数据
        JobInstanceListDto jobInstanceListDto = new JobInstanceListDto();
        jobInstanceListDto.setActivityId(dto.getActivityPropertyId());
        jobInstanceListDto.setStartDate(dto.getStartDate());
        jobInstanceListDto.setEndDate(dto.getEndDate());
        List<JoblistActivityExample> p_example = getJoblistActivityExamplesDetailByQueryFromDb(jobInstanceListDto);
        if (StringUtils.isEmpty(p_example)) {
            return result;
        }
        //根据筛选活动主数据为时间范围内的当班班次
        List<ShiftForeignVo> shiftList = new ArrayList<>();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Date start = null;
        Date end = null;
        try {
            start = sdf.parse(dto.getStartDate());
            end = sdf.parse(dto.getEndDate());
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
        if (start != null && end != null) {
            for (Date cur = start; cur.before(end) || cur.equals(end); cur = DateTimeUtils.doDate(cur, 1)) {
                String rq = sdf.format(cur);
                shiftList.addAll(shiftService.getShiftByObjCode(rq, dto.getPersonOrg()));
            }
        }
        if (StringUtils.isEmpty(shiftList)) {
            return result;
        }
        List<String> shiftIds =
                shiftList.stream().map(ShiftForeignVo::getShiftClassCode).distinct().collect(Collectors.toList());
        Map<String, String> shiftNameMap = shiftList.stream().collect(Collectors.toMap(ShiftForeignVo::getShiftClassCode,
                ShiftForeignVo::getShiftClassName, (key1, key2) -> key1));
        p_example = p_example.stream().filter(item -> shiftIds.contains(item.getShiftClassCode())).collect(Collectors.toList());
        //查询活动配置
        StandardJobLibQueryDto queryDto = new StandardJobLibQueryDto();
        queryDto.setId(dto.getActivityPropertyId());
        queryDto.setSortType(1);
        queryDto.setTmUsed(null);
        List<JoblistActivityProperties> joblistActivityPropertiesList = methodService.getJoblistActivityPropertiesList(queryDto);
        if (StringUtils.isEmpty(joblistActivityPropertiesList)) {
            return result;
        }
        JoblistActivityProperties activityConfig = joblistActivityPropertiesList.get(0);
        //查询活动的配置属性
        //优先级
        StandardJobLibQueryDto queryPriority = new StandardJobLibQueryDto();
        queryPriority.setPtype(1);
        queryPriority.setId(activityConfig.getPriorityid());
        List<JoblistExtPropertiesConfig> joblistExtPropertiesConfigList = methodService.getJoblistExtPropertiesConfigList(queryPriority);
        if (StringUtils.isNotEmpty(joblistExtPropertiesConfigList)) {
            activityConfig.setPriorityName(joblistExtPropertiesConfigList.get(0).getPname());
        } else {
            activityConfig.setPriorityName("无效优先级");
        }
        //分类
        StandardJobLibQueryDto queryClassDto = new StandardJobLibQueryDto();
        queryClassDto.setId(activityConfig.getClassid());
        List<JoblistClass> joblistClassList = methodService.getJoblistClassList(queryClassDto);
        if (StringUtils.isNotEmpty(joblistClassList)) {
            activityConfig.setClassName(joblistClassList.get(0).getCname());
        } else {
            activityConfig.setClassName("无效分类");
        }
        //活动名称
        MethodQueryDto queryDto1 = new MethodQueryDto();
        List<String> unitIds = new ArrayList<>();
        unitIds.add(dto.getActivityPropertyId());
        queryDto1.setIdList(unitIds);
        List<Costuint> costuintList = unitMethodService.getCostuintList(queryDto1);
        if (StringUtils.isNotEmpty(costuintList)) {
            String name = costuintList.get(0).getName();
            activityConfig.setUnitName(name);
        } else {
            activityConfig.setUnitName("无效活动名称");
        }
        //频次
        StandardJobLibQueryDto queryFrequence = new StandardJobLibQueryDto();
        queryFrequence.setId(activityConfig.getFrequencyid());
        List<CycleScheme> cycleSchemeList = methodService.getCycleSchemeList(queryFrequence);

        //查询分数清况
        List<String> pExampleIds = p_example.stream().map(JoblistActivityExample::getId).collect(Collectors.toList());

        List<JoblistActivityExample> activityChildren = getActivityListByPidList(0, null, pExampleIds);
        List<String> childrenIds = Optional.ofNullable(activityChildren).orElse(new ArrayList<>()).stream().map(JoblistActivityExample::getId).collect(Collectors.toList());
        jobInstanceListDto.setOrgCode(dto.getPersonOrg());
        List<JobListExampleDutyPerson> dutyPersons = getDutyPersonByActivityId(childrenIds, dto.getPersonId(),jobInstanceListDto);
        List<String> personActivity =
                Optional.ofNullable(dutyPersons)
                        .orElse(new ArrayList<>())
                        .stream()
                        .map(JobListExampleDutyPerson::getActivityExampleId)
                        .collect(Collectors.toList());
        List<String> personPid = Optional.ofNullable(activityChildren)
                .orElse(new ArrayList<>())
                .stream()
                .filter(item -> personActivity.contains(item.getId()))
                .map(JoblistActivityExample::getPid)
                .distinct()
                .collect(Collectors.toList());
        p_example = p_example
                .stream()
                .filter(item-> personPid.contains(item.getId()))
                .collect(Collectors.toList());
        for (JoblistActivityExample joblistActivityExample : p_example) {
            WorkCalendarGroupTaskDto bean = new WorkCalendarGroupTaskDto();
            //获取活动配置属性
            bean.setId(joblistActivityExample.getId());
            bean.setActivityId(activityConfig.getId());
            bean.setNodeShowName(activityConfig.getClassName());
            String shiftName = shiftNameMap.get(joblistActivityExample.getShiftClassCode());
            bean.setUnitName(activityConfig.getUnitName() + "(" + joblistActivityExample.getTbrq() +(StringUtils.isNotEmpty(shiftName)?shiftName:"") +")");
            if (StringUtils.isNotEmpty(cycleSchemeList)) {
                bean.setSname(cycleSchemeList.get(0).getSname());
            } else {
                bean.setSname("不定期");
            }
            bean.setStandardDuration(activityConfig.getStandardDuration());
            //获取分数属性
            if (StringUtils.isNotEmpty(dutyPersons)) {
                Double score = dutyPersons.get(0).getScore();
                bean.setScore(score);
            }
            bean.setStartDate(joblistActivityExample.getBeginDate());
            bean.setStatus(joblistActivityExample.getActivityStatus());
            result.add(bean);
        }
        return result;
    }

    public List<JoblistActivityExample> getJoblistActivityExamplesDetailByQueryFromDb(JobInstanceListDto jobInstanceListDto) {
        //查询主任务
        Where where = Where.create();
        where.eq(JoblistActivityExample::getTmUsed, 1);
        where.eq(JoblistActivityExample::getIsParent, 1);
        //班次id查询
        if (StringUtils.isNotEmpty(jobInstanceListDto.getActivityId())) {
            where.eq(JoblistActivityExample::getActivityId, jobInstanceListDto.getActivityId());
        }
        //上下班查询
        if (StringUtils.isNotEmpty(jobInstanceListDto.getEndDate())) {
            where.le(JoblistActivityExample::getTbrq, jobInstanceListDto.getEndDate());
        }
        if (StringUtils.isNotEmpty(jobInstanceListDto.getStartDate())) {
            where.ge(JoblistActivityExample::getTbrq, jobInstanceListDto.getStartDate());
        }
        //父数据
        Order order = Order.create();
        order.orderByAsc(JoblistActivityExample::getTmSort);
        return dao.rawQueryListByWhere(JoblistActivityExample.class, where, order);
    }
    /**
     * 获取活动详情指标结果
     *
     * @param dto
     * @return
     * <AUTHOR>
     * @date 2025/3/18
     * @params
     */
    @Autowired
    private IJoblistQualityIndexService qualityIndexService;

    @Autowired
    private IQualityResultService qualityResultService;

    @Autowired
    private IAccountConfigService accountConfigService;

    @Override
    public JSONObject getQualityIndex(WorkCalendarAcitityDetailDto dto) {
        JSONObject result = new JSONObject();
        //查询活动的指标信息
        JoblistQualityIndexQueryDto indexQueryDto = new JoblistQualityIndexQueryDto();
        indexQueryDto.setObjectId(dto.getActivityPropertyId());
        indexQueryDto.setIndexName(dto.getIndexName());
        List<QualityIndex> qualityIndices = qualityIndexService.queryJoblistQualityIndexByPage(indexQueryDto, null);
        //查询活动任务
        List<String> ids = new ArrayList<>();
        ids.add(dto.getActivityId());
        List<JoblistActivityExample> activityChildren = getActivityListByPidList(0, null, ids);
        List<String> childrenIds = activityChildren.stream().map(JoblistActivityExample::getId).collect(Collectors.toList());
        //获取人员实际得分
        //通过活动查询指标结果
        QualityResult qualityResult = new QualityResult();
        qualityResult.setSourceIdList(childrenIds);
        List<QualityResult> results = qualityResultService.queryQualityResultByPage(qualityResult, null);
        // 同父活动的所有指标结果
        //计算总得分
        Result scoreAndStatusresult = getResult(results);
        List<QualityIndexDto> activityIndexList = new ArrayList<>();
        JSONArray activityArray = new JSONArray();
        if (StringUtils.isNotEmpty(qualityIndices)) {
            JSONObject bean = new JSONObject();
            bean.put("title", "活动质量指标");
            for (QualityIndex qualityIndex : qualityIndices) {
                QualityIndexDto index = ObjUtils.copyTo(qualityIndex, QualityIndexDto.class);
                index.setScore(scoreAndStatusresult.scoreResult.get(index.getId()));
                index.setResult(scoreAndStatusresult.statusResult.get(index.getId()));
                index.setProgramName(scoreAndStatusresult.programResult.get(index.getId()));
                //计算实际得分
                JSONArray factScorekResults = scoreAndStatusresult.factScoreResult.get(index.getId());
                computFactScore(dto, factScorekResults, index);
                activityIndexList.add(index);
            }
            bean.put("datas", activityIndexList);
            activityArray.add(bean);
        }
        result.put("activityIndexDatas", activityArray);


        //获取活动相关台账
        StandardJobLibQueryDto queryForm = new StandardJobLibQueryDto();
        queryForm.setBindtype(4);
        queryForm.setPid(dto.getActivityPropertyId());
        List<JoblistPersonBind> joblistPersonBindList = methodService.getJoblistPersonBindList(queryForm);
        if (StringUtils.isEmpty(joblistPersonBindList)) {
            return result;
        }
        //有表单绑定
        List<String> formIds = joblistPersonBindList.stream().map(JoblistPersonBind::getBindid).collect(Collectors.toList());
        //查询关系表
        AccountConfigQueryDto queryAccountConfig = new AccountConfigQueryDto();
        queryAccountConfig.setFormIds(formIds);
        List<DigitalLedger> dataList = accountConfigService.getDataList(queryAccountConfig);
        if (StringUtils.isEmpty(dataList)) {
            return result;
        }
        //关系表中获取台账对象id
        List<String> ledgerIds = dataList.stream().map(item -> item.getFormAlias() + ":" + item.getComponentId()).collect(Collectors.toList());
        //获取台账的指标信息
        indexQueryDto = new JoblistQualityIndexQueryDto();
        indexQueryDto.setIndexName(dto.getIndexName());
        indexQueryDto.setObjectIds(ledgerIds);
        qualityIndices = qualityIndexService.queryJoblistQualityIndexByPage(indexQueryDto, null);
        Map<String, List<QualityIndex>> indexGroup = qualityIndices.stream().collect(Collectors.groupingBy(QualityIndex::getObjectId));
        qualityResult = new QualityResult();
        qualityResult.setSourceIdList(ledgerIds);
        results = qualityResultService.queryQualityResultByPage(qualityResult, null);
        scoreAndStatusresult = getResult(results);

        JSONArray ledgerArray = new JSONArray();
        for (DigitalLedger digitalLedger : dataList) {
            JSONObject bean = new JSONObject();
            bean.put("title", digitalLedger.getLedgerName() + "质量指标");
            List<QualityIndexDto> ledgerIndexList = new ArrayList<>();
            String objectId = digitalLedger.getFormAlias() + ":" + digitalLedger.getComponentId();
            for (QualityIndex qualityIndex : indexGroup.getOrDefault(objectId, new ArrayList<>())) {
                QualityIndexDto index = ObjUtils.copyTo(qualityIndex, QualityIndexDto.class);
                index.setScore(scoreAndStatusresult.scoreResult.get(index.getId()));
                index.setResult(scoreAndStatusresult.statusResult.get(index.getId()));
                ledgerIndexList.add(index);
            }
            bean.put("datas", ledgerIndexList);
            ledgerArray.add(bean);
        }
        result.put("ledgerIndexDatas", ledgerArray);
        return result;
    }

    /**
     * 计算实际得分
     * <AUTHOR>
     * @date 2025/6/9
     * @params
     * @return
     *
    */
    private static void computFactScore(WorkCalendarAcitityDetailDto dto, JSONArray factScorekResults,
                                        QualityIndexDto index) {
        //计算人员的实际得分
        if(factScorekResults!=null){
            Double factScore = 0d;
            for (int i = 0; i < factScorekResults.size(); i++) {
                JSONObject jsonObject = factScorekResults.getJSONObject(i);
                Double personScore = jsonObject.getDouble(dto.getPersonId());
                if(personScore==null){
                    personScore = 0d;
                }
                factScore = factScore+personScore;
            }
            index.setFactScore(factScore);
        }
    }

    private static Result getResult(List<QualityResult> results) {
        //总分
        Map<String, Double> scoreResult = Optional.ofNullable(results).orElse(new ArrayList<>()).stream()
                //按照指标id分组
                .collect(Collectors.groupingBy(QualityResult::getQualityId,
                                // 只统计分
                                Collectors.mapping(item -> {
                                            if (item.getScore() != null) {
                                                return item.getScore();
                                            } else {
                                                return 0d;
                                            }
                                        },
                                        //收集成列表
                                        Collectors.collectingAndThen(Collectors.toList(),
                                                x -> x.stream().reduce(0d, (a, b) -> {
                                                            BigDecimal add = new BigDecimal(a).add(new BigDecimal(b));
                                                            return add.doubleValue();
                                                        }

                                                )
                                        )
                                )
                        )
                );
        //完成情况
        Map<String, Integer> statusResult = Optional.ofNullable(results).orElse(new ArrayList<>()).stream()
                .collect(Collectors.groupingBy(QualityResult::getQualityId,
                                Collectors.mapping(QualityResult::getResult,
                                        Collectors.collectingAndThen(Collectors.toList(), x -> {
                                            boolean b = x.stream().allMatch(item -> item == 1);
                                            return b ? 1 : 0;
                                        })
                                )
                        )
                );
        //分配方案
        Map<String, String> programResult = Optional.ofNullable(results).orElse(new ArrayList<>()).stream()
                .filter(item-> StringUtils.isNotEmpty(item.getProgramName()))
                .collect(Collectors.groupingBy(QualityResult::getQualityId,
                                Collectors.mapping(QualityResult::getProgramName,
                                        Collectors.collectingAndThen(Collectors.toList(), x -> {
                                            x = x.stream().distinct().collect(Collectors.toList());
                                            if(StringUtils.isNotEmpty(x)){
                                                return StringUtils.join(x, ",");
                                            }
                                            return "";
                                        })
                                )
                        )
                );
        //实际得分情况
        Map<String, JSONArray> factScoreResult =
                Optional.ofNullable(results).orElse(new ArrayList<>()).stream()
                        .filter(item-> StringUtils.isNotEmpty(item.getProgramResult()))
                .collect(Collectors.groupingBy(QualityResult::getQualityId,
                                Collectors.mapping(QualityResult::getProgramResult,
                                        Collectors.collectingAndThen(Collectors.toList(), x -> {
                                            JSONArray jsonArray = new JSONArray();
                                            x.stream().forEach(item -> {
                                                JSONObject jsonObject = JSONObject.parseObject(item);
                                                if(jsonObject!=null && jsonObject.size()>0){
                                                    jsonArray.add(jsonObject);
                                                }
                                            });
                                            return jsonArray;
                                        })
                                )
                        )
                );
        Result result = new Result(scoreResult, statusResult, programResult, factScoreResult);
        return result;
    }

    private static class Result {
        public final Map<String, Double> scoreResult;
        public final Map<String, Integer> statusResult;
        public final Map<String, String> programResult;
        public final Map<String, JSONArray> factScoreResult;
        public Result(Map<String, Double> scoreResult, Map<String, Integer> statusResult, Map<String, String> programResult, Map<String, JSONArray> factScoreResult) {
            this.scoreResult = scoreResult;
            this.statusResult = statusResult;
            this.programResult = programResult;
            this.factScoreResult = factScoreResult;
        }
    }
    }
