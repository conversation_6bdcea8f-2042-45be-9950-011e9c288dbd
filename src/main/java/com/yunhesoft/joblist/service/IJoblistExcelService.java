package com.yunhesoft.joblist.service;


import javax.servlet.http.HttpServletResponse;

import org.springframework.web.multipart.MultipartFile;

import com.yunhesoft.joblist.entity.dto.StandardJobLibQueryDto;

/**
 *	作业清单导出、导入Excel服务接口
 * <AUTHOR>
 * @date 2024-06-28
 */
public interface IJoblistExcelService {

	/**
	 *	作业活动导入Excel
	 * @param file
	 * @param queryDto
	 * @return
	 */
	public String importExcelJoblistActivity(MultipartFile file, StandardJobLibQueryDto queryDto);
	
	/**
	 *	作业活动导出Excel模板
	 * @param queryDto
	 * @param response
	 */
	public void exportExcelTemplateJoblistActivity(StandardJobLibQueryDto queryDto, HttpServletResponse response);
	
}
