//package com.yunhesoft.joblist.listener;
//
//import com.alibaba.fastjson.JSONArray;
//import com.alibaba.fastjson.JSONObject;
//import com.yunhesoft.joblist.constant.JoblistConstant;
//import com.yunhesoft.joblist.entity.dto.JoblistInputMappingDto;
//import com.yunhesoft.joblist.service.IJobGeneraterService;
//import com.yunhesoft.joblist.service.IJoblistInputService;
//import com.yunhesoft.tmsf.form.event.FormSaveEvent;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.context.ApplicationListener;
//import org.springframework.stereotype.Component;
//
//import java.util.Arrays;
//
//@Component
//public class SaveFormListener implements ApplicationListener<FormSaveEvent> {
//
//    @Autowired
//    private IJoblistInputService joblistInputService;
//
//    @Autowired
//    private IJobGeneraterService jobGeneraterService;
//
//    /**
//     * 表单保存触发事件
//     */
//    @Override
//    public void onApplicationEvent(FormSaveEvent event) {
//        JSONObject data = (JSONObject) event.getSource();
//        JSONObject formData = data.getJSONObject("formData");
//        String jobPid = formData.getString("jobPid");
//        String jobId = formData.getString("exampleId");
//
//        //更新主活动与外部业务模块主数据关系
//        JoblistInputMappingDto joblistInputMappingDto = new JoblistInputMappingDto();
//        joblistInputMappingDto.setJobType(JoblistConstant.JOB_TYPE_ROUTINE_WORK);
//        joblistInputMappingDto.setMapType(JoblistConstant.MAP_TYPE_CLOUD_FLOW_FORM);
//        joblistInputMappingDto.setMasterId(jobPid);
//        joblistInputMappingDto.setSlaveId(data.getString("dataId"));
//        joblistInputService.updateInputMappingData(Arrays.asList(joblistInputMappingDto), false);
//
//        //更新子活动反馈、确认状态
//        JSONArray activityArray = new JSONArray();
//        JSONObject activityObj = new JSONObject();
//        activityObj.put("taskId", jobId);
//        activityArray.add(activityObj);
//        jobGeneraterService.saveActivityStatusHandler(activityArray);
//    }
//}
//
