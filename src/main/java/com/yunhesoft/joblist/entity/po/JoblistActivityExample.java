package com.yunhesoft.joblist.entity.po;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Index;
import javax.persistence.Table;
import javax.persistence.Transient;

import com.yunhesoft.core.common.entity.BaseEntity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel(value = "工作清单实例表")
@Data
@Entity
@Table(name = "JOBLIST_ACTIVITYEXAMPLE",indexes = {
        @Index(name = "IDX_JOBLIST_ACTEXAMPLE_ACTID", columnList = "ACTIVITY_ID"),
        @Index(name = "IDX_JOBLIST_ACTEXAMPLE_PID", columnList = "PID"),
        @Index(name = "IDX_JOBLIST_ACTEXAMPLE_QUERY", columnList = "ORG_CODE,SHIFT_CLASS_CODE,TBRQ"),
        @Index(name = "IDX_JOBLIST_ACTEXAMPLE_QUERY_SJ", columnList = "SBSJ,SHIFT_CLASS_CODE,TMUSED")
})
public class JoblistActivityExample extends BaseEntity {

    @ApiModelProperty(value = "父记录id")
    @Column(name = "PID", length = 100)
    private String pid;

    @ApiModelProperty(value = "是否是父记录")
    @Column(name = "IS_PARENT", length = 100)
    private Integer isParent;

    @ApiModelProperty(value = "活动id")
    @Column(name = "ACTIVITY_ID", length = 100)
    private String activityId;

    @ApiModelProperty(value = "活动时间")
    @Column(name = "ACTIVITY_DATE", length = 100)
    private String activityDate;

    @ApiModelProperty(value = "活动实例开始时间")
    @Column(name = "BEGIN_DATE", length = 100)
    private String beginDate;

    @ApiModelProperty(value = "活动实例结束时间")
    @Column(name = "END_DATE", length = 100)
    private String endDate;

    @ApiModelProperty(value = "标准时长")
    @Column(name = "STANDARD_DURATION", length = 100)
    private Double standardDuration;

    @ApiModelProperty(value = "活动实例状态   0：未开始1 进行中 2已录入 ")
    @Column(name = "ACTIVITY_STATUS", length = 100)
    private Integer activityStatus;

    @ApiModelProperty(value = "负责人")
    @Column(name = "RESPONSIBLE_PERSON_NAME", length = 100)
    private String responsiblePersonName;

    @ApiModelProperty(value = "负责人id")
    @Column(name = "RESPONSIBLE_PERSON_ID", length = 100)
    private String responsiblePersonId;
    ;

    @ApiModelProperty(value = "责任对象类型 ，标志班组任务还是例行任务")
    @Column(name = "RESPONSIBLE_TYPE", length = 100)
    private Integer responsibilityType;
    ;

    @ApiModelProperty(value = "分配时间")
    @Column(name = "DELIVER_DATE", length = 100)
    private String deliverDate;

    @ApiModelProperty(value = "完成时间")
    @Column(name = "FINISH_DATE", length = 100)
    private String finishDate;

    @ApiModelProperty(value = "任务备注")
    @Column(name = "MEMO", length = 100)
    private String memo;

    @ApiModelProperty(value = "排序")
    @Column(name = "TMSORT", length = 100)
    private Integer tmSort;

    @ApiModelProperty(value = "是否启用")
    @Column(name = "TMUSED", length = 100)
    private Integer tmUsed;


    @ApiModelProperty(value = "班次代码")
    @Column(name = "SHIFT_CLASS_CODE", length = 100)
    private String shiftClassCode;

    @ApiModelProperty(value = "父机机构代码")
    @Column(name = "P_ORG_CODE", length = 100)
    private String pOrgCode;

    @ApiModelProperty(value = "班组机构代码")
    @Column(name = "ORG_CODE", length = 100)
    private String orgCode;


    @ApiModelProperty(value = "班次上班时间")
    @Column(name = "SBSJ", length = 100)
    private String sbsj;

    @ApiModelProperty(value = "班次下班时间")
    @Column(name = "XBSJ", length = 100)
    private String xbsj;

    @ApiModelProperty(value = "记录来源")
    @Column(name = "RECORD_ID", length = 100)
    private String recordid;
    //从数据字典读取来源
    @Column(name="RECORDTYPE", length=100)
    private String recordType;
    //技术要求
    @Column(name="JOBREQUIREMENT", length=4000)
    private String jobRequirement;
    //活动编号
    @Column(name="JOB_NO")
    private Integer jobNo;
    //生成频次类型
    @Column(name="FREQUENCY_TYPE")
    private Integer frequencyType;

    @Column(name="DATA_ORIGIN")
    private String dataOrigin;

    //0、自动确认；1、手工确认；默认是自动确认
    @Column(name="CONFIRMTYPE")
    private Integer confirmType;

    @Column(name="JOB_NAME")
    private String jobName;

    @Column(name="PRIORITYID", length=100)
    private String priorityid;
    @Column(name="TBRQ", length=100)
    private String tbrq;


    @Transient
    private Boolean skipInsert;
    @Transient
    private String tempDate;
    @Transient
    private Integer priority;
}
