package com.yunhesoft.joblist.entity.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("人员活动数量统计对象")
public class JoblistPersonTaskNumVo {
    @ApiModelProperty("用户ID")
    private String userId;
    @ApiModelProperty("用户姓名")
    private String userName;

    @ApiModelProperty("班组工作已完成数量")
    private Integer groupWorkDoneNum;
    @ApiModelProperty("班组工作总数量")
    private Integer groupWorkAllNum;

    @ApiModelProperty("例行工作已完成数量")
    private Integer routineWorkDoneNum;
    @ApiModelProperty("例行工作总数量")
    private Integer routineWorkAllNum;

    @ApiModelProperty("例外工作已完成数量")
    private Integer exceptionalWorkDoneNum;
    @ApiModelProperty("例外工作总数量")
    private Integer exceptionalWorkAllNum;
}
