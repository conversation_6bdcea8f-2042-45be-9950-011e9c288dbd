package com.yunhesoft.joblist.entity.dto;

import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import lombok.Data;

@ApiOperation("获取活动录入情况数据-参数")
@Data
public class JoblistSummaryDto {
    @ApiModelProperty(value = "开始日期", example = "2025-07-01")
    private String startDate;
    @ApiModelProperty(value = "结束日期", example = "2025-07-31")
    private String endDate;
    //@ApiModelProperty(value = "班次编码",example = "TMUID")
    //private String shiftCode;
    @ApiModelProperty(value = "班次名称", example = "夜班/白班/中班")
    private String shiftName;
    @ApiModelProperty(value = "班组编码", example = "TMUID")
    private String orgCode;
    @ApiModelProperty(value = "班组名称", example = "一班/二班/三班")
    private String orgName;
    @ApiModelProperty(value = "岗位ID（多选）", example = "xxx,xxx,xxx")
    private String postIds;
    @ApiModelProperty(value = "岗位名称（多选）", example = "班长,内操,外操")
    private String postNames;
    @ApiModelProperty(value = "页码", example = "1")
    private Integer page;
    @ApiModelProperty(value = "每页数量", example = "100")
    private Integer size;
    @ApiModelProperty(value = "活动状态", example = "0=未开始、1=进行中、2=已完成")
    private String activityStatus;
}
