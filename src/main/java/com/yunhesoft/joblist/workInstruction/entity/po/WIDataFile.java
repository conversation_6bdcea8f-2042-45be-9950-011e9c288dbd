package com.yunhesoft.joblist.workInstruction.entity.po;

import com.yunhesoft.core.common.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;

@ApiModel(value = "工作指令文件")
@Getter
@Setter
@Entity
@Table(name = "WI_DATA_FILE")
public class WIDataFile extends BaseEntity {

	@ApiModelProperty(value = "主数据id")
	@Column(name = "DATA_ID", length = 50)
	private String dataId;

	@ApiModelProperty(value = "文件id")
	@Column(name = "FILE_ID", length = 50)
	private String fileId;

	@ApiModelProperty(value = "文件名")
	@Column(name = "FILE_NAME", length = 200)
	private String fileName;

	@ApiModelProperty(value = "文件路径")
	@Column(name = "FILE_URL", length = 200)
	private String fileUrl;

	@ApiModelProperty(value = "文件类型   0下达接收时的附件  1填报反馈时的附件 2机构向上反馈时的附件")
	@Column(name = "FILE_TYPE")
	private int fileType;

	@ApiModelProperty(value = "1填报反馈时的附件此字段为WI_DATA_ORG_FEEDBACK_INFO反馈信息表id  2机构向上反馈时此字段为WI_DATA_ORG_INFO表的ID  ")
	@Column(name = "INFO_ID", length = 50)
	private String infoId;

	@ApiModelProperty(value = "机构代码 机构反馈时机构")
	@Column(name = "ORG_CODE", length = 50)
	private String orgCode;

	@Transient
	@ApiModelProperty(value = "可以删除")
	private boolean canDelete = false;
}
