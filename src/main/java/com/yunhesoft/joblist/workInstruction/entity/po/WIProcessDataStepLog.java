package com.yunhesoft.joblist.workInstruction.entity.po;

import com.yunhesoft.core.common.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

@ApiModel(value = "流程步骤操作日志")
@Getter
@Setter
@Entity
@Table(name = "WI_PROCESS_DATA_STEP_LOG")
public class WIProcessDataStepLog extends BaseEntity {

	@ApiModelProperty(value = "业务数据id")
	@Column(name = "DATA_ID", length = 50)
	private String dataId;

	@ApiModelProperty(value = "流程实例id")
	@Column(name = "PROCESS_DATA_ID", length = 50)
	private String processDataId;

	@ApiModelProperty(value = "流程步骤id")
	@Column(name = "PROCESS_DATA_STEP_ID", length = 50)
	private String processDataStepId;

	@ApiModelProperty(value = "接收机构(车间)")
	@Column(name = "ACC_ORG_CODE", length = 50)
	private String accOrgCode;

	@ApiModelProperty(value = "接收机构(车间)")
	@Column(name = "ACC_ORG_NAME", length = 50)
	private String accOrgName;

	@ApiModelProperty(value = "处理结果 0未处理 1通过 -1否决")
	@Column(name = "HANDLE_RESULT")
	private int handleResult;

	@ApiModelProperty(value = "结转")
	@Column(name = "HANDLE_TRANSFER")
	private int handleTransfer;

	@ApiModelProperty(value = "处理人所在机构")
	@Column(name = "HANDLE_USER_ORG_CODE", length = 50)
	private String handleUserOrgCode;

	@ApiModelProperty(value = "处理人")
	@Column(name = "HANDLE_USER_ID", length = 50)
	private String handleUserId;

	@ApiModelProperty(value = "处理人")
	@Column(name = "HANDLE_USER_NAME", length = 100)
	private String handleUserName;

	@ApiModelProperty(value = "处理时间")
	@Column(name = "HANDLE_TIME", length = 20)
	private String handleTime;


}
