package com.yunhesoft.joblist.operCard.entity.po;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Index;
import javax.persistence.Table;

import com.yunhesoft.core.common.entity.BaseEntity;

import lombok.Getter;
import lombok.Setter;

/**
 * 操作卡执行_主表
 * 
 */
@Entity
@Setter
@Getter
@Table(name = "OPERCARD_EXEC", indexes = {@Index(name = "opercard_exec_i", columnList = "CARDID,CATALOGALIAS,ORGCODE")})
public class OpercardExec extends BaseEntity {
	
    private static final long serialVersionUID = 1L;

    
    /** 操作卡id */
    @Column(name="CARDID", length=50)
    private String cardId;
    
    /** 操作卡名称 */
    @Column(name="CARDNAME", length=255)
    private String cardName;
    
    /** 操作卡目录别名 */
    @Column(name="CATALOGALIAS", length=50)
    private String catalogAlias;
    
    /** 目录名称 */
    @Column(name="CATALOGNAME", length=500)
    private String catalogName;
    
    /** 操作卡版本ID */
    @Column(name="CARDVERID", length=50)
    private String cardVerId;
    
    /** 机构代码 */
    @Column(name="ORGCODE", length=50)
    private String orgCode;
    
    /** 开始时间 */
    @Column(name="STARTDT")
    private Date startDt;
    
    /** 结束时间 */
    @Column(name="ENDDT")
    private Date endDt;
    
    /** 执行持续时间(分钟) */
    @Column(name="EXECUTIVETIME")
    private Double executiveTime;
    
    /** 指令/活动id */
    @Column(name="WORKID", length=50)
    private String workId;
    
    /** 指令/活动id */
    @Column(name="WORKCONTENT", length=4000)
    private String workContent;
    
    /** 工作类型 */
    @Column(name="WORKTYPE")//1=指令，2=活动
    private Integer workType;
    
    /** 执行方式 */
    @Column(name="EXECTYPE")//1顺序执行 2同步执行
    private Integer execType;
    
    /** 录入方式 */
    @Column(name="INPUTTYPE")//1逐步确认 2关键点确认
    private Integer inputType;
    
    /** 执行状态 */
    @Column(name="EXECSTATUS")//-1废止 0未开始 1执行中 2已完成
    private Integer execStatus;

    /** 固化状态 */
    @Column(name="FIXEDSTATUS")//0未固化 1已固化
    private Integer fixedStatus;
    
    /** 外操执行次数 */
    @Column(name="PCOUNT")
    private Integer pCount;
    
    /** 内操执行次数 */
    @Column(name="ICOUNT")
    private Integer iCount;
    
    /** 班长执行次数 */
    @Column(name="MCOUNT")
    private Integer mCount;
    
    /** 操作卡录入人员 */
    @Column(name="INPUTUSERNAME", length=4000)
    private String inputUserName;
    
    /** 操作卡录入人员 */
    @Column(name="INPUTUSERID", length=4000)
    private String inputUserId;
}
