package com.yunhesoft.joblist.operCard.entity.vo;

import lombok.Data;


/**
 * 操作卡操作步骤仪表信息
 * 
 */
@Data
public class OpercardInstrumentVo {
	
    private static final long serialVersionUID = 1L;

    /** 操作卡id */
    private String cardId;
    
    /** 操作卡版本ID */
    private String cardVerId;
    
    /** 操作卡目录别名 */
    private String catalogAlias;
    
    /** 步骤ID */
    private String stepId;
    
    /** 注释 */
    private String memo;
    
    /** 仪表名称 */
    private String tagName;
    
    /** 仪表位号 */
    private String tagCode;
    
    /** 上限值 */
    private Double upLimit;
    
    /** 下限值 */
    private Double lowLimit;
    
    /** 控件类型 *///raw：实时仪表；number：数字框；combo:下拉框；img:图片；tip：风险提示  textfield：文本框
    private String comType;
    
    /** 是否使用 */
    private Integer tmUsed;
    
    /** 排序 */
    private Integer tmSort;
    
    /** 扩展内容 */
    private String propertyCode;

    private String type;

    private String id;

}
