package com.yunhesoft.joblist.operCard.entity.po;


import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

import com.yunhesoft.core.common.entity.BaseEntity;

import lombok.Getter;
import lombok.Setter;


/**
 * 操作卡执行_操作卡固化表
 * 
 */
@Entity
@Setter
@Getter
@Table(name = "OPERCARD_EXEC_FIXED")
public class OpercardExecFixed extends BaseEntity {
	
    private static final long serialVersionUID = 1L;


    /** 操作卡id */
    @Column(name="CARDID", length=50)
    private String cardId;
    
    /** 操作卡目录别名 */
    @Column(name="CATALOGALIAS", length=50)
    private String catalogAlias;
    
    /** 操作卡版本ID */
    @Column(name="CARDVERID", length=50)
    private String cardVerId;
    
    /** 执行次数 */
    @Column(name="EXECCOUNT")
    private Integer execCount;
    

}