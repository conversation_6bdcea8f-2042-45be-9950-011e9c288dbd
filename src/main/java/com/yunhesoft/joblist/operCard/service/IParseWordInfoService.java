package com.yunhesoft.joblist.operCard.service;

import java.util.List;

import org.springframework.web.multipart.MultipartFile;

import com.yunhesoft.joblist.operCard.entity.dto.OpercardInfoDto;
import com.yunhesoft.joblist.operCard.entity.po.OpercardInfo;
import com.yunhesoft.system.kernel.service.model.Pagination;

public interface IParseWordInfoService {

    //查看操作卡名称
    List<OpercardInfo> queryParseWord(Pagination<?> page, OpercardInfoDto OpercardInfoDto);

    //添加操作卡
    int insertOperCard (List<OpercardInfo> param);

    //删除操作卡
    int deleteOperCard (List<OpercardInfo> ids);

    //修改操作卡
    int updateOperCard (OpercardInfo opercardInfo);

    //导入word并解析word内容
    String parseWord(String cardId, MultipartFile file) throws Exception;
    //查询所有操作卡
    List<OpercardInfo> queryAllParseWord(String orgCode);
    /**
     * 更新操作卡为未发布状态
     * @category 
     * <AUTHOR> 
     * @param cardId
     */
    void updateCardStatus(String cardId);
    /**
     * 根ID查询操作卡
     * @category 根ID查询操作卡
     * <AUTHOR> 
     * @param cardId 
     * @return OpercardInfo
     */
    OpercardInfo getOpercardInfo(String cardId);
}
