package com.yunhesoft.joblist.operCard.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.yunhesoft.core.common.model.Res;
import com.yunhesoft.core.common.model.SysUser;
import com.yunhesoft.joblist.operCard.entity.dto.OperCardExecDto;
import com.yunhesoft.joblist.operCard.entity.dto.OperCardExecInitDto;
import com.yunhesoft.joblist.operCard.entity.dto.OperCardExecQueryDto;
import com.yunhesoft.joblist.operCard.entity.vo.OperCardExecDataVo;
import com.yunhesoft.joblist.operCard.entity.vo.OperCardExecVo;
import com.yunhesoft.joblist.operCard.entity.vo.OperCardInitResultVo;
import com.yunhesoft.joblist.operCard.service.IOpercardExecService;
import com.yunhesoft.system.kernel.config.SysUserHolder;
import com.yunhesoft.system.kernel.controller.BaseRestController;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

//@CrossOrigin(origins = "*", maxAge = 3600, methods = { RequestMethod.GET, RequestMethod.POST }) // 支持跨域
@RestController
@RequestMapping("/operCard/operCardExec")
@Api(tags = "操作卡执行")
public class OperCardExecController extends BaseRestController {

    @Autowired
    private IOpercardExecService serv;
    
	@ApiOperation(value = "生成操作卡实例", notes = "生成操作卡实例")
	@RequestMapping(value = "/initOpercard", method = {RequestMethod.POST})
	public Res<?> initOpercard(@RequestBody OperCardExecInitDto parm) {		
		OperCardInitResultVo result = serv.initOpercard(parm);
		if (result==null) {
			return Res.OK(result).setSuccess(false);// 没查到
		} else {
	        return Res.OK(result);
	    }
	}
    
	@ApiOperation(value = "获取操作卡实例", notes = "获取操作卡实例")
	@RequestMapping(value = "/getExecOpercard", method = {RequestMethod.POST})
	public Res<?> getExecOpercard(@RequestBody OperCardExecQueryDto parm) {	
		OperCardExecVo result = serv.getExecOpercard(parm);
		if (result==null) {
			return Res.OK(result).setSuccess(false);// 没查到
		} else {
	        return Res.OK(result);
	    }
	}
	
	@ApiOperation(value = "验证身份证", notes = "验证身份证")
	@RequestMapping(value = "/checkIdentityCard", method = {RequestMethod.POST})
	public Res<?> checkIdentityCard(@RequestParam("cardId") String cardId) {
		  return Res.OK(serv.checkIdentityCard(cardId));
	}
	@ApiOperation(value = "人脸识别验证", notes = "人脸识别验证")
	@RequestMapping(value = "/checkFace", method = {RequestMethod.POST})
	public Res<?> checkFace() {
		  return Res.OK(serv.checkFace());
	}
	@ApiOperation(value = "执行操作卡实例", notes = "执行操作卡实例")
	@RequestMapping(value = "/execOpercard", method = {RequestMethod.POST})
	public Res<?> execOpercard(@RequestBody OperCardExecDto parm) {	
		OperCardExecVo result = serv.execOpercard(parm);
		if (result==null) {
			return Res.OK(result).setSuccess(false);// 没查到
		} else {
	        return Res.OK(result);
	    }
	}
	
	@ApiOperation(value = "获取操作卡人员待办信息", notes = "获取操作卡人员待办信息")
	@RequestMapping(value = "/getExecOpercardToDoList", method = {RequestMethod.POST})
	public Res<?> getExecOpercardToDoList() {	
		String userId=null;
		SysUser user = SysUserHolder.getCurrentUser();
		if(user!=null) {
			userId=user.getId();
		}
		return Res.OK(serv.getExecOpercardToDoList(userId));
	}
	/**
	 * 校验人员是否可以操作某张操作卡
	 * @category 校验人员是否可以操作某张操作卡
	 * <AUTHOR> 
	 * @param parm OperCardExecInitDto
	 * @return List<OperCardUserVo>  OperCardUserVo.canEdit=1可操作 perCardUserVo.errorinfo 不可操作的原因
	 */
	@ApiOperation(value = "校验人员是否可以操作某张操作卡", notes = "校验人员是否可以操作某张操作卡")
	@RequestMapping(value = "/checkOperateUser", method = {RequestMethod.POST})
	public Res<?> checkOperateUser(@RequestBody OperCardExecInitDto parm) {	
//		return Res.OK(serv.checkOperateUser(parm));
		return Res.OK(null);
	}
	
	/**
	 * 查询操作卡执行记录
	 * @category 查询操作卡执行记录
	 * <AUTHOR> 
	 * @param parm
	 * @return List<OperCardExecDataVo>
	 */
	@ApiOperation(value = "查询操作卡执行记录（数据统计）", notes = "查询操作卡执行记录（数据统计）")
	@RequestMapping(value = "/queryOperCardExecData", method = {RequestMethod.POST})
	public Res<?> queryOperCardExecData(@RequestBody OperCardExecQueryDto parm) {	
		List<OperCardExecDataVo> result = serv.queryOperCardExecData(parm);
		int total = 0;
		if(parm!=null && parm.getTotal()!=null) {
			total=parm.getTotal().intValue();
		} 
		return Res.OK(result).setTotal(total);
	}
	/**
	 * 操作卡执行记录导出Excel
	 * @category 操作卡执行记录导出Excel
	 * <AUTHOR> @param param
	 * @return
	 */
	@ApiOperation(value = "操作卡执行记录导出Excel", notes = "操作卡执行记录导出Excel")
	@RequestMapping(value = "/exportOperCardExecData", method = { RequestMethod.POST })
	public void exportOperCardExecData(@RequestBody OperCardExecQueryDto parm) {
		serv.exportOperCardExecData(parm, response);
	}
	/**
	 * 操作卡导出PDF
	 * @category 操作卡导出PDF
	 * <AUTHOR> @param param
	 * @return
	 */
	@ApiOperation(value = "操作卡导出PDF", notes = "操作卡导出PDF")
	@RequestMapping(value = "/exportOperCardExecPDF", method = { RequestMethod.POST })
	public void exportOperCardExecPDF(@RequestBody OperCardExecQueryDto parm) {
		serv.exportOperCardExecPDF(parm, response);
	}
}
