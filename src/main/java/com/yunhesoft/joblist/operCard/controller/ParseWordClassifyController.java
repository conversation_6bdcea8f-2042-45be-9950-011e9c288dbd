package com.yunhesoft.joblist.operCard.controller;

import com.yunhesoft.bzjs.entity.dto.BzjsConfDto;
import com.yunhesoft.core.common.model.Res;
import com.yunhesoft.joblist.operCard.entity.po.OpercardClassify;
import com.yunhesoft.joblist.operCard.entity.vo.OpercardClassifyVo;
import com.yunhesoft.joblist.operCard.service.IParseWordClassifyService;
import com.yunhesoft.system.kernel.controller.BaseRestController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/operCard/operCardClassify")
@Api(tags = "操作卡分类接口")
public class ParseWordClassifyController extends BaseRestController {
    @Autowired
    private IParseWordClassifyService parseWordClassifyService;

    @ApiOperation("操作卡分类 - 分类懒加载树")
    @RequestMapping(value = "/queryTreeClassify", method = {RequestMethod.POST})
    public Res queryTreeClassify(@RequestBody OpercardClassifyVo opercardClassifyVo) {
        return Res.OK(parseWordClassifyService.queryTreeCata(opercardClassifyVo));
    }

    @ApiOperation("操作卡分类 - 删除操作卡分类")
    @RequestMapping(value = "/deleteCardClassify", method = {RequestMethod.POST})
    public Res deleteTreeClassify(@RequestParam String id) {
        return Res.OK(parseWordClassifyService.deleteCardClassify(id));
    }

    @ApiOperation("操作卡分类 - 修改操作卡分类")
    @RequestMapping(value = "/updateCardClassify", method = {RequestMethod.POST})
    public Res updateCardClassify(@RequestBody OpercardClassify opercardClassify) {
        return Res.OK(parseWordClassifyService.updateCardClassify(opercardClassify));
    }


    @ApiOperation("操作卡分类 - 新增操作卡分类")
    @RequestMapping(value = "/insertCardClassify", method = {RequestMethod.POST})
    public Res insertCardClassify(@RequestBody OpercardClassify opercardClassify) {
        return Res.OK(parseWordClassifyService.insertCardClassify(opercardClassify));
    }


    @ApiOperation("操作卡分类 - 获取页面登录用户信息")
    @RequestMapping(value = "/querysysUser", method = {RequestMethod.POST})
    public Res querysysUser() {
        return Res.OK(parseWordClassifyService.querysysUser());
    }



}
