package com.yunhesoft.joblist.constant;

// 常量类
public class JoblistConstant {
    //模块编码
    public final static String MODULE_CODE = "joblist";
    //模块名称
    public final static String MODULE_NAME = "岗位工作清单";

    //例行工作
    public static final int JOB_TYPE_ROUTINE_WORK = 1;
    //例外工作
    public static final int JOB_TYPE_EXCEPTIONAL_WORK = 2;

    //采集点录入
    public static final String MAP_TYPE_COLLECTION_POINT = "collection_point";
    //常规活动录入
    public static final String MAP_TYPE_GENERAL_ACTIVITY = "general_activity";
    //行云流表单录入
    public static final String MAP_TYPE_CLOUD_FLOW_FORM = "cloud_flow_form";
    //电子台账录入
    public static final String MAP_TYPE_ELECTRONIC_LEDGER = "electronic_ledger";

    // 班长操作权限
    public static final String OPS_CLASS_MONITOR = "class_monitor";
    // 高级管理权限
    public static final String OPS_CLASS_MANAGE = "class_manage";
    //添加工作权限
    public static final String ADD_JOB_PERMISSION = "add_job";

    //岗位录入情况统计：高级管理权限（可查看所有机构的数据，无权限者默认可查管辖机构的数据）
    public static final String SUMMARY_MONITOR_PERMISSION = "summary_monitor_permission";
}
