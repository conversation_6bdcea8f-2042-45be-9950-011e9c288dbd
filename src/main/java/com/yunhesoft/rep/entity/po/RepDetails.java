package com.yunhesoft.rep.entity.po;


import com.yunhesoft.core.common.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

@ApiModel(value = "报表详细信息")
@Getter
@Setter
@Entity
@Table(name = "REP_DETAILS")
public class RepDetails extends BaseEntity {
    /**
     * 父编号 rep_info.id
     */
    @ApiModelProperty(value = "报表id")
    @Column(name = "REPID", length = 50)
    private String repId;
    /**
     * 分类名称1
     */
    @ApiModelProperty(value = "分类名称1")
    @Column(name = "FL1", length = 50)
    private String fl1;
    /**
     * 分类名称2
     */
    @ApiModelProperty(value = "分类名称2")
    @Column(name = "FL2", length = 50)
    private String fl2;
    /**
     * 分类名称3
     */
    @ApiModelProperty(value = "分类名称3")
    @Column(name = "FL3", length = 50)
    private String fl3;
    /**
     * 指标名称
     */
    @ApiModelProperty(value = "指标名称")
    @Column(name = "ZBMC", length = 255)
    private String zbmc;
    /**
     * 计量单位
     */
    @ApiModelProperty(value = "计量单位")
    @Column(name = "JLDW", length = 50)
    private String jldw;
    /**
     * 数据来源 0:人工录入；1：数据源；2：实时数据
     */
    @ApiModelProperty(value = "数据来源")
    @Column(name = "SJLY", length = 20)
    private int sjly;
    /**
     * 仪表位号/数据源实时
     */
    @ApiModelProperty(value = "仪表位号/数据源实时")
    @Column(name = "SCRIPT", length = 50)
    private String script;
    /**
     * 获取班次
     */
    @ApiModelProperty(value = "获取班次")
    @Column(name = "HQBC", length = 255)
    private String hqbc;

    @ApiModelProperty(value = "展示形式")
    @Column(name = "SHOWTYPE", length = 255)
    private String showType;
    /**
     * 是否获取上次之 1：是；0：否
     */
    @ApiModelProperty(value = "是否获取上次之 1：是；0：否")
    @Column(name = "ISGETLAST", length = 20)
    private int isGetLast;
    /**
     * 是否允许修改 1：是；0：否
     */
    @ApiModelProperty(value = "是否允许修改 1：是；0：否")
    @Column(name = "CANEDIT", length = 20)
    private int canEdit;
    /**
     * 是否使用 1：是；0：否
     */
    @ApiModelProperty(value = "是否使用 1：是；0：否")
    @Column(name = "TMUSED", length = 20)
    private int tmused;
    /**
     * 排序
     */
    @ApiModelProperty(value = "排序")
    @Column(name = "TMSORT", length = 20)
    private int tmsort;

}
