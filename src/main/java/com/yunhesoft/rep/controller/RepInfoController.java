package com.yunhesoft.rep.controller;

import com.alibaba.fastjson.JSONObject;
import com.yunhesoft.accountTools.entity.dto.AbnormalParam;
import com.yunhesoft.accountTools.entity.vo.AbnormalObj;
import com.yunhesoft.core.common.model.Res;
import com.yunhesoft.joblist.workInstruction.service.IWIDataService;
import com.yunhesoft.rep.entity.dto.RepInfoDto;
import com.yunhesoft.rep.entity.po.RepInfo;
import com.yunhesoft.rep.service.IRepInfoService;
import com.yunhesoft.system.kernel.controller.BaseRestController;
import com.yunhesoft.system.kernel.service.model.Pagination;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Api(tags = "报表模型相关函数")
@RestController
@RequestMapping("/rep/repInfo")
public class RepInfoController extends BaseRestController {

    @Autowired
    private IRepInfoService srv;


    @ApiOperation("查询报表模型")
    @RequestMapping(value = "/queryList", method = {RequestMethod.POST})
    public Res<?> queryList(@RequestBody RepInfoDto param) {
        Pagination<?> page = null;
        if (param.getPageSize() > 0) {
            page = Pagination.create(param.getPageNum(), param.getPageSize());
        }
        Res res = Res.OK(srv.queryList(param));
        if (page != null) {
            res.setTotal(page.getTotal());
        }
        return res;
    }
    @ApiOperation("保存报表模型")
    @RequestMapping(value = "/batchSave", method = {RequestMethod.POST})
    public Res<?> batchSave(@RequestBody List<RepInfo> param) {
        Res res = Res.OK(srv.saveData(param));
        return res;
    }

    @ApiOperation("删除报表模型")
    @RequestMapping(value = "/batchDelete", method = {RequestMethod.POST})
    public Res<?> batchDelete(@RequestBody List<RepInfo> param) {
        Res res = Res.OK(srv.deleteData(param));
        return res;
    }
}
