package com.yunhesoft.mobile.entity.dto;

import com.yunhesoft.mobile.entity.vo.AcctobjInputVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;

@ApiModel("精益核算请求参数")
@Data
public class MobLeanCostingDto {
    @ApiModelProperty("查询日期")
    private String searchDate;
    @ApiModelProperty("录入时间（日期时间）")
    private String inputDateTime;
    @ApiModelProperty("班次编码")
    private String bcdm;
    @ApiModelProperty("核算对象ID")
    private String acctobjId;
    @ApiModelProperty("核算对象分类ID（业务活动id）")
    private String flid;
    @ApiModelProperty("录入主数据，其内部包含子数据")
    private AcctobjInputVo inputData;
    @ApiModelProperty("业务活动ID")
    private String baId;
    @ApiModelProperty("是否确认完成此活动")
    private Boolean ifConfirm;
    @ApiModelProperty("上班时间")
    private String sbsj;
    @ApiModelProperty("下班时间")
    private String xbsj;

}
