package com.yunhesoft.system.auth.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.yunhesoft.core.common.model.Res;
import com.yunhesoft.system.auth.entity.po.SysSkin;
import com.yunhesoft.system.auth.service.ISystemSkinService;
import com.yunhesoft.system.kernel.controller.BaseRestController;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

@RestController
@RequestMapping("/auth-systemskin")
@Api(tags = "获取河北或中小企业的皮肤名称")
public class SystemSkinController extends BaseRestController {

	@Autowired
	private ISystemSkinService skinServ;

	@ApiOperation("获取皮肤名称")
	@RequestMapping(value = "/getSysSkin", method = RequestMethod.POST)
	public Res<?> getPlanFeedDataListGroupMxJsonStr() {
		Res<List<SysSkin>> res = new Res<List<SysSkin>>();
		List<SysSkin> list = skinServ.getSysSkin();
		res.setResult(list);
		return res;
	}
}