package com.yunhesoft.system.tds.entity.vo;

import lombok.Data;

import java.util.List;

/**
 * 数据源表格表头显示信息
 *
 * <AUTHOR>
 *
 */
@Data
public class TdsTableColumn {

	private String alias;// 别名
	private String header;// 标题
	private String dataType;// 数据类型
	private boolean hidden;// 是否隐藏
	private String rendererFun;// 渲染函数
	private String isKey;// 是否为关键列
	private String isSumCol;// 是否合计
	private String isSpanCol;// 是否合并
	private Integer spanType;// 合并方式（0：相同合并；2：按照列分组合并；1：按照脚本合并）
	private String spanScript;// 合并脚
	private String comType;// 组件类型
	private String lx;
	private String reportFormula;
	private String isGroupCol;
	private String defaultKeyScript;
	private String defaultValueScript;
	private boolean overtip;// 超出后是否自动折行，悬浮提示
	private String fixed;// 是否锁定列
	private String minwidth;// 最小宽度
	private String width;// 宽度
	private Long maxlength;// 限制录入字符数
	private Boolean autoSwapRow;
	private String align;// 对齐方式
	private boolean required;// 是否为必填项
	private boolean showPlaceholder;// 是否显示站位提示及字符限制
	private String comParams;// 组件属性
	private Integer dataStatusFlag;// 0或null:不是数据状态 列；1：数据状态列
	private String tips; // 提示信息
	private Integer isExportRender;// 导出excel 是否解析渲染函数
	private Integer isShowAsNum;// 显示时是否按照数值显示（去掉无用的小数，0 不显示）
	// 添加数据时的默认值，数据源修改功能使用
	private String insertDefaultValue;

	// 是否禁用，true为禁用，false为可用
	private Boolean disabled;
	private String displayMode;// 多选框显示模式
	private String copyAddDefaultMode;// 复制新增


	/** 用于合并表头功能 */
	private List<TdsTableColumn> children = null;

}
