package com.yunhesoft.system.tds.entity.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
/**
 * 数据源查询条件
 * 
 * <AUTHOR>
 *
 */
@Getter
@Setter
@ApiModel(description = "数据源类别查询条件")
public class TdsDataQueryDto {
	
	@ApiModelProperty(value = "检索条件模块或分类")
	private String module;//moduleCode 模块，categoryId 分类
	
	@ApiModelProperty(value = "数据源名称或别名")
	private String tdsAlias; // tdsname 名称 ，tdsalias 别名

	/** 第几页 */
	@ApiModelProperty(value = "第几页 ")
	private Integer page;

	/** 每页数量 */
	@ApiModelProperty(value = "每页数量 ")
	private Integer pageSize;
}
