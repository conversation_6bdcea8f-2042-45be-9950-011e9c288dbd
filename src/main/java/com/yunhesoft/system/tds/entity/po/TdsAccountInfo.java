package com.yunhesoft.system.tds.entity.po;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

import com.yunhesoft.core.common.entity.BaseEntity;

import lombok.Getter;
import lombok.Setter;


/**
 * 台账数据源保存数据主表
 * 
 */
@Entity
@Setter
@Getter
@Table(name = "TDS_ACCOUNT_INFO")
public class TdsAccountInfo extends BaseEntity {
	
    private static final long serialVersionUID = 1L;

    /** 数据源别名 */
    @Column(name="TDSALIAS", length=50)
    private String tdsalias;
    
    /** 日期 */
    @Column(name="RQ", length=50)
    private String rq;
    
    /** 机构 */
    @Column(name="ORGCODE", length=50)
    private String orgcode;
    
    /** 机构名称 */
    @Column(name="ORGNAME", length=100)
    private String orgname;
    
    /** 班次 */
    @Column(name="CLSNO", length=100)
    private String clsno;
    
    /** 班次名称 */
    @Column(name="CLSNAME", length=100)
    private String clsname;
    
    /** 核算对象代码 */
    @Column(name="UNITCODE", length=100)
    private String unitcode;
    
    /** 核算对象名称 */
    @Column(name="UNITNAME", length=100)
    private String unitname;
    
    /** 上班时间字符串 */
    @Column(name="SBSJSTR", length=30)
    private String sbsjstr;
    
    /** 下班时间字符串 */
    @Column(name="XBSJSTR", length=30)
    private String xbsjstr;
    
    /** 实际上班时间 */
    @Column(name="ACTSBSJSTR", length=30)
    private String actsbsjstr;
    
    /** 实际下班时间 */
    @Column(name="ACTXBSJSTR", length=30)
    private String actxbsjstr;
    
    /** 时间间隔（分） */
    @Column(name="TIMESTEP")
    private Integer timestep;
    
    /** 包含关系10包括开始，01包括结束，11包括两个时间点 */
    @Column(name="TIMEBOUND", length=10)
    private String timebound;
    
    /** 岗位标识 */
    @Column(name="JOBID", length=50)
    private String jobid;
    
    /** TMSORT */
    @Column(name="TMSORT")
    private Integer tmsort;
    
    /** TMUSED */
    @Column(name="TMUSED")
    private Integer tmused;
    

}

