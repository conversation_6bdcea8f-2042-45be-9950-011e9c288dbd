package com.yunhesoft.system.tds.entity.po;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

import com.yunhesoft.core.common.entity.BaseEntity;

import lombok.Getter;
import lombok.Setter;


/**
 * 台账数据源备注信息表
 * 
 */
@Entity
@Setter
@Getter
@Table(name = "TDS_ACCOUNT_MARKINFO")
public class TdsAccountMarkinfo extends BaseEntity {
	
    private static final long serialVersionUID = 1L;


    /** 数据源别名 */
    @Column(name="TDSALIAS", length=50)
    private String tdsalias;
    
    /** 日期 */
    @Column(name="RQ", length=50)
    private String rq;
    
    /** 核算对象代码，可能有多个 */
    @Column(name="UNITCODE", length=1000)
    private String unitcode;
    
    /** 班次 */
    @Column(name="CLSNO", length=100)
    private String clsno;
    
    /** 上班时间字符串 */
    @Column(name="SBSJSTR", length=30)
    private String sbsjstr;
    
    /** 下班时间字符串 */
    @Column(name="XBSJSTR", length=30)
    private String xbsjstr;
    
    /** 自定义表单标识 */
    @Column(name="FORMID", length=50)
    private String formid;
    
    /** 时间点 */
    @Column(name="TIMEPOInteger", length=50)
    private String timepoInteger;
    
    /** 列标识 */
    @Column(name="COLALIAS", length=50)
    private String colalias;
    
    /** 单元格标识，时间点和列标识 */
    @Column(name="MARK_KEY", length=200)
    private String markKey;
    
    /** 备注信息 */
    @Column(name="MARK_INFO", length=4000)
    private String markInfo;
    
    /** 备注对应数值 */
    @Column(name="VALSTR", length=1000)
    private String valstr;
    
    /** TMSORT */
    @Column(name="TMSORT")
    private Integer tmsort;
    
    /** TMUSED */
    @Column(name="TMUSED")
    private Integer tmused;
    

}

