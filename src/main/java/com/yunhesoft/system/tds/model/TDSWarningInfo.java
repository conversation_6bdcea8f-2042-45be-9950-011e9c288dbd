package com.yunhesoft.system.tds.model;

import com.yunhesoft.core.common.utils.TMUID;
import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.core.utils.spring.SpringUtils;
import com.yunhesoft.system.tools.warning.entity.vo.WarningInfoVo;
import com.yunhesoft.system.tools.warning.service.IWarningService;
import lombok.extern.log4j.Log4j2;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 预警信息查询
 *
 * <AUTHOR>
 * @since 2025-04-01
 */
@Log4j2
public class TDSWarningInfo extends ADataSource {

    private static final long serialVersionUID = 1L;

    //r3db实时数据库服务
    private IWarningService serv = SpringUtils.getBean(IWarningService.class);


    /**
     * 初始化数据源必需信息<br>
     *
     * @param initInfoObj
     * @category 初始化数据源必需信息
     */
    public void init(Object initInfoObj) {
        setDataSourceId(TMUID.getUID());
        setAutoLoad(false);
    }


    public void load() {
        this.load(1, 0);
    }

    public void load(int page, int pageSize) {
        this.initOutParams();
        List<WarningInfoVo> list = serv.getWarningInfo();
        if (StringUtils.isNotEmpty(list)) {
            for (WarningInfoVo vo : list) {
                int rowid = tds.addRow();
                Map<String, Object> rowRawMp = new HashMap<String, Object>(); // 显示值
                for (int n = 0; n < tOutParas.size(); n++) {
                    Object value = null;
                    String col = tOutParas.get(n).getAlias();
                    if ("title".equals(col)) {//时间
                        value = vo.getTitle();
                    } else if ("info".equals(col)) {
                        value = vo.getInfo();
                    } else if ("doinfo".equals(col)) {
                        value = vo.getDoinfo();
                    } else if ("createdt".equals(col)) {
                        value = vo.getCreatedt();
                    }
                    rowRawMp.put(col, value);
                    tds.set(rowid, n, value);
                }
                this.addData(rowRawMp);
            }
        }
    }

    /**
     * 初始化输出参数
     */
    private void initOutParams() {
        if (StringUtils.isEmpty(tOutParas)) {
            tOutParas = new ArrayList<TOutPara>();
            int rowId = 0;
            this.addOutPara(this.getOut("title", "类型", rowId, 120, "center", IDataSource.DataType.tdsString));
            rowId++;
            this.addOutPara(this.getOut("info", "预警信息", rowId, 480, "left", IDataSource.DataType.tdsString));
            rowId++;
            this.addOutPara(this.getOut("doinfo", "措施", rowId, 280, "left", IDataSource.DataType.tdsString));
            rowId++;
            this.addOutPara(this.getOut("createdt", "预警时间", rowId, 160, "center", IDataSource.DataType.tdsString));
        }
    }


    private TOutPara getOut(String alias, String name, int id, int width, String align, IDataSource.DataType dataType) {
        TOutPara top1 = new TOutPara(this);
        top1.setAlias(alias);
        top1.setName(name);
        top1.setID(id);
        top1.setWidth(width);
        top1.setVisible(true);
        top1.setAlign(align);
        top1.setDataType(dataType);
        return top1;
    }

    @Override
    public Boolean update() {
        return null;
    }


}
