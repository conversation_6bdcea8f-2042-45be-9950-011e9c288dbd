package com.yunhesoft.system.org.entity.vo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.yunhesoft.system.kernel.utils.excel.ExcelExt;
import lombok.Getter;
import lombok.Setter;

/**
 * SysOrgTree
 *
 * <AUTHOR>
 * @category 用于Excel导出
 * @date 2021/8/7
 */
@Setter
@Getter
public class SysOrgTree {

    @Excel(name = "ID", width = 25, orderNum = "1")
    @ExcelExt(align = "left")
    private String id;

    @Excel(name = "机构名称", width = 50, orderNum = "2")
    @ExcelExt(align = "left") // 导出列居左对齐
    private String orgname;

    @Excel(name = "机构类型", width = 20, orderNum = "3")
    @ExcelExt(align = "left") // 导出列居左对齐
    private String orgTypeLabel;
    private String orgType;


    @Excel(name = "机构分类", width = 20, orderNum = "4")
    @ExcelExt(align = "left") // 导出列居左对齐
    private String orgClassifyLabel;
    private String orgClassify;


    // @Excel(name = "排序序号", width = 10, orderNum = "5")
    private int tmSort;

    // 父机构编码
    private String porgcode;

}
