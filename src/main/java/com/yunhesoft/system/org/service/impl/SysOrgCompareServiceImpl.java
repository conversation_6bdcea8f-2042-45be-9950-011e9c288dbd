package com.yunhesoft.system.org.service.impl;

import java.util.List;

import javax.transaction.Transactional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.yunhesoft.system.kernel.service.EntityService;
import com.yunhesoft.system.kernel.service.model.Where;
import com.yunhesoft.system.org.entity.po.SysOrgCompare;
import com.yunhesoft.system.org.entity.po.SysOrgVersionsDetail;
import com.yunhesoft.system.org.service.ISysOrgCompareService;


/**
 * 对照表数据库操作实现类
 * <AUTHOR>
 *
 */
@Service
public class SysOrgCompareServiceImpl implements ISysOrgCompareService {

	@Autowired
	private EntityService entityService;
	
	
	@Override
	public List<SysOrgCompare> listData(String sourceOrgCode) {
		List<SysOrgCompare> list = null;
		Where where = Where.create();
		where.eq(SysOrgCompare::getSourceOrgCode, sourceOrgCode);
		list = entityService.queryList(SysOrgCompare.class, where);
		return list;
	}

	@Override
	public boolean saveData(List<SysOrgCompare> list) {
		int rs = entityService.insertBatch(list);
		if (rs <= 0) {
			return false;
		}
		return true;
	}

	@Override
	public boolean updateData(List<SysOrgCompare> list) {
		int rs = entityService.updateByIdBatch(list);
		if (rs <= 0) {
			return false;
		}
		return true;
		// return this.updateData(list);
	}

	@Override
	@Transactional
	public boolean insertData(List<SysOrgCompare> list) {
		return this.saveData(list);
	}

	@Override
	@Transactional
	public boolean deleteData(String id) {
		Where where = Where.create();
		where.eq(SysOrgVersionsDetail::getId, id);
		int rs = entityService.delete(SysOrgCompare.class, where);
		if (rs <= 0) {
			return false;
		} else {
			return true;
		}
		// return this.removeById(id);
	}

}