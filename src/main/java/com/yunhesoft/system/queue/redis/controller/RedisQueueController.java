package com.yunhesoft.system.queue.redis.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.yunhesoft.core.common.model.Res;
import com.yunhesoft.system.kernel.controller.BaseRestController;
import com.yunhesoft.system.queue.redis.impl.RedisQueueConsumerContainer;
import com.yunhesoft.system.queue.redis.impl.RedisQueueProducerService;
import com.yunhesoft.system.queue.redis.test.TestRedisQueueConsumer;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

@Api(tags = "redis消息队列")
@RestController
@RequestMapping("/redis-queue")
public class RedisQueueController extends BaseRestController {
	@Autowired
	private RedisQueueProducerService producer;

	@ApiOperation(value = "发送消息测试")
	@RequestMapping(value = "sendMessage", method = { RequestMethod.GET })
	public Res<?> sendMessage(String queueName, String message) {
		producer.sendMessage(queueName, message);
		return Res.OK();
	}

	@ApiOperation(value = "新增测试消息消费者")
	@RequestMapping(value = "addTestRedisQueueConsumer", method = { RequestMethod.GET })
	public Res<?> addTestRedisQueueConsumer(String queueName) {
		TestRedisQueueConsumer consumer = new TestRedisQueueConsumer();
		consumer.setQueueName(queueName);
		RedisQueueConsumerContainer.addConsumer(consumer);
		return Res.OK();
	}
}