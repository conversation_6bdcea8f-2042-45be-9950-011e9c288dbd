package com.yunhesoft.system.tools.todo.service;

import java.util.List;

import com.yunhesoft.system.tools.todo.entity.dto.ReadRecordDto;
import com.yunhesoft.system.tools.todo.entity.po.SysReadRecord;

/**
 * 系统已读未读接口
 * 
 * <AUTHOR>
 * @date 2022/06/01
 */
public interface ReadRecordService {
	/**
	 * 设置已读数据
	 */
	Boolean setReadRecord(ReadRecordDto dto);

	/**
	 * 获取已读列表
	 */
	List<SysReadRecord> getReadRecord(ReadRecordDto dto);

	/**
	 * 设置数据为未读（删除已读的数据）
	 */
	Boolean setUnReadRecord(ReadRecordDto dto);

}
