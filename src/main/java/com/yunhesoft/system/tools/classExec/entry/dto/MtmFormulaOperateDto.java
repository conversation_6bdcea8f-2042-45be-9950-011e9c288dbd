package com.yunhesoft.system.tools.classExec.entry.dto;

import java.util.ArrayList;
import java.util.List;

import com.yunhesoft.system.tools.classExec.entry.vo.MtmFormulaValueVo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 目标传导公式数据操作条件
 * 
 * @Description:
 * <AUTHOR>
 * @date 2021年11月17日
 */
@Data
@ApiModel(value="目标传导公式数据操作 DTO类",description="目标传导公式数据操作 DTO类")
public class MtmFormulaOperateDto {

	@ApiModelProperty(value = "查询条件开始日期")
	private String startDt;
	@ApiModelProperty(value = "查询条件截止日期")
	private String endDt;
	@ApiModelProperty(value = "查询条件列表 MtmFormulaValueVo.objType 类型（机构、岗位、人员） MtmFormulaValueVo.objCode 对应类型的代码 MtmFormulaValueVo.paramValue存放要取数据的表")
	private List<MtmFormulaValueVo> queryList = new ArrayList<MtmFormulaValueVo>();
	@ApiModelProperty(value = "要保存的数据列表，json格式，数据存储在MtmFormulaValueVo.formulaValue字段中，对应表存储在MtmFormulaValueVo.paramValue中")
	private List<MtmFormulaValueVo> saveList = new ArrayList<MtmFormulaValueVo>();
	
}
