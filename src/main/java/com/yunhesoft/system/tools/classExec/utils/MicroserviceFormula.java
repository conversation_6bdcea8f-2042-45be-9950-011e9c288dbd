package com.yunhesoft.system.tools.classExec.utils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.core.utils.spring.SpringUtils;
import com.yunhesoft.system.tools.classExec.entry.dto.MtmFormulaOperateDto;
import com.yunhesoft.system.tools.classExec.entry.dto.MtmFormulaTreeDto;
import com.yunhesoft.system.tools.classExec.entry.dto.MtmFormulaValueDto;
import com.yunhesoft.system.tools.classExec.entry.vo.MtmFormulaApiVo;
import com.yunhesoft.system.tools.classExec.entry.vo.MtmFormulaTreeVo;
import com.yunhesoft.system.tools.classExec.entry.vo.MtmFormulaValueVo;

import lombok.extern.log4j.Log4j2;
//通用接口解析类，不能带@Service，只能直接new否则会出现重复数据
@Log4j2
public class MicroserviceFormula extends MtmFormulaModel {
    
	@Override
	public String getModuleCode() {
		// TODO Auto-generated method stub
		return  sysConfig==null?"":sysConfig.getModuleCode();//本模块的编码
	}

	@Override
	public String getModuleName() {
		// TODO Auto-generated method stub
		return sysConfig==null?"":sysConfig.getModuleName();//本模块的名称
	}
	/**
	 * 获取公式tree
	 * @category 获取公式tree
	 * <AUTHOR> 
	 * @param pId 父ID 加载根节点时会传入机构代码(为了扩展性，这里可能传入多个机构代码，用逗号分隔)
	 * @param isRootLoad 是否在加载根节点
	 * @return
	 */
	@Override
	protected List<MtmFormulaTreeVo> getFormulaTree(String pId,boolean isRootLoad) {
		// TODO Auto-generated method stub
		List<MtmFormulaTreeVo> result = new ArrayList<MtmFormulaTreeVo>();
		if(StringUtils.isNotEmpty(sysConfig.getServiceName()) && StringUtils.isNotEmpty(sysConfig.getGetFormulaTree())){//有链接
			MtmFormulaTreeDto dto = new MtmFormulaTreeDto();
			dto.setPId(pId);
			dto.setIsRootLoad(isRootLoad);
			dto.setTreeParam(this.getTreeParam());//树形加载参数（各个模块可能不同）
			MtmFormulaApiVo res = execRestApi(sysConfig.getServiceName(),sysConfig.getGetFormulaTree(),dto,false);//调用api
			if(res!=null && res!=null) {
				result.addAll(res.getTreeList());
			}
		}
		return result;
	}
	/**
	 * 获取公式解析结果
	 * @category 获取公式解析结果
	 * @param startDt 开始日期
	 * @param endDt 截止日期
	 * @param formulaTextList 公式列表（去重复）
	 * @param formulaTextObjList 公式列表(按对象存储，不去除重复)
	 * @return
	 */
	@Override
	protected List<MtmFormulaValueVo> getFormulaValue(String startDt, String endDt,
			List<String> formulaTextList,List<MtmFormulaValueVo> formulaTextObjList) {
		List<MtmFormulaValueVo> result = new ArrayList<MtmFormulaValueVo>();
		if(StringUtils.isNotEmpty(sysConfig.getServiceName()) && StringUtils.isNotEmpty(sysConfig.getGetFormulaValue())){//有链接
			MtmFormulaValueDto dto = new MtmFormulaValueDto();
			dto.setStartDt(startDt);
			dto.setEndDt(endDt);
			dto.setFormulaTextList(formulaTextList);
			dto.setFormulaTextObjList(formulaTextObjList);
			MtmFormulaApiVo res = execRestApi(sysConfig.getServiceName(),sysConfig.getGetFormulaValue(),dto,false);//调用api
			if(res!=null && res.getValueList()!=null) {
				result.addAll(res.getValueList());
			}
		}
		return result;
		
		// TODO Auto-generated method stub
		//传入的内容 startDt = 2024-01 endDt = 2024-03 formulaTextList = [a.zb1,b.zb2]  传进来的公式，就是之前树形上的公式编码
//		
//		List<MtmFormulaValueVo> reuslt = new ArrayList<MtmFormulaValueVo>();
//		//解析结果
//		MtmFormulaValueVo zb1_1 = new MtmFormulaValueVo();
//		zb1_1.setFormulaText("a.zb1");//把传入的公式写到这个里
//		zb1_1.setObjCode("JGDM111111");//这里存对象ID 可以是人员id 岗位id 机构id
//		zb1_1.setObjType(1);//这个是code的类型1：机构，2：岗位，3：人员
//		zb1_1.setFormulaValue("15.6");
//		reuslt.add(zb1_1);
//		
//		MtmFormulaValueVo zb1_2 = new MtmFormulaValueVo();
//		zb1_2.setFormulaText("a.zb1");//把传入的公式写到这个里
//		zb1_2.setObjCode("JGDM222222");//这里存对象ID 可以是人员id 岗位id 机构id
//		zb1_2.setObjType(1);//这个是code的类型1：机构，2：岗位，3：人员
//		zb1_2.setFormulaValue("-5.8");
//		reuslt.add(zb1_2);
//		//一条公式如果只能解析出一个结果，那就不需要加objCode和type了
//		//如果这个是有指向性的数据还要叫，比如取装置平稳率，公式出的是装置下5个班组的结果，如果只查到了1个班组（这时就是解析出1个结果了），那就还是需要加上班组代码（objCode和objType）
//		//如果这个就是只出一个结果的公式，比如取装置总平稳率，公式出的就只有该装置的总平稳率，那就不要加上objCode和objType了
//		MtmFormulaValueVo zb2_1 = new MtmFormulaValueVo();
//		zb2_1.setFormulaText("b.zb2");//把传入的公式写到这个里
//		//zb2_1.setObjCode("ZYID111111");//这里存对象ID 可以是人员id 岗位id 机构id
//		//zb2_1.setObjType(3);//这个是code的类型1：机构，2：岗位，3：人员
//		zb2_1.setFormulaValue("11.2");
//		reuslt.add(zb2_1);
//		return reuslt;
	}
	/**
	 * 根据条件获取模块内部数据
	 * @category 根据条件获取模块内部数据 
	 * <AUTHOR> 
	 * @param startDt 开始日期
	 * @param endDt 截止日期
	 * @param queryList 查询条件列表 MtmFormulaValueVo.objType 类型（机构、岗位、人员） MtmFormulaValueVo.objCode 对应类型的代码 MtmFormulaValueVo.paramValue存放要取数据的表
	 * @return 查询结果转为 json存入MtmFormulaValueVo.formulaValue中
	 */
	@Override
	public void getJsonData(String startDt, String endDt, List<MtmFormulaValueVo> queryList) {
		// TODO Auto-generated method stub
		if(StringUtils.isNotEmpty(sysConfig.getServiceName()) && StringUtils.isNotEmpty(sysConfig.getGetJsonData())){//有链接
			if(StringUtils.isNotEmpty(queryList)){
				MtmFormulaOperateDto dto = new MtmFormulaOperateDto();
				dto.setStartDt(startDt);
				dto.setEndDt(endDt);
				dto.setQueryList(queryList);
				MtmFormulaApiVo res = execRestApi(sysConfig.getServiceName(),sysConfig.getGetJsonData(),dto,false);//调用api
				if(res!=null && res.getValueList()!=null) {
					HashMap<String,MtmFormulaValueVo> resultMap = new HashMap<String,MtmFormulaValueVo>();
					for(MtmFormulaValueVo temp:res.getValueList()){
						resultMap.put(getMtmFormulaValueKey(temp), temp);
					}
					for(MtmFormulaValueVo temp:queryList){
						MtmFormulaValueVo resultBean = resultMap.get(getMtmFormulaValueKey(temp));//获取数据
						if(resultBean!=null) {//找到了对应数据
							temp.setFormulaValue(resultBean.getFormulaValue());//公式公司解析的值
							temp.setParamValue(resultBean.getParamValue());//参数解析的值
						}
					}
				}
			}
		}
	}
	
	/**
	 * 获取公式key
	 * @category 
	 * <AUTHOR> 
	 * @param bean
	 * @return
	 */
	private String getMtmFormulaValueKey(MtmFormulaValueVo bean) {
		return bean.getFormulaText()+"___"+bean.getIndexName()+"___"+bean.getObjCode();
	}
	/**
	 * 保存模块数据
	 * @category 保存模块数据
	 * <AUTHOR> 
	 * @param saveList 要保存的数据列表，json格式，数据存储在MtmFormulaValueVo.formulaValue字段中，对应表存储在MtmFormulaValueVo.paramValue中
	 * @return
	 */
	@Override
	public boolean saveJsonData(List<MtmFormulaValueVo> saveList) {
		// TODO Auto-generated method stub
		boolean result =false;
		if(StringUtils.isNotEmpty(sysConfig.getServiceName()) && StringUtils.isNotEmpty(sysConfig.getSaveJsonData())){//有链接
			if(StringUtils.isNotEmpty(saveList)){
				MtmFormulaOperateDto dto = new MtmFormulaOperateDto();
				dto.setSaveList(saveList);
				MtmFormulaApiVo res = execRestApi(sysConfig.getServiceName(),sysConfig.getSaveJsonData(),dto,false);//调用api
				if(res!=null && res.getResult()!=null) {
					result =res.getResult().booleanValue();
				}
			}
		}
		return result;
	}
	/**
	 * 公式模块数据初始化
	 * @category 公式模块数据初始化
	 * <AUTHOR>
	 */
	@Override
	protected void init() {
		// TODO Auto-generated method stub
		if(StringUtils.isNotEmpty(sysConfig.getServiceName()) && StringUtils.isNotEmpty(sysConfig.getInit())){//有链接
			MtmFormulaOperateDto dto = new MtmFormulaOperateDto();//无参
			execRestApi(sysConfig.getServiceName(),sysConfig.getInit(),dto,true);//调用api,要求在无token时创建token(初始化时没有token)
		}
	}
	/**
	 * 获取操作Service
	 * @category 获取操作Service
	 * <AUTHOR> 
	 * @return
	 */
	private MicroserviceFormulaService getService() {
		MicroserviceFormulaService result = null;
		try{			
			result = (MicroserviceFormulaService) SpringUtils.getBean(MicroserviceFormulaService.class);
		}catch(Exception e){
			log.error("", e);
		}
		return result;
	}
	/**
	 * 执行微服务调用
	 * @category 
	 * <AUTHOR> 
	 * @param <T>
	 * @param <E>
	 * @param cls
	 * @param serviceName
	 * @param requestPath
	 * @param param
	 * @param createToken 是否在无token时创建token
	 * @return
	 */
	private <T> MtmFormulaApiVo execRestApi(String serviceName,String requestPath,T param,boolean createToken) {
		MtmFormulaApiVo result = null;
		MicroserviceFormulaService serv = getService();
		if(serv!=null) {
			result = serv.execRestApi(serviceName, requestPath, param, createToken);
		}else {
			log.error("无法实例化MicroserviceFormula");
		}
		return result;
	}
}
