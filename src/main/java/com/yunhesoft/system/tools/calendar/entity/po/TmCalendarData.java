package com.yunhesoft.system.tools.calendar.entity.po;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

import com.alibaba.fastjson.annotation.JSONField;
import com.yunhesoft.core.common.entity.BaseEntity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Entity
@Setter
@Getter
@ToString
@Table(name = "TM_CALENDAR_DATA")
public class TmCalendarData extends BaseEntity {
	
	/** 年份 */
	@Column(name = "YEAR", length = 200)
	private String year;
	
	/** 月份 */
	@Column(name = "MONTH", length = 200)
	private String month;
	
	/** 日期 */
	@JSONField(format = "yyyy-MM-dd HH:mm:ss")
	@ApiModelProperty(value = "日期", required = false, hidden = true)
	@Column(name = "TM_DATE")
	private Date TmDate;
	
	/** 类型  1：上班   0：休息*/
	@Column(name = "DAY_VALUE")
	private Integer dayValue;
	
	/** 1：上班（节假日）   0：法定休息  2：周末  3周一~周五  */
	@Column(name = "TM_FLAG")
	private Integer tmFlag;
	
	/** 节日编码 fdjr、tzxx */
	@Column(name = "FESTBM", length = 255)
	private String festBm;

	/** 备注 */
	@Column(name = "TM_COMMENT", length = 2000)
	private String TmComment;
}
