package com.yunhesoft.system.tools.calendar.controller;

import com.yunhesoft.core.common.model.Res;
import com.yunhesoft.core.common.utils.DateTimeUtils;
import com.yunhesoft.system.kernel.controller.BaseRestController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;


@RestController
@Api(tags = "时间日期工具")
@RequestMapping("/calendar/calendarTool")
public class CalendarToolController extends BaseRestController {

	@ApiOperation(value = "获取当前日期")
	@RequestMapping(value = "getNowDate", method = { RequestMethod.GET })
	public Res<?> getNowDate() {
		return Res.OK(DateTimeUtils.getND());
	}

	@ApiOperation(value = "获取当前日期")
	@RequestMapping(value = "getNowDateStr", method = { RequestMethod.GET })
	public Res<?> getNowDateStr() {
		return Res.OK(DateTimeUtils.getNowDateStr());
	}

	@ApiOperation(value = "获取当前时间")
	@RequestMapping(value = "getNowDateTime", method = { RequestMethod.GET })
	public Res<?> getNowDateTime() {
		return Res.OK(DateTimeUtils.getNowDate());
	}
	@ApiOperation(value = "获取当前时间")
	@RequestMapping(value = "getNowDateTimeStr", method = { RequestMethod.GET })
	public Res<?> getNowDateTimeStr() {
		return Res.OK(DateTimeUtils.getNowDateTimeStr());
	}



}
