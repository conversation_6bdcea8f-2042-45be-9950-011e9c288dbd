package com.yunhesoft.system.tools.calendar.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.yunhesoft.core.common.model.Res;
import com.yunhesoft.system.kernel.controller.BaseRestController;
import com.yunhesoft.system.tools.calendar.entity.dto.CalendarDataDto;
import com.yunhesoft.system.tools.calendar.entity.dto.CalendarQueryDto;
import com.yunhesoft.system.tools.calendar.entity.dto.SaveDataDto;
import com.yunhesoft.system.tools.calendar.entity.dto.WeekDataDto;
import com.yunhesoft.system.tools.calendar.service.CalendarService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * 保存日历
 * 
 * <AUTHOR>
 * @date 2023/2/16
 */
@RestController
@Api(tags = "日历设置")
@RequestMapping("/calendar/calendarSet")
public class CalendarSetController extends BaseRestController {

	@Autowired
	private CalendarService serve; // 计划变更服务

	@ApiOperation(value = "查询日期  数组")
	@RequestMapping(value = "getCalendarArrData", method = { RequestMethod.POST })
	public Res<?> getCalendarArrData(@RequestBody CalendarQueryDto param) {
		Res<List<String>> res = new Res<List<String>>();
		List<String> list = serve.getTmCalendarDateList(param);
		res.setResult(list);
		return res;
	}

	@ApiOperation(value = "查询日期  map")
	@RequestMapping(value = "getCalendarMapData", method = { RequestMethod.POST })
	public Res<?> getTmCalendarMap(@RequestBody CalendarQueryDto param) {
		Res<String> res = new Res<String>();
		String list = serve.getTmCalendarMap(param);
		res.setResult(list);
		return res;
	}

	@ApiOperation(value = "查询日期 数组全量")
	@RequestMapping(value = "getTmCalendarDateAllList", method = { RequestMethod.POST })
	public Res<?> getTmCalendarDateAllList(@RequestBody CalendarQueryDto param) {
		Res<CalendarDataDto> res = new Res<CalendarDataDto>();
		CalendarDataDto list = serve.getTmCalendarDateAllList(param);
		res.setResult(list);
		return res;
	}

	@ApiOperation(value = "保存日历")
	@RequestMapping(value = "saveCalendarData", method = { RequestMethod.POST })
	public Res<?> saveCalendarData(@RequestBody SaveDataDto param) {
		String res = serve.saveCalendarData(param);
		return Res.OK(res);
	}

	@ApiOperation(value = "同步日历")
	@RequestMapping(value = "syncCalendar", method = { RequestMethod.POST })
	public Res<?> saveCalendarData(@RequestBody CalendarQueryDto param) {
		boolean res = serve.syncCalendar(param.getYear());
		return Res.OK(res);
	}

	@ApiOperation(value = "按年获取日历日期集合")
	@RequestMapping(value = "getCalendarDateListByYear", method = { RequestMethod.POST })
	public Res<?> getCalendarDateListByYear(@RequestBody CalendarQueryDto param) {
		return Res.OK(serve.getCalendarDateListByYear(param.getYear(), Integer.parseInt(param.getType())));
	}

	@ApiOperation(value = "按年获取日历假期信息")
	@RequestMapping(value = "getTmCalendarInfoList", method = { RequestMethod.POST })
	public Res<?> getTmCalendarInfoList(@RequestBody CalendarQueryDto param) {
		return Res.OK(serve.getTmCalendarInfoList(param));
	}

	@ApiOperation(value = "生成周初始化数据")
	@RequestMapping(value = "createWeekData", method = { RequestMethod.POST })
	public Res<?> createWeekData(@RequestBody WeekDataDto param) {
		serve.createWeekData(param.getStartYear(), param.getEndYear(), param.getWeekStartNum());
		return Res.OK("生成完毕");
	}

}
