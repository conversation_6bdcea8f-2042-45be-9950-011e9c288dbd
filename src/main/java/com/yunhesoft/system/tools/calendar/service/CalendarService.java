package com.yunhesoft.system.tools.calendar.service;

import java.util.Date;
import java.util.List;
import java.util.Map;

import com.yunhesoft.system.tools.calendar.entity.dto.CalendarDataDto;
import com.yunhesoft.system.tools.calendar.entity.dto.CalendarQueryDto;
import com.yunhesoft.system.tools.calendar.entity.dto.CalendarYearDto;
import com.yunhesoft.system.tools.calendar.entity.dto.SaveDataDto;

public interface CalendarService {

	List<String> getTmCalendarDateList(CalendarQueryDto dto);

	CalendarDataDto getTmCalendarDateAllList(CalendarQueryDto dto);

	String saveCalendarData(SaveDataDto param);

	String getTmCalendarMap(CalendarQueryDto dto);

	/**
	 * 同步年数据
	 * 
	 * @param year
	 */
	boolean syncCalendar(String year);

	/**
	 * 刨除节假日的两日期相减
	 * 
	 * @param kssj 开始时间
	 * @param jzsj 截止时间
	 * @return 截止时间-开始时间
	 */
	int dayDiffWithoutHoliday(Date kssj, Date jzsj);

	/**
	 * 刨除节假日的两日期相减
	 * 
	 * @param kssj 开始时间
	 * @param jzsj 截止时间
	 * @return 截止时间-开始时间
	 */
	long getDateBwtweenHolidays(Date kssj, Date jzsj);

	/**
	 * 两日期间的休息节假日天数(不包含最后出现的节假日)
	 * 
	 * @param kssj 开始时间
	 * @param jzsj 截止时间
	 * @return
	 */
	long getDateBwtweenHolidaysWithoutLast(Date kssj, Date jzsj);

	/**
	 * 检查日期是否休息
	 * 
	 * @param day
	 * @return true：休息日 false：工作日
	 */
	boolean isHolidays(Date day);

	/**
	 * 刨除节假日的日期加
	 * 
	 * @param
	 * @return
	 */
	Date doDateWithoutHoliday(Date day, int days);

	/**
	 * 获取大于等于当前日期的第一个工作日
	 * 
	 * @param
	 * @return
	 */
	Date getFirstWorkDay(Date dt);

	/**
	 * 按年份获取特定日期
	 * 
	 * @param
	 * @return
	 */
	List<Long> getCalendarDateListByYear(String year, int dayValue);

	/**
	 * 日历信息查询
	 * 
	 * @param dto
	 * @return
	 */
	Map<String, CalendarYearDto> getTmCalendarInfoList(CalendarQueryDto dto);

	/**
	 * 生成周数据
	 * 
	 * @param startYear    开始年
	 * @param endYear      截止年
	 * @param weekStartNum 每周开始周数
	 */
	void createWeekData(Integer startYear, Integer endYear, Integer weekStartNum);

}
