package com.yunhesoft.system.tools.database.entity.po;

import java.util.Date;
import java.util.Map;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
import javax.persistence.UniqueConstraint;

import lombok.Data;

@Entity
@Data
@Table(name = "SYS_DB_TABLE_FIELD", uniqueConstraints = {
		@UniqueConstraint(columnNames = { "TABLE_ID", "FIELD_NAME" }) })
public class SysDbTableField {
	/** 表格标识 */
	@Column(name = "TABLE_ID")
	private long TableId;
	/** 字段标识 */
	@Id
	@Column(name = "ID")
	private long id;
	/** 字段排序 */
	@Column(name = "ORDER_ID")
	private int orderId;
	/** 字段名称 */
	@Column(name = "FIELD_NAME", length = 32)
	private String fieldName;
	/** 主键标识 */
	@Column(name = "IS_KEY")
	private Integer isKey;
	/** 允许为空 */
	@Column(name = "IS_NULLABLE")
	private Integer isNullable = 1;
	/** 字段备注 */
	@Column(name = "TABLE_COMMENT")
	private String comment;
	/** 字段类型 */
	@Column(name = "FIELD_TYPE", length = 50)
	private String fieldType;
	@Column(name = "DEFAULT_VALUE")
	private String defaultValue;
	/** 字段长度 */
	@Column(name = "FIELD_LENGTH")
	private Integer fieldLength;
	/** 字段精度 */
	@Column(name = "PRECISION_")
	private Integer precision;

	/** 创建时间 */
	@Column(name = "CREATE_TIME")
	private Date createTime;
	/** 更新时间 */
	@Column(name = "UPDATE_TIME")
	private Date updateTime;
	@Column(name = "CREATE_BY")
	private String createBy;
	@Column(name = "UPDATE_BY")
	private String updateBy;
	/** 数据库中不存在 */
	@Column(name = "IS_SYNCHRONIZED")
	private Integer isSynchronized;

	@Transient
	private String tableName;
	/** 删除标识 0 创建 1 逻辑删除 11 实际删除 2 更新 */
	@Transient
	private int status;
	// 差异 字段名称，字段值
	@Transient
	private Map<String, Object> differences;
}
