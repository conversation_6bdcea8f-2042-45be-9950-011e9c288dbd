package com.yunhesoft.system.tools.database.service;

import java.util.List;

import com.yunhesoft.system.kernel.service.model.TableColumnInfo;
import com.yunhesoft.system.kernel.service.model.TableInfo;

public interface SysDataBaseService {

	/**
	 * 获取数据库表列表
	 * 
	 * @return
	 */
	List<TableInfo> getTables();

	/**
	 * 根据表名称获取表信息
	 * 
	 * @param tableName
	 * @return
	 */
	TableInfo getTable(String tableName);

	/**
	 * 根据表名获取字段信息
	 * 
	 * @param tableName
	 * @return
	 */
	List<TableColumnInfo> getColumns(String tableName);

}
