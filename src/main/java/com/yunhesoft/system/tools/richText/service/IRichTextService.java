package com.yunhesoft.system.tools.richText.service;

import org.springframework.web.multipart.MultipartFile;

import com.yunhesoft.system.tools.files.entity.vo.FileInfo;
import com.yunhesoft.system.tools.richText.entity.vo.FileInformation;

/**
 * @Description: 富文本编辑器上传服务
 * <AUTHOR>
 * @date 2021-12-09
 */
public interface IRichTextService {

	/**
	 * 上传图片
	 * <AUTHOR>
	 * @param
	 * @return
	 */
	public FileInfo uploadImg(MultipartFile image,String moduleCode);

	public FileInformation uploadFile(MultipartFile image, String moduleCode);
}
