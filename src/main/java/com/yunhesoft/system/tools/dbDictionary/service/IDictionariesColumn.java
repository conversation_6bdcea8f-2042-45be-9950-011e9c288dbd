package com.yunhesoft.system.tools.dbDictionary.service;

import com.yunhesoft.system.tools.dbDictionary.entity.po.DictionariesData;

import java.util.List;
/**
 * IDictionariesColumn
 *
 * <AUTHOR>
 * @date 2019/12/24
 */
public interface IDictionariesColumn {

	/**
	 * 保存表信息
	 * @param list
	 * @param name
	 * @return
	 */
	//public boolean saveDataColumn1(List<DictionariesData> list, String name);
	/**
	 * 更新表信息
	 * @param list
	 * @param name
	 * @return
	 */
	public boolean upDataColumn(List<DictionariesData> list,String name);
	/**
	 * 反转数据库表到数据库字典
	 */
	public Boolean getFzSql();
}
