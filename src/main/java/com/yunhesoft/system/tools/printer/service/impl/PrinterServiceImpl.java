package com.yunhesoft.system.tools.printer.service.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.yunhesoft.core.common.utils.TMUID;
import com.yunhesoft.core.utils.ObjUtils;
import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.system.kernel.service.EntityService;
import com.yunhesoft.system.kernel.service.model.Where;
import com.yunhesoft.system.tools.printer.entity.dto.PrinterSaveWidthDto;
import com.yunhesoft.system.tools.printer.entity.po.PrinterColumns;
import com.yunhesoft.system.tools.printer.entity.po.PrinterConfig;
import com.yunhesoft.system.tools.printer.entity.vo.PrinterColumnsVo;
import com.yunhesoft.system.tools.printer.service.PrinterService;

import lombok.extern.log4j.Log4j2;

/**
 * 表格打印服务实现类
 * 
 * <AUTHOR>
 * @since 2022-10-27
 *
 */
@Service
@Log4j2
public class PrinterServiceImpl implements PrinterService {

	@Autowired
	private EntityService dao;

	/**
	 * 获取打印表格列信息
	 * 
	 * @param moduelCode
	 * @param code
	 * @return
	 */
	@Override
	public Map<String, PrinterColumnsVo> getPrinterColMap(String moduleCode, String code) {
		Map<String, PrinterColumnsVo> map = new HashMap<String, PrinterColumnsVo>();
		List<PrinterColumns> list = this.getPrinterColumns(moduleCode, code);
		if (list != null) {
			for (PrinterColumns e : list) {
				map.put(e.getAlias(), this.columns2Vo(e));
			}
		}
		return map;
	}

	/**
	 * 获取打印表格列信息
	 * 
	 * @param moduelCode
	 * @param code
	 * @return
	 */
	@Override
	public List<Map<String, Object>> getPrinterColList(String moduleCode, String code) {
		List<Map<String, Object>> colList = new ArrayList<Map<String, Object>>();
		List<PrinterColumns> list = this.getPrinterColumns(moduleCode, code);
		if (list != null) {
			for (PrinterColumns e : list) {
				PrinterColumnsVo vo = this.columns2Vo(e);
				Map<String, Object> map = ObjUtils.convertToMap(vo);
				colList.add(map);
			}
		}
		return colList;
	}

	/**
	 * 数据库获取列信息
	 * 
	 * @param moduleCode
	 * @param code
	 * @return
	 */
	private List<PrinterColumns> getPrinterColumns(String moduleCode, String code) {
		List<PrinterColumns> list = null;
		Where where = Where.create();
		where.eq(PrinterColumns::getModuleCode, moduleCode);
		where.eq(PrinterColumns::getCode, code);
		list = dao.queryList(PrinterColumns.class, where);
		return list;
	}

//	private List<PrinterColumnsVo> columns2Vo(List<PrinterColumns> list) {
//		List<PrinterColumnsVo> voList = new ArrayList<PrinterColumnsVo>();
//		if (list != null) {
//			for (PrinterColumns e : list) {
//				voList.add(columns2Vo(e));
//			}
//		}
//		return voList;
//	}

	/**
	 * 打印列po转换为vo
	 * 
	 * @param bean
	 * @return
	 */
	private PrinterColumnsVo columns2Vo(PrinterColumns bean) {
		PrinterColumnsVo vo = new PrinterColumnsVo();
		vo.setAlias(bean.getAlias());
		vo.setHeader(bean.getColHeader());
		if (bean.getIsshow() == null || bean.getIsshow() == 1) {
			vo.setIsshow(true);
		} else {
			vo.setIsshow(false);
		}
		vo.setWidth(bean.getColWidth());
		vo.setTmsort(bean.getTmsort());
		return vo;
	}

	/**
	 * 打印列vo转换po
	 * 
	 * @param moduleCode
	 * @param code
	 * @param vo
	 * @return
	 */
	private PrinterColumns vo2columns(String moduleCode, String code, PrinterColumnsVo vo) {
		PrinterColumns bean = new PrinterColumns();
		bean.setModuleCode(moduleCode);
		bean.setCode(code);
		bean.setAlias(vo.getAlias());
		bean.setColHeader(vo.getHeader());
		bean.setColWidth(vo.getWidth());
		bean.setId(TMUID.getUID());
		if (vo.getIsshow() != null) {
			bean.setIsshow(vo.getIsshow() ? 1 : 0);
		}
		bean.setTmsort(vo.getTmsort());
		return bean;
	}

	/**
	 * 打印列vo转换为po
	 * 
	 * @param moduleCode
	 * @param code
	 * @param voList
	 * @return
	 */
	private List<PrinterColumns> vo2columns(String moduleCode, String code, List<PrinterColumnsVo> voList) {
		List<PrinterColumns> list = new ArrayList<PrinterColumns>();
		if (voList != null) {
			for (PrinterColumnsVo vo : voList) {
				list.add(vo2columns(moduleCode, code, vo));
			}
		}
		return list;
	}

	/**
	 * 保存表格列信息
	 * 
	 * @param moduleCode
	 * @param code
	 * @param data
	 * @return
	 */
	@Override
	public boolean savePrinterColList(String moduleCode, String code, List<PrinterColumnsVo> data) {
		List<PrinterColumns> list = this.vo2columns(moduleCode, code, data);
		if (StringUtils.isNotEmpty(list)) {
			boolean bln = this.deletePrinterColumns(moduleCode, code);
			if (bln) {
				int i = dao.insertBatch(list);
				return i > 0 ? true : false;
			}
		}
		return false;
	}

	/**
	 * 删除打印配置
	 * 
	 * @param moduleCode
	 * @param code
	 * @return
	 */
	private boolean deletePrinterColumns(String moduleCode, String code) {
		Where where = Where.create();
		where.eq(PrinterColumns::getModuleCode, moduleCode);
		where.eq(PrinterColumns::getCode, code);
		dao.delete(PrinterColumns.class, where);
		return true;
	}

	/**
	 * 删除打印基础配置
	 * 
	 * @param moduleCode
	 * @param code
	 * @return
	 */
	private boolean deletePrinterConfig(String moduleCode, String code) {
		Where where = Where.create();
		where.eq(PrinterConfig::getModuleCode, moduleCode);
		where.eq(PrinterConfig::getCode, code);
		dao.delete(PrinterConfig.class, where);
		return true;
	}

	/**
	 * 保存表格宽度
	 * 
	 * @param dto
	 * @return
	 */
	@Override
	public boolean savePrinterColWidth(PrinterSaveWidthDto dto) {
		int i = 0;
		Where where = Where.create();
		where.eq(PrinterColumns::getModuleCode, dto.getModuleCode());
		where.eq(PrinterColumns::getCode, dto.getCode());
		where.eq(PrinterColumns::getAlias, dto.getAlias());
		List<PrinterColumns> list = dao.queryList(PrinterColumns.class, where);
		PrinterColumns bean = null;
		if (StringUtils.isNotEmpty(list)) {
			if (list.size() == 1) {
				bean = list.get(0);
			} else {
				dao.deleteByIdBatch(list);
			}
		}
		if (bean == null) {// 添加
			bean = new PrinterColumns();
			bean.setId(TMUID.getUID());
			bean.setAlias(dto.getAlias());
			bean.setCode(dto.getCode());
			bean.setModuleCode(dto.getModuleCode());
			bean.setColWidth(dto.getWidth());
			bean.setIsshow(1);
			i = dao.insert(bean);
		} else {// 修改
			bean.setColWidth(dto.getWidth());
			i = dao.rawUpdateById(bean);
		}
		return i > 0 ? true : false;
	}

	/**
	 * 获取打印配置信息
	 * 
	 * @param moduleCode
	 * @param code
	 * @return
	 */
	@Override
	public Map<String, Object> getPrinterConfig(String moduleCode, String code) {
		Map<String, Object> map = new HashMap<String, Object>();
		Where where = Where.create();
		where.eq(PrinterConfig::getModuleCode, moduleCode);
		where.eq(PrinterConfig::getCode, code);
		List<PrinterConfig> list = dao.queryList(PrinterConfig.class, where);
		if (StringUtils.isNotEmpty(list)) {
			for (PrinterConfig e : list) {
				String key = e.getCfgKey();
				String value = e.getCfgValue();
				if (StringUtils.isNotEmpty(key) && value != null) {
					Boolean isBln = isBool(value);
					if (isBln != null) {
						map.put(key, isBln);
					} else if (isInteger(value)) {
						try {
							map.put(key, Integer.parseInt(value));
						} catch (Exception ex) {
							log.error("", ex);
						}
					} else {
						map.put(key, value);
					}
				}
			}
		}
		return map;
	}

	/**
	 * 判断是否为整数
	 * 
	 * @param str
	 * @return
	 */
	private boolean isInteger(String str) {
		if (StringUtils.isNotEmpty(str)) {
			Pattern pattern = Pattern.compile("^[-\\+]?[\\d]*$");
			return pattern.matcher(str).matches();
		} else {
			return false;
		}
	}

	/**
	 * 判断是否为bool
	 * 
	 * @param str
	 * @return
	 */
	private Boolean isBool(String str) {
		if ("true".equalsIgnoreCase(str)) {
			return true;
		}
		if ("false".equalsIgnoreCase(str)) {
			return false;
		}
		return null;
	}

	/**
	 * 保存表格配置信息
	 * 
	 * @param moduleCode
	 * @param val
	 * @return
	 */
	@Override
	public boolean savePrinterConfig(String moduleCode, String code, Map<String, Object> val) {
		if (StringUtils.isNotEmpty(val)) {
			if (this.deletePrinterConfig(moduleCode, code)) {
				List<PrinterConfig> list = new ArrayList<PrinterConfig>();
				for (String key : val.keySet()) {
					Object value = val.get(key);
					if (value != null) {
						PrinterConfig bean = new PrinterConfig();
						bean.setId(TMUID.getUID());
						bean.setCode(code);
						bean.setModuleCode(moduleCode);
						bean.setCfgKey(key);
						bean.setCfgValue(value.toString());
						list.add(bean);
					}
				}
				if (list.size() > 0) {
					int i = dao.insertBatch(list);
					return i > 0 ? true : false;
				}
			}
		}
		return false;
	}

}
