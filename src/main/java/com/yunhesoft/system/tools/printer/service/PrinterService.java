package com.yunhesoft.system.tools.printer.service;

import java.util.List;
import java.util.Map;

import com.yunhesoft.system.tools.printer.entity.dto.PrinterSaveWidthDto;
import com.yunhesoft.system.tools.printer.entity.vo.PrinterColumnsVo;

/**
 * 表格打印服务
 * 
 * <AUTHOR>
 * @since 2022-10-27
 *
 */
public interface PrinterService {
	/**
	 * 获取打印表格列信息
	 * 
	 * @param moduelCode
	 * @param code
	 * @return
	 */
	Map<String, PrinterColumnsVo> getPrinterColMap(String moduleCode, String code);

	/**
	 * 获取打印表格列信息
	 * 
	 * @param moduelCode
	 * @param code
	 * @return
	 */
	List<Map<String, Object>> getPrinterColList(String moduleCode, String code);

	/**
	 * 保存表格列信息
	 * 
	 * @param moduleCode
	 * @param code
	 * @param data
	 * @return
	 */
	boolean savePrinterColList(String moduleCode, String code, List<PrinterColumnsVo> data);

	/**
	 * 保存表格宽度
	 * 
	 * @param dto
	 * @return
	 */
	boolean savePrinterColWidth(PrinterSaveWidthDto dto);

	/**
	 * 获取打印配置信息
	 * 
	 * @param moduleCode
	 * @param code
	 * @return
	 */
	Map<String, Object> getPrinterConfig(String moduleCode, String code);

	/**
	 * 保存表格配置信息
	 * 
	 * @param moduleCode
	 * @param val
	 * @return
	 */
	boolean savePrinterConfig(String moduleCode, String code, Map<String, Object> val);

}
