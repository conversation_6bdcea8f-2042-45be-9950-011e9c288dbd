package com.yunhesoft.system.tools.components.entity.dto;

import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.Setter;

/**
 * 岗位选择
 * 
 * <AUTHOR>
 *
 */
@Getter
@Setter
@ApiModel(description = "岗位选择组件")
public class PostComDto {

	/** 机构代码（All：所有岗位） */
	private String orgCode;
	/** 岗位名称 */
	private String postName;

	/** 分页当前页数 */
	private Integer current;

	/** 分页每页记录数 */
	private Integer size;
}
