package com.yunhesoft.system.tools.dbtools;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.yunhesoft.system.kernel.service.EntityService;
import com.yunhesoft.system.kernel.service.model.TableColumnInfo;
import com.yunhesoft.system.kernel.service.model.TableInfo;

import lombok.extern.log4j.Log4j2;

/**
 * 数据库工具类
 * 
 * <AUTHOR>
 *
 */
@Log4j2
@Service
public class TenantClearSqls {
	@Autowired
	private EntityService entityService;
	private Map<String, Object> acts = new HashMap<String, Object>();

	/**
	 * 清理一个租户的所有的数据
	 * 
	 * @param tenantId
	 */
	public List<Map<String, Object[]>> deleteNotTenantAllDatas(String tenantId) {
		List<Map<String, Object[]>> sqlList = new ArrayList<Map<String, Object[]>>();
		Object[] tenantIds = { tenantId };
		// 普通表格
		addActTables();
		List<TableInfo> tables = entityService.getTableList("");
		for (TableInfo table : tables) {
			if (!acts.containsKey(table.getTableName().toUpperCase())) {
				List<TableColumnInfo> cols = entityService.getTableColumnList(table.getTableName());
				for (TableColumnInfo col : cols) {
					if (col.getColumnName().equalsIgnoreCase("TENANT_ID")) {
						Map<String, Object[]> sql = new HashMap<String, Object[]>();
						sql.put("delete from " + table.getTableName() + " where TENANT_ID<>'0' AND TENANT_ID <> ?",
								tenantIds);
						sqlList.add(sql);
						break;
					}
				}
			}
		}
		// 工作流表格
		sqlList.addAll(activitiSqls(tenantId));
		for (Map<String, Object[]> sqls : sqlList) {
			for (String key : sqls.keySet()) {
				try {
					log.error(key, sqls.get(key).toString());
					entityService.execute(key, sqls.get(key));
				} catch (Exception e) {
					log.error("删除其他租户数据错误", e);
				}
			}
		}
		System.out.println("执行清除多余租户数据动作完成!!!!");
		return sqlList;
	}

	private List<Map<String, Object[]>> activitiSqls(String tenantId) {
		List<Map<String, Object[]>> sqlList = new ArrayList<Map<String, Object[]>>();
		List<String> tenantIds = new ArrayList<String>();
		tenantIds.add(tenantId);
		// 根据act_ru_task查询非给定租户的流程实例id和任务实例id
		List<Map<String, Object>> rows = entityService
				.queryListMap("select proc_inst_id_,id_ from act_ru_task where tenant_id_ <> ?", tenantId);

		List<String> procs = new ArrayList<String>();
		List<String> tasks = new ArrayList<String>();
		for (Map<String, Object> row : rows) {
			procs.add(String.valueOf(row.get("proc_inst_id_")));
			tasks.add(String.valueOf(row.get("id_")));
		}
		// delete from act_ru_variable;
		Map<String, Object[]> sql = new HashMap<String, Object[]>();
		sql.put("delete from act_ru_variable where proc_inst_id_ in (" + getQuestMark(procs.size()) + ")",
				procs.toArray());
		sqlList.add(sql);
		// delete from act_ru_job;
		// delete from act_ru_identitylink;
		sql = new HashMap<String, Object[]>();
		sql.put("delete from act_ru_identitylink where proc_inst_id_ in (" + getQuestMark(procs.size()) + ")",
				procs.toArray());
		sqlList.add(sql);
		sql = new HashMap<String, Object[]>();
		sql.put("delete from act_ru_identitylink where task_id_ in (" + getQuestMark(tasks.size()) + ")",
				tasks.toArray());
		sqlList.add(sql);
		// delete from act_ru_suspended_job;
		// delete from act_ru_integration;
		// delete from act_ru_timer_job;
		// delete from act_ru_task;
		sql = new HashMap<String, Object[]>();
		sql.put("delete from act_ru_task where proc_inst_id_ in (" + getQuestMark(procs.size()) + ")", procs.toArray());
		sqlList.add(sql);
		// delete from act_ru_event_subscr where proc_inst_id_ is not null;
		// delete from act_ru_execution;
		sql = new HashMap<String, Object[]>();
		sql.put("delete from act_ru_execution where proc_inst_id_ in (" + getQuestMark(procs.size()) + ")",
				procs.toArray());
		sqlList.add(sql);
		//
		// delete from act_hi_detail;
		sql = new HashMap<String, Object[]>();
		sql.put("delete from act_hi_detail where proc_inst_id_ in (" + getQuestMark(procs.size()) + ")",
				procs.toArray());
		sqlList.add(sql);
		// delete from act_hi_varinst;
		sql = new HashMap<String, Object[]>();
		sql.put("delete from act_hi_varinst where proc_inst_id_ in (" + getQuestMark(procs.size()) + ")",
				procs.toArray());
		sqlList.add(sql);
		// delete from act_hi_comment;
		sql = new HashMap<String, Object[]>();
		sql.put("delete from act_hi_comment where proc_inst_id_ in (" + getQuestMark(procs.size()) + ")",
				procs.toArray());
		sqlList.add(sql);
		sql = new HashMap<String, Object[]>();
		sql.put("delete from act_hi_comment where task_id_ in (" + getQuestMark(tasks.size()) + ")", tasks.toArray());
		sqlList.add(sql);
		// delete from act_hi_actinst;
		sql = new HashMap<String, Object[]>();
		sql.put("delete from act_hi_actinst where proc_inst_id_ in (" + getQuestMark(procs.size()) + ")",
				procs.toArray());
		sqlList.add(sql);
		// delete from act_hi_identitylink;
		sql = new HashMap<String, Object[]>();
		sql.put("delete from act_hi_identitylink where proc_inst_id_ in (" + getQuestMark(procs.size()) + ")",
				procs.toArray());
		sqlList.add(sql);
		sql = new HashMap<String, Object[]>();
		sql.put("delete from act_hi_identitylink where task_id_ in (" + getQuestMark(tasks.size()) + ")",
				tasks.toArray());
		sqlList.add(sql);
		// delete from act_hi_taskinst;
		sql = new HashMap<String, Object[]>();
		sql.put("delete from act_hi_taskinst where proc_inst_id_ in (" + getQuestMark(procs.size()) + ")",
				procs.toArray());
		sqlList.add(sql);
		// delete from act_hi_attachment;
		// delete from act_hi_procinst;
		sql = new HashMap<String, Object[]>();
		sql.put("delete from act_hi_procinst where proc_inst_id_ in (" + getQuestMark(procs.size()) + ")",
				procs.toArray());
		sqlList.add(sql);

		List<String> deployIds = new ArrayList<String>();
		rows = entityService.queryListMap("select id_ from act_re_deployment where tenant_id_ <> ?", tenantId);
		for (Map<String, Object> row : rows) {
			deployIds.add(String.valueOf(row.get("id_")));
		}
		// act_re_procdef
		sql = new HashMap<String, Object[]>();
		sql.put("delete from act_re_procdef where tenant_id_ <> ?", tenantIds.toArray());
		sqlList.add(sql);
		// act_ge_bytearray
		sql = new HashMap<String, Object[]>();
		sql.put("delete from act_ge_bytearray where DEPLOYMENT_ID_ in (" + getQuestMark(deployIds.size()) + ")",
				deployIds.toArray());
		sqlList.add(sql);
		// act_re_deployment
		sql = new HashMap<String, Object[]>();
		sql.put("delete from act_re_deployment where tenant_id_ <> ?", tenantIds.toArray());
		sqlList.add(sql);
		return sqlList;
	}

	private String getQuestMark(int size) {
		String qm = "";
		for (int i = 0; i < size; i++) {
			if (i == 0) {
				qm += "?";
			} else {
				qm += ",?";
			}
		}
		return qm;
	}

	private void addActTables() {
		acts.put("act_ge_bytearray".toUpperCase(), null);
		acts.put("act_ge_property".toUpperCase(), null);
		acts.put("act_hi_actinst".toUpperCase(), null);
		acts.put("act_hi_attachment".toUpperCase(), null);
		acts.put("act_hi_comment".toUpperCase(), null);
		acts.put("act_hi_detail".toUpperCase(), null);
		acts.put("act_hi_identitylink".toUpperCase(), null);
		acts.put("act_hi_procinst".toUpperCase(), null);
		acts.put("act_hi_taskinst".toUpperCase(), null);
		acts.put("act_hi_varinst".toUpperCase(), null);
		acts.put("act_re_deployment".toUpperCase(), null);
		acts.put("act_re_deployment_bak".toUpperCase(), null);
		acts.put("act_re_model".toUpperCase(), null);
		acts.put("act_re_procdef".toUpperCase(), null);
		acts.put("act_re_procdef_bak".toUpperCase(), null);
		acts.put("act_ru_deadletter_job".toUpperCase(), null);
		acts.put("act_ru_event_subscr".toUpperCase(), null);
		acts.put("act_ru_execution".toUpperCase(), null);
		acts.put("act_ru_identitylink".toUpperCase(), null);
		acts.put("act_ru_integration".toUpperCase(), null);
		acts.put("act_ru_job".toUpperCase(), null);
		acts.put("act_ru_suspended_job".toUpperCase(), null);
		acts.put("act_ru_task".toUpperCase(), null);
		acts.put("act_ru_timer_job".toUpperCase(), null);
		acts.put("act_ru_variable".toUpperCase(), null);

		acts.put("SYS_MENU", null);
		acts.put("SYS_MENU_LIB", null);
		acts.put("SYS_MENU_CLASS", null);
		acts.put("SYS_MULTI_TENANT", null);
	}
}
