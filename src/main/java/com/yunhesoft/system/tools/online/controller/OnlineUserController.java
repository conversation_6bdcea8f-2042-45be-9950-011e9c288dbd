package com.yunhesoft.system.tools.online.controller;

import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.yunhesoft.core.common.model.Res;
import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.system.kernel.config.SysUserInterceptor;
import com.yunhesoft.system.kernel.controller.BaseRestController;
import com.yunhesoft.system.tools.online.entity.dto.QueryOnlineUserDto;
import com.yunhesoft.system.tools.online.service.IOnlineUserService;
import com.yunhesoft.tmtools.JwtUser;
import com.yunhesoft.tmtools.TokenUtils;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * 在线用户服务
 * 
 * <AUTHOR>
 * @date 2021/09/25 12:36
 */
@RestController
@RequestMapping("/userOnline")
@Api(tags = "在线用户服务")
public class OnlineUserController extends BaseRestController {

	@Autowired
	private IOnlineUserService srv;

	/**
	 * 获取在线用户列表
	 */
	@RequestMapping(value = "/getOnlineUserList", method = RequestMethod.POST)
	@ApiOperation(value = "获取在线用户列表")
	public Res<?> getOnlineUserList(@RequestBody QueryOnlineUserDto param) {
		Res<List<Map<String, String>>> resp = new Res<List<Map<String, String>>>();
		resp.setResult(srv.getOnlineUserList(param));
		resp.setTotal(param.getRecordCount());
		return resp;
	}

	/**
	 * 用户在线心跳激活
	 * 
	 * @return
	 */
	@RequestMapping(value = "/userActive", method = { RequestMethod.POST, RequestMethod.GET })
	@ApiOperation(value = "用户在线心跳激活")
	public Res<?> userActive() {
		boolean bln = false;
		if (request != null) {
			String token = SysUserInterceptor.getTokenString(request);
			// System.out.println("token:" + token);
			if (StringUtils.isNotEmpty(token)) {
				JwtUser juser = TokenUtils.getJwtUser(token);
				if (juser != null && juser.isSuccess()) {
					bln = srv.userActive(request, juser);
				}
			}
		}
		return Res.OK(bln);
	}

}