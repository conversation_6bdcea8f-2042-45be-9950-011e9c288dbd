package com.yunhesoft.system.tools.redisManager.controller;

import com.alibaba.fastjson2.JSONObject;
import com.yunhesoft.core.common.model.Res;
import com.yunhesoft.core.common.utils.PassUtils;
import com.yunhesoft.system.auth.service.AuthService;
import com.yunhesoft.system.tools.redisManager.service.IRMService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;

@Api(tags = "redis管理")
@RestController
@RequestMapping("/tool/redismanager")
@Log4j2
public class RMController {

	@Autowired
	private IRMService redisManager;

	@Autowired
	private AuthService authService;

	@ApiOperation(value = "获取redis-树")
	@RequestMapping(value = "/getTreeData", method = RequestMethod.POST)
	public Res<?> getTreeData(@RequestParam("dbindex") Integer dbindex, @RequestParam("keys") String keys) {
		return Res.OK(redisManager.getTreeData(dbindex, keys));
	}

	@ApiOperation(value = "获取数据库列表")
	@RequestMapping(value = "/getDBList", method = RequestMethod.POST)
	public Res<?> getDBList() {
		return Res.OK(redisManager.getDBList());
	}

	@ApiOperation(value = "清空数据库")
	@RequestMapping(value = "/flushDb", method = RequestMethod.POST)
	public Res<?> flushDb(@RequestParam("dbindex") Integer dbindex) {
		try {
			redisManager.flushDb(dbindex);
			return Res.OK(true);
		} catch (Exception e) {
			log.error("", e);
			return Res.FAIL(e.getMessage());
		}
	}

	@ApiOperation(value = "获取Key值数据")
	@RequestMapping(value = "/getKeyData", method = RequestMethod.POST)
	public Res<?> getDate(@RequestParam("dbindex") Integer dbindex, @RequestParam("key") String key) {
		try {
			return Res.OK(redisManager.getKeyData(dbindex, key));
		} catch (Exception e) {
			log.error("", e);
			return Res.FAIL(e.getMessage());
		}
	}

	@ApiOperation(value = "删除数据")
	@RequestMapping(value = "/deleteKeyData", method = RequestMethod.POST)
	public Res<?> deleteDate(@RequestParam("dbindex") Integer dbindex, @RequestParam("key") String key,
			@RequestParam("isKey") Boolean isKey) {
		try {
			return Res.OK(redisManager.deleteKey(dbindex, key, isKey));
		} catch (Exception e) {
			log.error("", e);
			return Res.FAIL(e.getMessage());
		}
	}

	@ApiOperation(value = "密码校验")
	@RequestMapping(value = "/checkPass", method = RequestMethod.POST)
	public Res<?> checkPass(@RequestParam("pass") String pass) {
		try {
			String password = authService.RSADecryptPassword(pass);// 解密
			return Res.OK(redisManager.checkPass(password));
		} catch (Exception e) {
			log.error("", e);
			return Res.OK(false);
		}
	}

	@ApiOperation(value = "密码校验")
	@RequestMapping(value = "/checkRedisPassword", method = RequestMethod.POST)
	public Res<?> checkRedisPassword(@RequestParam("pass") String pass) {
		try {
			String password = authService.RSADecryptPassword(pass);// 解密
			boolean checkResult = redisManager.checkPass(password);
			JSONObject result = new JSONObject();
			result.put("result", checkResult);
			result.put("dt", new Date().getTime());
//			String s = PassUtils.RSAEncryptPassword(result.toString());
			String s = PassUtils.encryptByPublicKey(result.toString());
//			System.out.println("bdyh加密结果：");
//			String s = PassUtils.RSAEncryptPassword(Base64.encode("bdyh".getBytes(StandardCharsets.UTF_8)));
//			System.out.println(s);
//			System.out.println(PassUtils.RSADecryptPassword(s));
//			String r = Base64.encode(result.toString().getBytes(StandardCharsets.UTF_8));
			return Res.OK(s);
		} catch (Exception e) {
			log.error("", e);
			return Res.OK(false);
		}
	}

	@ApiOperation(value = "获取当前时间戳")
	@RequestMapping(value = "/getNowTimestamp", method = RequestMethod.GET)
	public Res<?> getNowTimestamp() {
		return Res.OK(new Date().getTime());
	}

}
