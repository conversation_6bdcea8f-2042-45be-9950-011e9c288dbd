package com.yunhesoft.system.tools.files.controller;

import com.yunhesoft.core.common.model.Res;
import com.yunhesoft.system.kernel.controller.BaseRestController;
import com.yunhesoft.system.kernel.service.model.Pagination;
import com.yunhesoft.system.tools.files.entity.dto.FileManageQueryDto;
import com.yunhesoft.system.tools.files.entity.po.SysFileManage;
import com.yunhesoft.system.tools.files.service.ISysFileOnlineManage;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 文件管理Controller
 *
 */
@Log4j2
@RestController
@Api(tags = "在线文件管理")
@RequestMapping("/system/fileOnlineManage")
public class FileOnlineManageController extends BaseRestController {
	@Autowired
	private ISysFileOnlineManage srv;
	
	@ApiOperation(value = "查询数据列表")
	@RequestMapping(value = "/queryDataList", method = { RequestMethod.POST })
	public Res queryDataList(@RequestBody FileManageQueryDto dto) {
		Pagination<?> page = getRequestPagination();
		return Res.OK(srv.queryDataList(dto));
	}


	@ApiOperation(value = "保存文件夹")
	@RequestMapping(value = "/saveFolder", method = { RequestMethod.POST })
	public Res saveFolder(@RequestBody SysFileManage folder) {
		return Res.OK(srv.saveFolder(folder));
	}


	@ApiOperation(value = "批量删除")
	@RequestMapping(value = "/deleteDataByIdList", method = { RequestMethod.POST })
	public Res deleteDataByIdList(@RequestBody List<String> idList) {
		return Res.OK(srv.deleteDataByIdList(idList));
	}

	@ApiOperation(value = "彻底删除")
	@RequestMapping(value = "/realDeleteFileByIdList", method = { RequestMethod.POST })
	public Res realDeleteFileByIdList(@RequestBody List<String> idList) {
		return Res.OK(srv.realDeleteFileByIdList(idList));
	}

	@ApiOperation(value = "文件上传（包括在线文档处理）")
	@RequestMapping(value = "/fileUpload", method = { RequestMethod.POST })
	public Res<?> fileUpload(@RequestParam("pid") String pid, @RequestParam("dataClass") String dataClass, @RequestParam("moduleCode") String moduleCode, 
			 @RequestParam("fileMode") String fileMode, @RequestParam("fileAuditFlow") String fileAuditFlow, @RequestParam("addFileMode") String addFileMode, 
			 @RequestParam("level1") String level1, @RequestParam("applyType") String applyType, @RequestParam("orgCode") String orgCode, @RequestParam("file") MultipartFile file) {
		return Res.OK(srv.fileUpload(pid, dataClass, moduleCode,fileMode,fileAuditFlow,addFileMode, level1, applyType, orgCode, file));
	}

    @ApiOperation(value = "下载文件")
    @RequestMapping("/fileDownload")
    public void fileDownload(HttpServletResponse response, @RequestBody FileManageQueryDto dto) {
    	srv.fileDownload(response, dto);

    }

	@ApiOperation(value = "批量还原")
	@RequestMapping(value = "/restoreFiles", method = { RequestMethod.POST })
	public Res restoreFiles(@RequestBody List<String> idList) {
		return Res.OK(srv.restoreFiles(idList));
	}

	@ApiOperation(value = "文件是否存在")
	@RequestMapping(value = "/fileExists", method = { RequestMethod.POST })
	public Res fileExists(@RequestBody FileManageQueryDto dto) {
		return Res.OK(srv.fileExists(dto));
	}

	@ApiOperation(value = "文件预览")
	@RequestMapping(value = "/getFileBase64", method = { RequestMethod.POST })
	public Res getFileBase64(@RequestBody SysFileManage file) {
		return Res.OK(srv.getFileBase64(file));
	}
	
	
	/**新增方法******************************************************************************************************************************************************************************/
	
	/*--文档列表信息操作-----------------------------------------------------------*/
	@ApiOperation(value = "查询文档数据列表(替代原文档管理中查询)")
	@RequestMapping(value = "/queryOnlineDataList", method = { RequestMethod.POST })
	public Res queryOnlineDataList(@RequestBody FileManageQueryDto dto) {
		Pagination<?> page = getRequestPagination();
		return Res.OK(srv.queryOnlineDataList(dto));
	}
	@ApiOperation(value = "查询文档树)")
	@RequestMapping(value = "/getOnlineFileTree", method = { RequestMethod.POST })
	public Res getOnlineFileTree(@RequestBody FileManageQueryDto dto) {
		Pagination<?> page = getRequestPagination();
		return Res.OK(srv.getOnlineFileTree(dto));
	}
	
	@ApiOperation(value = "添加空在线文档")
	@RequestMapping(value = "/addNullOnlineFile", method = { RequestMethod.POST })
	public Res addNullOnlineFile(@RequestBody FileManageQueryDto dto) {
		return Res.OK(srv.addNullOnlineFile(dto));
	}
	
	@ApiOperation(value = "获取个人数据列表（待发布、待审核、已发布）")
	@RequestMapping(value = "/queryUserFileList", method = { RequestMethod.POST })
	public Res queryUserFileList(@RequestBody FileManageQueryDto dto) {
//		Pagination<?> page = getRequestPagination();
		Pagination<?> page = null;
		if (dto.getPageSize() != null && dto.getPageSize() > 0) {
			page = Pagination.create(dto.getPageNum() == null ? 1 : dto.getPageNum(), dto.getPageSize());
		}
		dto.setPage(page);
		return Res.OK(srv.queryFileList(dto));
	}
	@ApiOperation(value = "在线文档发布")
	@RequestMapping(value = "/publishOnlineFile", method = { RequestMethod.POST })
	public Res publishOnlineFile(@RequestBody FileManageQueryDto dto) {
		return Res.OK(srv.publishOnlineFile(dto));
	}
    
    @ApiOperation(value = "在线文档删除")
	@RequestMapping(value = "/deleteOnlineFile", method = { RequestMethod.POST })
	public Res deleteOnlineFile(@RequestBody FileManageQueryDto dto) {
		return Res.OK(srv.deleteOnlineFile(dto));
	}
    
    @ApiOperation(value = "在线文档审核")
	@RequestMapping(value = "/auditOnlieFile", method = { RequestMethod.POST })
	public Res auditOnlieFile(@RequestBody FileManageQueryDto dto) {
		return Res.OK(srv.auditOnlieFile(dto));
	}
    @ApiOperation(value = "下载在线文件")
    @RequestMapping("/onlieFileDownload")
    public void onlieFileDownload(HttpServletResponse response, @RequestBody FileManageQueryDto dto) {
    	srv.onlineFileDownload(response, dto);
    }
    
    @ApiOperation(value = "获取修改日志")
	@RequestMapping(value = "/getUpdLog", method = { RequestMethod.POST })
	public Res getUpdLog(@RequestBody FileManageQueryDto dto) {
		Pagination<?> page = getRequestPagination();
		return Res.OK(srv.getUpdLog(dto));
	}
	
	@ApiOperation(value = "获取版本日志")
	@RequestMapping(value = "/getVerLog", method = { RequestMethod.POST })
	public Res getVerLog(@RequestBody FileManageQueryDto dto) {
		Pagination<?> page = getRequestPagination();
		return Res.OK(srv.getVerLog(dto));
	}
	
	@ApiOperation(value = "更换文件管理对象")
	@RequestMapping(value = "/changeFileManager", method = { RequestMethod.POST })
	public Res changeFileManager(@RequestBody FileManageQueryDto dto) {
		return Res.OK(srv.changeFileManager(dto));
	}
    
    /*--文档内容信息操作-----------------------------------------------------------*/
    @ApiOperation(value = "获取文件树形数据")
	@RequestMapping(value = "/getTreeData", method = { RequestMethod.POST })
	public Res getTreeData(@RequestBody FileManageQueryDto dto) {
		return Res.OK(srv.getFileTree(dto));
	}
    @ApiOperation(value = "保存节点（添加、修改、删除）")
	@RequestMapping(value = "/saveNode", method = { RequestMethod.POST })
	public Res saveNode(@RequestBody FileManageQueryDto dto) {
		return Res.OK(srv.saveNode(dto));
	}

    @ApiOperation(value = "获取节点内容")
	@RequestMapping(value = "/getNodeData", method = { RequestMethod.POST })
	public Res getNodeData(@RequestBody FileManageQueryDto dto) {
		return Res.OK(srv.getNodeData(dto));
	}
    
    @ApiOperation(value = "保存节点内容")
	@RequestMapping(value = "/saveNodeData", method = { RequestMethod.POST })
	public Res saveNodeData(@RequestBody FileManageQueryDto dto) {
		return Res.OK(srv.saveNodeData(dto));
	}
    
    @ApiOperation(value = "获取节点对比前后内容")
	@RequestMapping(value = "/getDiffNodeData", method = { RequestMethod.POST })
	public Res getDiffNodeData(@RequestBody FileManageQueryDto dto) {
		return Res.OK(srv.getDiffNodeData(dto));
	}
    
    @ApiOperation(value = "根据发布ID获取文件ID和版本号")
    @RequestMapping(value = "/getFileIdVersion", method = { RequestMethod.POST })
    public Res getFileIdVersion(@RequestBody FileManageQueryDto dto) {
    	return Res.OK(srv.getFileIdVersion(dto));
    }
    
    @ApiOperation(value = "移动大纲节点")
    @RequestMapping(value = "/moveTreeNode", method = { RequestMethod.POST })
    public Res moveTreeNode(@RequestBody FileManageQueryDto dto) {
    	return Res.OK(srv.moveTreeNode(dto));
    }
    
    
//	@ApiOperation(value = "获取待发布列表")
//	@RequestMapping(value = "/getToPublishList", method = { RequestMethod.POST })
//	public Res getToPublishList(@RequestBody FileManageQueryDto dto) {
//		Pagination<?> page = getRequestPagination();
//		return Res.OK(srv.queryFileList(dto));
//	}
//	
//	@ApiOperation(value = "获取待审核列表")
//	@RequestMapping(value = "/getToAuditList", method = { RequestMethod.POST })
//	public Res getToAuditList(@RequestBody FileManageQueryDto dto) {
//		Pagination<?> page = getRequestPagination();
//		return Res.OK(srv.queryFileList(dto));
//	}
//	
//	@ApiOperation(value = "获取已发布列表")
//	@RequestMapping(value = "/getPublishedList", method = { RequestMethod.POST })
//	public Res getPublishedList(@RequestBody FileManageQueryDto dto) {
//		Pagination<?> page = getRequestPagination();
//		return Res.OK(srv.queryFileList(dto));
//	}
	
}
