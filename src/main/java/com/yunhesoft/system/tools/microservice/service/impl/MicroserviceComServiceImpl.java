package com.yunhesoft.system.tools.microservice.service.impl;

import com.yunhesoft.core.common.model.Res;
import com.yunhesoft.system.kernel.config.SysUserHolder;
import com.yunhesoft.system.tools.microservice.service.IMicroserviceComService;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;

@Service
public class MicroserviceComServiceImpl implements IMicroserviceComService {
    @Resource(name = "CustomRestTemplate")
    private RestTemplate restTemplate;

    /**
     * 与其它微服务POST通信交换数据
     * @param serviceName 微服务名称
     * @param requestPath 请求路径
     * @param dto 请求对象
     * @return
     * @param <T> 返回对象类型
     * @param <E> 请求对象类型
     */
    public <T, E> T postExchange(String serviceName, String requestPath, E dto) {
        T res = null;

        String url = "http://" + serviceName + "/" + requestPath;

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        String currentToken = SysUserHolder.getCurrentToken();
        headers.set("Authorization", currentToken);
        HttpEntity<Object> entity = new HttpEntity<>(dto, headers);
        ParameterizedTypeReference<Res<T>> responseType = new ParameterizedTypeReference<Res<T>>() {
        };
        Res<T> sysRes = restTemplate.exchange(url, HttpMethod.POST, entity, responseType).getBody();

        if (sysRes != null) {
            res = sysRes.getResult();
        }

        return res;
    }
}
