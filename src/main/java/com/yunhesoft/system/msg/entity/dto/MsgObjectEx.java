package com.yunhesoft.system.msg.entity.dto;

import java.util.ArrayList;
import java.util.List;

import com.yunhesoft.system.employee.entity.po.SysEmployeeInfo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * 发消息 - 对象(扩展-返回使用)
 * <AUTHOR>
 * 2021.12.07
 */
@Getter
@Setter
@ApiModel(description = "发消息 - 对象(扩展-返回使用)")
public class MsgObjectEx extends MsgObject {
	
	@ApiModelProperty(value="成功标志(有一个消息发送成功，就算成功)")
	private boolean success = false;
	
	@ApiModelProperty(value="错误信息")
	private String errInfo;
	
	@ApiModelProperty(value="消息发送人姓名")
	private String sendEmpName;
	
	@ApiModelProperty(value="消息发送人手机号")
	private String sendEmpPhone;
	
	@ApiModelProperty(value="消息接收人信息列表(有效人员-系统内查询到信息的人员)")
	private List<SysEmployeeInfo> recvEmpInfoList = new ArrayList<SysEmployeeInfo>();
	
	@ApiModelProperty(value="系统中未找到信息的，人员ID列表")
	private List<String> sysNotEmpList = new ArrayList<String>();
	
}
