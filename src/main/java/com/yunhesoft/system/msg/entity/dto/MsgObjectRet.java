package com.yunhesoft.system.msg.entity.dto;

import java.util.ArrayList;
import java.util.List;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 发消息 - 返回值对象
 * 
 * <AUTHOR> 2021.12.07
 */
@Data
@ApiModel(description = "发消息-返回值对象")
public class MsgObjectRet {

	@ApiModelProperty(value = "成功标志(有一个消息发送成功，就算成功)")
	private boolean success = false;

	@ApiModelProperty(value = "成功消息列表")
	private List<MsgObjectEx> succMsgList = new ArrayList<MsgObjectEx>();

	@ApiModelProperty(value = "失败消息列表")
	private List<MsgObjectEx> failMsgList = new ArrayList<MsgObjectEx>();

}
