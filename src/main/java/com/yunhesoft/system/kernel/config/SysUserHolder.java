package com.yunhesoft.system.kernel.config;

import com.yunhesoft.core.common.model.SysUser;
import com.yunhesoft.tmtools.JwtUser;

/**
 * 线程用户持有者
 * 
 * <AUTHOR>
 *
 * @since 2021-5-19 8:28:18
 */
public class SysUserHolder {

	/** 保存用户对象的ThreadLocal 在拦截器操作 添加、删除相关用户数据 */
	private static final ThreadLocal<SysUser> SYS_USER_THREADLOCAL = new ThreadLocal<SysUser>();
	private static final ThreadLocal<JwtUser> JWT_USER_THREADLOCAL = new ThreadLocal<JwtUser>();
	private static final ThreadLocal<String> TOKEN_THREADLOCAL = new ThreadLocal<String>();// token

	/**
	 * 添加当前登录用户方法 在拦截器方法执行前调用设置获取用户
	 * 
	 * @param user
	 */
	public static void setCurrentUser(SysUser user) {
		SYS_USER_THREADLOCAL.set(user);
	}

	/**
	 * 获取当前登录用户方法
	 */
	public static SysUser getCurrentUser() {
		return SYS_USER_THREADLOCAL.get();
	}

	/**
	 * 添加当前登录用户方法 在拦截器方法执行前调用设置获取用户
	 * 
	 * @param juser
	 */
	public static void setCurrentJwtUser(JwtUser juser) {
		JWT_USER_THREADLOCAL.set(juser);
	}

	/**
	 * 获取当前登录用户方法
	 */
	public static JwtUser getCurrentJwtUser() {
		return JWT_USER_THREADLOCAL.get();
	}

	/**
	 * 添加当前登录用户token
	 * 
	 * @param juser
	 */
	public static void setCurrentToken(String token) {
		TOKEN_THREADLOCAL.set(token);
	}

	/**
	 * 获取当前登录用户token
	 */
	public static String getCurrentToken() {
		return TOKEN_THREADLOCAL.get();
	}

	/**
	 * 删除当前登录用户方法 在拦截器方法执行后 移除当前用户对象
	 */
	public static void remove() {
		SYS_USER_THREADLOCAL.remove();
		JWT_USER_THREADLOCAL.remove();
		TOKEN_THREADLOCAL.remove();
	}
}
