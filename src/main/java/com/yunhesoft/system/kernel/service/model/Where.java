package com.yunhesoft.system.kernel.service.model;

import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.List;

import com.yunhesoft.core.common.utils.EntityUtils;
import com.yunhesoft.core.utils.ObjUtilsReflection;
import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.core.utils.spring.SpringUtils;
import com.yunhesoft.system.kernel.service.AEntityService;
import com.yunhesoft.system.kernel.service.EntityService;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.log4j.Log4j2;

/**
 * @category where查询条件对象
 * <AUTHOR>
 *
 */
@Log4j2
@Data
@EqualsAndHashCode(callSuper = false)
public class Where extends ASQLObject {
	/** 作用于的表格名称 */
	private String table;
	/** 实体对象 */
	private Object entity;
	/** wher的表达式列表 */
	private List<WhereExpr> exprs = new ArrayList<WhereExpr>();

	public static Where create() {
		return new Where();
	}

	public static Where create(String table) {
		return new Where(table);
	}

	public static Where create(Class<?> entityClass) {
		return new Where(EntityUtils.tableName(entityClass));
	}

	public static Where create(Object entity) {
		Where where = new Where();
		where.setEntity(entity);
		where.setTable(EntityUtils.tableName(entity.getClass()));
		return where;
	}

	public static Where create(String exp, Object... args) {
		return new Where().and(exp, args);
	}

	public Where() {
	}

	public Where(String table) {
		this.table = table;
	}

	/**
	 * @category 左括号
	 * 
	 *           注：左括号必须搭配右括号，成对使用
	 * 
	 * @return
	 */
	public Where lb() {
		return add("", "(");
	}

	/**
	 * @category 右括号
	 * @return
	 */
	public Where rb() {
		return add("", ")");
	}

	/**
	 * @category 并且 and
	 * @return
	 */
	public Where and() {
		if (exprs.size() > 0) {
			exprs.add(new WhereExpr().and());
		}
		return this;
	}

	/**
	 * @category 或者 or
	 * @return
	 */
	public Where or() {
		if (exprs.size() > 0) {
			exprs.add(new WhereExpr().or());
		}
		return this;
	}

	/**
	 * @category 增加表达式
	 * @param left
	 * @param oper
	 * @param right
	 * @return
	 */
	private Where add(String column, String oper, Object... args) {
		Column left = new Column(table, column);
		if ("and".equalsIgnoreCase(oper) && exprs.size() > 0) {
			exprs.add(new WhereExpr(table, null, oper));
		} else if ("or".equalsIgnoreCase(oper) && exprs.size() > 0) {
			exprs.add(new WhereExpr(table, null, oper));
		} else {
			exprs.add(new WhereExpr(table, left, oper, args));
		}
		return this;
	}

	private Where add(Column column, String oper, Object... args) {
		if ("and".equalsIgnoreCase(oper) && exprs.size() > 0) {
			exprs.add(new WhereExpr(table, null, oper));
		} else if ("or".equalsIgnoreCase(oper) && exprs.size() > 0) {
			exprs.add(new WhereExpr(table, null, oper));
		} else {
			exprs.add(new WhereExpr(table, column, oper, args));
		}
		return this;
	}

	private <T> Where add(Func<T, ?> func, String oper, Object... args) {
		if ("and".equalsIgnoreCase(oper) && exprs.size() > 0) {
			exprs.add(new WhereExpr(table, null, oper));
		} else if ("or".equalsIgnoreCase(oper) && exprs.size() > 0) {
			exprs.add(new WhereExpr(table, null, oper));
		} else {
			EntityLambdaUtils utils = new EntityLambdaUtils(func);
			exprs.add(new WhereExpr(table, Column.create(table, utils.columnName()), oper, args));
		}
		return this;
	}

	/**
	 * @category 等于
	 * 
	 * @param <T>
	 * @param fieldFunc 字段get函数名
	 * @param args      参数
	 * @return
	 */
	public <T> Where eq(Func<T, ?> fieldFunc, Object... args) {
		return add(fieldFunc, "=", args);
	}

	public <T> Where eq(Column column, Object... args) {
		return add(column, "=", args);
	}

	/**
	 * @category 等于
	 * @param <T>
	 * @param columnName 表格列名
	 * @param args       参数
	 * @return
	 */
	public <T> Where eq(String columnName, Object... args) {
		return add(columnName, "=", args);
	}

	@Deprecated
	public Where and(String exp, Object... args) {
		if (this.exprs.size() > 0) {
			add("", "and");
		}
		add(exp, "", args);
		return this;
	}

	@Deprecated
	public Where or(String exp, Object... args) {
		if (this.exprs.size() > 0) {
			add("", "or");
		}
		add(exp, "", args);
		return this;
	}

	/**
	 * @category 小于
	 * 
	 * @param <T>
	 * @param fieldFunc
	 * @param args
	 * @return
	 */
	public <T> Where lt(Func<T, ?> fieldFunc, Object... args) {
		return add(resolveColumnName(fieldFunc), "<", args);
	}

	public <T> Where lt(Column column, Object... args) {
		return add(column, "<", args);
	}

	/**
	 * @category 小于
	 * 
	 * @param <T>
	 * @param columnName 表格列名
	 * @param args
	 * @return
	 */
	public <T> Where lt(String columnName, Object... args) {
		return add(columnName, "<", args);
	}

	/**
	 * @category 小于等于
	 * 
	 * @param <T>
	 * @param fieldFunc
	 * @param args
	 * @return
	 */
	public <T> Where le(Func<T, ?> fieldFunc, Object... args) {
		return add(resolveColumnName(fieldFunc), "<=", args);
	}

	/**
	 * @category 小于等于
	 * 
	 * @param <T>
	 * @param columnName 表格列名
	 * @param args
	 * @return
	 */
	public <T> Where le(String columnName, Object... args) {
		return add(columnName, "<=", args);
	}

	public <T> Where le(Column column, Object... args) {
		return add(column, "<=", args);
	}

	/**
	 * @category 大于
	 * 
	 * @param <T>
	 * @param fieldFunc
	 * @param args
	 * @return
	 */
	public <T> Where gt(Func<T, ?> fieldFunc, Object... args) {
		return add(resolveColumnName(fieldFunc), ">", args);
	}

	/**
	 * @category 大于
	 * @param <T>
	 * @param columnName
	 * @param args
	 * @return
	 */
	public <T> Where gt(String columnName, Object... args) {
		return add(columnName, ">", args);
	}

	public <T> Where gt(Column column, Object... args) {
		return add(column, ">", args);
	}

	/**
	 * @category 大于等于
	 * 
	 * @param <T>
	 * @param fieldFunc
	 * @param args
	 * @return
	 */
	public <T> Where ge(Func<T, ?> fieldFunc, Object... args) {
		return add(resolveColumnName(fieldFunc), ">=", args);
	}

	/**
	 * @category 大于等于
	 * 
	 * @param <T>
	 * @param columnName
	 * @param args
	 * @return
	 */
	public <T> Where ge(String columnName, Object... args) {
		return add(columnName, ">=", args);
	}

	public <T> Where ge(Column column, Object... args) {
		return add(column, ">=", args);
	}

	/**
	 * @category 不等于
	 * 
	 * @param <T>
	 * @param fieldFunc
	 * @param args
	 * @return
	 */
	public <T> Where ne(Func<T, ?> fieldFunc, Object... args) {
		return add(resolveColumnName(fieldFunc), "<>", args);
	}

	/**
	 * @category 不等于
	 * 
	 * @param <T>
	 * @param columnName
	 * @param args
	 * @return
	 */
	public <T> Where ne(String columnName, Object... args) {
		return add(columnName, "<>", args);
	}

	public <T> Where ne(Column column, Object... args) {
		return add(column, "<>", args);
	}

	/**
	 * @category 包含在
	 * @param columnName
	 * @param args
	 * @return
	 */
	public <T> Where in(Func<T, ?> columnName, Object... args) {
		if (args != null && args.length == 1) {
			return add(resolveColumnName(columnName), "=", args);
		} else {
			return add(resolveColumnName(columnName), "in", args);
		}
	}

	/**
	 * @category 包含在
	 * @param columnName
	 * @param args
	 * @return
	 */
	public Where in(String columnName, Object... args) {
		if (args != null && args.length == 1) {
			return add(columnName, "=", args);
		} else {
			return add(columnName, "in", args);
		}

	}

	public Where in(Column column, Object... args) {
		if (args != null && args.length == 1) {
			return add(column, "=", args);
		} else {
			return add(column, "in", args);
		}
	}

	/**
	 * @category 不包含在
	 * @param columnName
	 * @param args
	 * @return
	 */
	public <T> Where notIn(Func<T, ?> columnName, Object... args) {
		return add(resolveColumnName(columnName), "not in", args);
	}

	/**
	 * @category 不包含在
	 * @param columnName
	 * @param args
	 * @return
	 */
	public Where notIn(String columnName, Object... args) {
		return add(columnName, "not in", args);
	}

	public Where notIn(Column column, Object... args) {
		return add(column, "not in", args);
	}

	/**
	 * @category 包含在
	 * @param columnName
	 * @param args
	 * @return
	 */
	@Deprecated
	public <T> Where andIns(Func<T, ?> columnName, Object... args) {
		if (this.exprs.size() > 0) {
			this.and();
		}
		if (args != null && args.length == 1) {
			return add(resolveColumnName(columnName), "=", args);
		} else {
			return add(resolveColumnName(columnName), "in", args);
		}
	}

	/**
	 * @category 包含在
	 * @param columnName
	 * @param args
	 * @return
	 */
	@Deprecated
	public Where andIns(String columnName, Object... args) {
		if (this.exprs.size() > 0) {
			this.and();
		}
		if (args != null && args.length == 1) {
			return add(columnName, "=", args);
		} else {
			return add(columnName, "in", args);
		}
	}

	/**
	 * 数据库字段添加字符串
	 * 
	 * @param <T>
	 * @param fieldFunc
	 * @param addLeftString  左边字符串
	 * @param addRightString 右边字符串
	 * @return
	 */
	public <T> String addConcat(Func<T, ?> fieldFunc, String addLeftString, String addRightString) {

		String colname = resolveColumnName(fieldFunc);
		String dbtype = SpringUtils.getBean(AEntityService.class).getDatabaseType();
		if (dbtype.equalsIgnoreCase(EntityService.DBTYPE.SQLSERVER.toString())) {// sqlserver
			if (StringUtils.isNotEmpty(addLeftString)) {// 字段左边添加字符
				colname = "'" + addLeftString + "' + " + colname;
			}
			if (StringUtils.isNotEmpty(addRightString)) {// 字段右边添加字符
				colname = colname + " + '" + addRightString + "'";
			}
		} else if (dbtype.equalsIgnoreCase(EntityService.DBTYPE.MYSQL.toString())) {// mysql
			if (StringUtils.isNotEmpty(addLeftString) && StringUtils.isNotEmpty(addRightString)) {// 字段左边添加字符
				colname = "CONCAT('" + addLeftString + "'," + colname + ",'" + addRightString + "')";
			} else if (StringUtils.isNotEmpty(addLeftString)) {// 字段左边边添加字符
				colname = "CONCAT('" + addLeftString + "'," + colname + ")";
			} else if (StringUtils.isNotEmpty(addRightString)) {
				colname = "CONCAT(" + colname + ",'" + addRightString + "')";
			}
		} else {// oracle or pg
			if (StringUtils.isNotEmpty(addLeftString)) {// 字段左边添加字符
				colname = "'" + addLeftString + "' || " + colname;
			}
			if (StringUtils.isNotEmpty(addRightString)) {// 字段右边添加字符
				colname = colname + " || '" + addRightString + "'";
			}
		}
		return colname;
	}

	/**
	 * 字段右边添加字符
	 * 
	 * @param <T>
	 * @param fieldFunc
	 * @param addRightStr
	 * @return
	 */
	public <T> String addRight(Func<T, ?> fieldFunc, String addRightStr) {
		return addConcat(fieldFunc, "", addRightStr);
	}

	/**
	 * 字段左边添加字符
	 * 
	 * @param <T>
	 * @param fieldFunc
	 * @param addLeftString
	 * @return
	 */
	public <T> String addLeft(Func<T, ?> fieldFunc, String addLeftString) {
		return addConcat(fieldFunc, addLeftString, "");

	}

	public <T> Where like(Func<T, ?> fieldFunc, Object... args) {
		return add(resolveColumnName(fieldFunc), "like", "%" + args[0] + "%");
	}

	public <T> Where like(String columnName, Object... args) {
		return add(columnName, "like", "%" + args[0] + "%");
	}

	public <T> Where like(Column column, Object... args) {
		return add(column, "like", "%" + args[0] + "%");
	}

	public <T> Where likeLeft(Func<T, ?> fieldFunc, Object... args) {
		return add(resolveColumnName(fieldFunc), "like", "%" + args[0]);
	}

	public <T> Where likeLeft(String columnName, Object... args) {
		return add(columnName, "like", "%" + args[0]);
	}

	public <T> Where likeLeft(Column column, Object... args) {
		return add(column, "like", "%" + args[0]);
	}

	public <T> Where likeRight(Func<T, ?> fieldFunc, Object... args) {
		return add(resolveColumnName(fieldFunc), "like", args[0] + "%");
	}

	public <T> Where likeRight(String columnName, Object... args) {
		return add(columnName, "like", args[0] + "%");
	}

	public <T> Where likeRight(Column column, Object... args) {
		return add(column, "like", args[0] + "%");
	}

	/**
	 * @category not like 包含
	 * 
	 *           注： % 由调用者自行定义
	 * 
	 * @param <T>
	 * @param fieldFunc
	 * @param args
	 * @return
	 */
	public <T> Where notLike(Func<T, ?> fieldFunc, Object... args) {
		return add(resolveColumnName(fieldFunc), "not like", args);
	}

	/**
	 * @category not like 包含
	 * 
	 *           注： % 由调用者自行定义
	 * 
	 * @param <T>
	 * @param columnName
	 * @param args
	 * @return
	 */
	public <T> Where notLike(String columnName, Object... args) {
		return add(columnName, "not like", args);
	}

	public <T> Where notLike(Column column, Object... args) {
		return add(column, "not like", args);
	}

	/**
	 * @category 两者之间
	 * 
	 * @param <T>
	 * @param fieldFunc
	 * @param args
	 * @return
	 */
	public <T> Where between(Func<T, ?> fieldFunc, Object... args) {
		return add(resolveColumnName(fieldFunc), "between", args);
	}

	/**
	 * @category 两者之间
	 * 
	 * @param <T>
	 * @param columnName
	 * @param args
	 * @return
	 */
	public <T> Where between(String columnName, Object... args) {
		return add(columnName, "between", args);
	}

	public <T> Where between(Column column, Object... args) {
		return add(column, "between", args);
	}

	/**
	 * @category 是否为Null
	 * 
	 * @param <T>
	 * @param fieldFunc
	 * @param args
	 * @return
	 */
	public <T> Where isNull(Func<T, ?> fieldFunc) {
		return add(resolveColumnName(fieldFunc), "is null");
	}

	/**
	 * @category 不为Null
	 * 
	 * @param <T>
	 * @param columnName
	 * @param args
	 * @return
	 */
	public <T> Where notNull(String columnName) {
		return add(columnName, "not null");
	}

	/**
	 * @category 不为Null
	 * 
	 * @param <T>
	 * @param fieldFunc
	 * @param args
	 * @return
	 */
	public <T> Where notNull(Func<T, ?> fieldFunc) {
		return add(resolveColumnName(fieldFunc), "not null");
	}

	/**
	 * @category 是否为Null
	 * 
	 * @param <T>
	 * @param columnName
	 * @param args
	 * @return
	 */
	public <T> Where isNull(String columnName) {
		return add(columnName, "is null");
	}

	/**
	 * @category 是否为空_Null等于空
	 * 
	 * @param <T>
	 * @param fieldFunc
	 * @param args
	 * @return
	 */
	public <T> Where isEmpty(Func<T, ?> fieldFunc) {
		return add(resolveColumnName(fieldFunc), "is empty");
	}

	/**
	 * @category 不为空_Null等于空
	 * 
	 * @param <T>
	 * @param columnName
	 * @param args
	 * @return
	 */
	public <T> Where isEmpty(String columnName) {
		return add(columnName, "is empty");
	}

	/**
	 * @category 不为空_Null等于空
	 * 
	 * @param <T>
	 * @param fieldFunc
	 * @param args
	 * @return
	 */
	public <T> Where notEmpty(Func<T, ?> fieldFunc) {
		return add(resolveColumnName(fieldFunc), "not empty");
	}

	/**
	 * @category 是否为空_Null等于空
	 * 
	 * @param <T>
	 * @param columnName
	 * @param args
	 * @return
	 */
	public <T> Where notEmpty(String columnName) {
		return add(columnName, "not empty");
	}

	private List<Object> args = new ArrayList<Object>();

	public List<Object> args(EntityService.DBTYPE category) {
		if (this.args.size() == 0) {
			this.sql(category);
		}
		return this.args;
	}

	public String sql(EntityService.DBTYPE category) {
		this.args.clear();
		if (this.entity != null) {
			return entitySql(category);
		}
		List<String> exprList = new ArrayList<String>();
		String oper = "";
		for (int i = 0; i < exprs.size(); i++) {
			WhereExpr expr = exprs.get(i);
			if (expr.isAnd() || expr.isOr()) {
				exprList.add(")");
				exprList.add(expr.sql(category));
				exprList.add("(");
				oper = "";
			} else if (expr.isLeftBrackets()) {
				exprList.add(oper);
				exprList.add("(");
				oper = "";
			} else if (expr.isRightBrackets()) {
				exprList.add(")");
				oper = " and ";
			} else {
				exprList.add(oper);
				exprList.add(expr.sql(category)); // 有可能是语句， 两边不加符号
				args.addAll(expr.args());
				oper = " and ";
			}
		}
		StringBuffer sb = new StringBuffer();
		for (String s : exprList) {
			if (s.length() > 0) {
				sb.append(s);
			}
		}
		String rtn = "";
		if (sb.length() > 0) {
			rtn = " where (" + sb.toString() + ")";
		}
		// log.info( rtn);
		return rtn;
	}

	private String entitySql(EntityService.DBTYPE category) {
		ObjUtilsReflection refs = new ObjUtilsReflection(this.entity.getClass());
		StringBuffer sb = new StringBuffer();
		String oper = "";
		for (Field field : refs.getFieldList()) {
			if (!EntityUtils.isTransientField(field)) {
				Object value = null;
				Method method = refs.getGetMethod(field.getName());
				if (method != null) {
					try {
						value = method.invoke(this.entity);
					} catch (IllegalAccessException | IllegalArgumentException | InvocationTargetException e) {
						log.error("Where对象反射调用方法异常：{}", field.getName(), e);
					}
				} else {
					field.setAccessible(true);
					try {
						value = field.get(this.entity);
					} catch (IllegalArgumentException | IllegalAccessException e) {
						log.error("Where对象反射读取字段异常：{}", field.getName(), e);
					}
					field.setAccessible(false);
				}
				if (value != null) {
					String col = ColumnUtils.getColumn(category, EntityUtils.columnName(field));
					sb.append(oper).append(col).append(" = ?");
					args.add(value);
					oper = " and ";
				}
			}
		}
		return sb.toString();
	}

	// public static void main(String[] args) {
	// Where w = Where.create();
	// w.like("aaa", "fsf");
	// System.out.println(w.sql(EntityService.DBTYPE.ORACLE));
	// System.out.println(w.args(EntityService.DBTYPE.ORACLE));
	// }
}
