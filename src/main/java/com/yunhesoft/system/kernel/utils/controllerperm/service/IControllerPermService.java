package com.yunhesoft.system.kernel.utils.controllerperm.service;

import java.util.List;
import java.util.Map;

import com.yunhesoft.system.kernel.utils.controllerperm.entity.dto.ControllerPermDto;
import com.yunhesoft.system.kernel.utils.controllerperm.entity.po.SysControllerPerm;

/**
 * 页面访问控制权限服务
 * 
 * <AUTHOR>
 *
 */
public interface IControllerPermService {

	/**
	 * 获取页面访问控制权限列表
	 */
	Map<String, List<String>> getControllerPermList();

	/**
	 * 获取页面访问控制权限列表 for 前台页面
	 * 
	 * @param permType 控制类型 admin or user
	 * @return
	 */
	List<SysControllerPerm> getAllControllerPermList(String permType);

	/**
	 * 前台保存
	 * 
	 * @param dto
	 * @return
	 */
	Boolean saveApiPerm(ControllerPermDto dto);
}
