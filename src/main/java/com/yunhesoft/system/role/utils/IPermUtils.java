package com.yunhesoft.system.role.utils;

import java.util.List;
import java.util.Map;

import com.yunhesoft.core.common.model.SysUser;
import com.yunhesoft.system.employee.entity.vo.EmployeeVo;
import com.yunhesoft.system.menu.entity.po.SysMenu;
import com.yunhesoft.system.role.entity.dto.EmpPermDto;
import com.yunhesoft.system.role.entity.dto.EmpRoleDto;
import com.yunhesoft.system.role.entity.po.SysRole;
import com.yunhesoft.system.role.entity.po.SysUserRole;

/**
 * 角色权限工具类
 * 
 * @category 角色权限工具类
 * <AUTHOR>
 *
 */

public interface IPermUtils {

	/**
	 * 判断当前用户是否拥有权限
	 * 
	 * @param permId 权限id (system:config:index:add)
	 * @return
	 */
	boolean isHasPerm(String permId);
	/**
	 * 判断当前用户是否拥有权限(菜单根据不同模式创建的，比如主题菜单)
	 * 
	 * @param tmSysType 参数类似 (tmSysType=类型id 主题菜单就是主题id)
	 * @param permId 权限id (system:config:index:add)
	 * @return
	 */
	boolean isHasTypePerm(String tmSysType, String permId);
	/**
	 * 判断是否拥有权限
	 * 
	 * @param userid 用户id
	 * @param permId 权限id (system:config:index:add)
	 * @return
	 */
//	boolean isHasPerm(String userId, String permId);

	/**
	 * 判断是否拥有权限
	 * 
	 * @param userid 用户id
	 * @param menuId 菜单id
	 * @return
	 */
//	boolean isHasPerm(SysUserPermDto params);

	/**
	 * 获取用户拥有的权限列表
	 * 
	 * @param userId 用户id
	 * @param type   M：for菜单权限；B：for按钮权限
	 * @return
	 */
	Map<String, Object> getPerms(String userId, String type);

	/**
	 * 获取用户拥有的权限列表
	 * @param mode 1:正常模式；2：协作模式
	 * @param userId
	 * @param type
	 * @return
	 */
	Map<String, Object> getPerms(String mode,String userId, String type);


	/**
	 * 根据权限id获取拥有的角色列表
	 * 
	 * @param permid 权限id
	 * @return
	 */
	List<SysRole> getRoleListByPermid(String permid);

	/**
	 * 根据权限id获取拥有的人员列表
	 * 
	 * @param permid
	 * @return
	 */
	List<EmployeeVo> getEmpListByPermid(EmpPermDto dto);

	/**
	 * 根据角色id获取拥有的人员列表
	 * 
	 * @param permid
	 * @return
	 */
	List<EmployeeVo> getEmpListByRoleid(EmpRoleDto dto);

	/**
	 * 判断是否为超级管理员
	 * 
	 * @param userId
	 * @return
	 */
	boolean isAdmin(String userId);

	/**
	 * 判断登录人员是否具有某个权限
	 * 
	 * @param tmSysType 参数类型
	 * @param perms     权限编码
	 * @return
	 */
	boolean isHashPerm(String tmSysType, String perms);

	/**
	 * 判断是否为租户管理员
	 * 
	 * @param userId
	 * @return
	 */
	boolean isTenantAdmin();

	/**
	 * 判断是否为租户管理员
	 * 
	 * @param userId
	 * @return
	 */
	boolean isTenantAdmin(SysUser user);

	/**
	 * 判断是否为租户管理员
	 * 
	 * @param userId
	 * @return
	 */
	boolean isTenantAdmin(List<SysUserRole> listRoles);

	List<SysMenu> getPermsByObjId(String objId,Integer objType, String type);
	/**
	 * 根据权限id获取拥有的人员列表
	 * 
	 * @param permid
	 * @return
	 */
	List<String> getEmpIdByPermid(List<String> permids);
}
