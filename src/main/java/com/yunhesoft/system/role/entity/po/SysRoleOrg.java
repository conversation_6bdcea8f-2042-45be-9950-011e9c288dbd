package com.yunhesoft.system.role.entity.po;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

import com.yunhesoft.core.common.entity.BaseEntity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Entity
@Table(name = "SYS_ROLE_ORG")
public class SysRoleOrg extends BaseEntity {

	private static final long serialVersionUID = 1L;

	/**
	 * 角色ID。
	 */
	@ApiModelProperty(value = "角色ID")
	@Column(name = "ROLEID", length=50)
	private String roleid;

	/**
	 * 权限ID。
	 */
	@ApiModelProperty(value = "权限ID")
	@Column(name = "PERMID", length=255)
	private String permid;

	/**
	 * 机构ID。
	 */
	@ApiModelProperty(value = "机构ID")
	@Column(name = "ORGID")
	private String orgid;
}
