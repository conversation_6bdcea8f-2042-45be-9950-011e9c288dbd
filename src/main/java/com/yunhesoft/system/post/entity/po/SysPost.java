package com.yunhesoft.system.post.entity.po;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

import com.yunhesoft.core.common.entity.BaseEntity;

import lombok.Getter;
import lombok.Setter;

/**
 * SysPost
 * 
 * @category SysPost
 * <AUTHOR>
 * @date 2020/03/12
 */
@Entity
@Setter
@Getter
@Table(name = "SYS_POST")
public class SysPost extends BaseEntity {
	/** */
	private static final long serialVersionUID = 1L;

	/** 树形全路径 */
	@Column(name = "ALLPATH", length = 2000)
	private String allpath;

	/** 岗位名称 */
	@Column(name = "NAME", length = 100)
	private String name;

	/** 岗位分类ID */
	@Column(name = "CLASSID", length = 50)
	private String classid;

	/** 岗位级别ID */
	@Column(name = "LEVELID", length = 50)
	private String levelid;

	/** 岗位系数 */
	@Column(name = "COEFFICIENT")
	private Double coefficient;

	/** 注释 */
	@Column(name = "MEMO", length = 1000)
	private String memo;

	/** 排序 */
	@Column(name = "TM_SORT", columnDefinition = "int default 0 ")
	private Integer tmSort;

	/** 是否使用 */
	@Column(name = "USED", columnDefinition = "int default 0 ")
	private Integer used;

	/** 层级 */
	@Column(name = "POSTLEVEL")
	private Integer postlevel;

	/** 是否为系统内置人员（1：是，0：否） */
	@Column(name = "SYS")
	private Integer sys;
	
	/** 是否确认岗位负责人 */
	@Column(name = "ISHEAD")
	private Integer isHead;

	//自定义岗位代码
	@Column(name = "ORG_CODE")
	private String orgCode;

	
	/**  专业ID*/
	@Column(name = "PROFESSIONALINFOID")
	private String professionalInfoId;

	@Column(name = "POST_LIB_ID",length = 50)
	private String postLibId;
	/** 租户ID */
//	@Column(name = "TENANT_ID", columnDefinition = "varchar(50) default '0'", length = 50)
//	private String tenant_id = "0";

}
