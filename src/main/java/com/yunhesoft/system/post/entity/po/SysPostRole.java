package com.yunhesoft.system.post.entity.po;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

import com.yunhesoft.core.common.entity.BaseEntity;

import lombok.Getter;
import lombok.Setter;

/**
 * 岗位角色绑定表
 * @category 岗位角色绑定表
 * <AUTHOR>
 * @date 2020/03/31
 */
@Entity	
@Setter
@Getter
@Table(name = "SYS_POST_ROLE")
public class SysPostRole extends BaseEntity {
	/** */
	private static final long serialVersionUID = 1L;

	/** 岗位ID（外键,关联:sys_post.id） */
	@Column(name = "postid", length=50)
	private String postid;

	/** 角色ID（外键,关联:sys_role.id） */
	@Column(name = "roleid", length=50)
	private String roleid;

}
