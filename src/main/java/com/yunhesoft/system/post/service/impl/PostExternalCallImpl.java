package com.yunhesoft.system.post.service.impl;

import java.util.ArrayList;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.stereotype.Service;

import com.yunhesoft.system.post.entity.dto.PostRoleParamDto;
import com.yunhesoft.system.post.entity.po.SysPostRole;
import com.yunhesoft.system.post.service.IPostExternalCallService;
import com.yunhesoft.system.post.service.ISysPostRoleService;

import lombok.extern.log4j.Log4j2;

/**
 * 岗位模块对外接口实现类
 * @category 岗位模块对外接口实现类
 * <AUTHOR>
 * @date 2020/03/31
 */
@Log4j2
@Service
@Repository("PostExternalCallImpl")
public class PostExternalCallImpl implements IPostExternalCallService {
	/** 岗位角色表操作服务接口 */
	@Autowired
	private ISysPostRoleService postRoleServ;

	/**
	 * 通过角色ID获取岗位角色信息（供外部模块调用）
	 * @category 通过角色ID获取岗位角色信息
	 * @param idList
	 * @return
	 */
	@Override
	public List<SysPostRole> getPostRoleListByRoleid(List<String> idList) {
		List<SysPostRole> list = new ArrayList<SysPostRole>();

		try {
			if (idList != null && idList.size() > 0) {
				String roleIds = "";
				for (String postRole : idList) {
					roleIds += "," + postRole;
				}
				if (!"".equals(roleIds)) {
					roleIds = roleIds.substring(1);
				}
				PostRoleParamDto postRolePrm = new PostRoleParamDto();
				postRolePrm.setRoleid(roleIds);
				list = postRoleServ.getPostRole(postRolePrm);
			}
		} catch (Exception e) {
			log.error("",e);
		}

		return list;
	}
}
