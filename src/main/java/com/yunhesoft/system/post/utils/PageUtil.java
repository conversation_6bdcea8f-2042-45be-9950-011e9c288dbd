package com.yunhesoft.system.post.utils;

import java.util.ArrayList;
import java.util.List;

import lombok.Getter;
import lombok.Setter;
import lombok.extern.log4j.Log4j2;

/**
 * 常用工具类
 * <AUTHOR>
 */
@Log4j2
@Getter
@Setter
public class PageUtil {
	/** 当前页数*/
	private Integer current;
	/** 每页记录数*/
	private Integer size;
	/** 所有记录数量*/
	private Integer allCount;

	/**
	 * 构造方法
	 */
	public PageUtil() {

	}

	/**
	 * 构造方法
	 * @param current
	 * @param size
	 */
	public PageUtil(Integer current, Integer size) {
		this.current = current;
		this.size = size;
	}

	/**
	 * 对数据列表进行分页，返回分页后数据列表
	 * @category 对数据列表进行分页
	 * @param <T>
	 * @param list
	 * @param current 当前页数
	 * @param size 每页记录数
	 * @return
	 */
	public <T> List<T> getPageList(List<T> list) {
		List<T> newList = new ArrayList<T>();

		try {
			if (list != null) {
				if (current == null || current.intValue() < 0 || size == null || size.intValue() <= 0) {
					return list;
				}

				int pageRecCnt = 0; // 每页记录计数器
				int startRecCnt = current.intValue() * size.intValue(); // 开始记录数，页数是从0开始计算时+1
				allCount = list.size(); // 最大记录数

				for (; startRecCnt < list.size(); startRecCnt++) {
					T obj = list.get(startRecCnt);
					newList.add(obj);
					// 达到每页记录数最大值后停止
					if ((pageRecCnt + 1) >= size) {
						break;
					}
					// 达到最后一条记录后停止
					if ((startRecCnt + 1) >= allCount.intValue()) {
						break;
					}
					pageRecCnt++; // 每页记录计数器+1
				}
			}
		} catch (Exception e) {
			log.error("",e);
		}

		return newList;
	}
}
