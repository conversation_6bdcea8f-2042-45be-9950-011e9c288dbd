package com.yunhesoft.accountTools.entity.po;

import com.yunhesoft.core.common.entity.BaseEntity;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;


/**
 * 活动台账表单模型关系表
 *
 */
@Entity
@Setter
@Getter
@Table(name = "DIGITAL_LEDGER")
public class DigitalLedger extends BaseEntity {

    private static final long serialVersionUID = 1L;


    /** 行云流表单ID */
    @Column(name="FORM_ID", length=50)
    private String formId;

    /** 行云流表单ID */
    @Column(name="FORM_ALIAS", length=255)
    private String formAlias;

    /** 行云流表单名称 */
    @Column(name="FORM_NAME", length=100)
    private String formName;

    /** 台账模型ID */
    @Column(name="LEDGER_MODULE_ID", length=50)
    private String ledgerModuleId;

    /** 台账名称 */
    @Column(name="LEDGER_NAME", length=50)
    private String ledgerName;

    /** 组件标识 */
    @Column(name="COMPONENT_ID", length=100)
    private String componentId;

    /** 版本暂无用 */
    @Column(name="VERSION_NUM", length=50)
    private String versionNum;

    /** 台账数据初始化类型 */
    @Column(name="INIT_TYPE", length=50)
    private String initType;

    /** 可用标识 */
    @Column(name="TMUSED")
    private Integer tmused;

    /** 排序 */
    @Column(name="TMSORT")
    private Integer tmsort;

    /** 删除按钮权限 */
    @Column(name="PERMISSION", length=2000)
    private String permission;

    /*
    @ApiModelProperty(hidden=true)
    @OneToMany(targetEntity=LedgerQualityIndex.class, mappedBy="ledger", fetch=FetchType.LAZY, cascade=CascadeType.REFRESH)
    private List<LedgerQualityIndex> ledgerQualityIndexList;
    */

    /*
    @ApiModelProperty(hidden=true)
    @OneToMany(targetEntity=DigitalLedgerTime.class, mappedBy="ledger", fetch=FetchType.LAZY, cascade=CascadeType.REFRESH)
    private List<DigitalLedgerTime> digitalLedgerTimeList;
    */

    /*
    @ApiModelProperty(hidden=true)
    @OneToMany(targetEntity=DigitalLedgerExtendRow.class, mappedBy="ledger", fetch=FetchType.LAZY, cascade=CascadeType.REFRESH)
    private List<DigitalLedgerExtendRow> digitalLedgerExtendRowList;
    */

    /*
    @ApiModelProperty(hidden=true)
    @OneToMany(targetEntity=DigitalLedgerFixedRow.class, mappedBy="ledger", fetch=FetchType.LAZY, cascade=CascadeType.REFRESH)
    private List<DigitalLedgerFixedRow> digitalLedgerFixedRowList;
    */


}
