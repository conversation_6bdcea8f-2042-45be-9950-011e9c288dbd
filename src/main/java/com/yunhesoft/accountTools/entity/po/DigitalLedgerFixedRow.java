package com.yunhesoft.accountTools.entity.po;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

import com.yunhesoft.core.common.entity.BaseEntity;

import lombok.Getter;
import lombok.Setter;


/**
 * 台账固定行初始化表
 * 
 */
@Entity
@Setter
@Getter
@Table(name = "DIGITAL_LEDGER_FIXED_ROW")
public class DigitalLedgerFixedRow extends BaseEntity {
	
    private static final long serialVersionUID = 1L;


    /** 台账ID */
    @Column(name="LEDGER_ID", length=50)
    private String ledgerId;
    
    /** 模版json字符串 */
    @Column(name="TPL_JSON", length=4000)
    private String tplJson;
    
    /** 可用标识 */
    @Column(name="TMUSED")
    private Integer tmused;
    

}
