package com.yunhesoft.accountTools.entity.dto;

import com.yunhesoft.accountTools.entity.vo.AccountAppVo;
import com.yunhesoft.system.applyConf.entity.vo.TdsAccountFormVo;
import com.yunhesoft.system.tds.entity.po.TdsAccountData;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class AccountParam {

	String orgCode;

	String nowDt;

	String rq;

	String userId;

	String manage;

	TdsAccountData data;

	String unitid;
	String unitname;
	String shiftCode;
	String imgid;

	String shfitClassId;//班次

	String tdsAlias;//数据源别名
	List<Map<String, String>> paramList;//输入参数值 [{name: xxxx, value: xxxx}]

	String qx;//权限标识
	Boolean addMark;//录入
	Boolean manageMark;//补录 管理
	Boolean searchMark;//查询

	List<TdsAccountFormVo> formList;

	String id;
	String op;//操作1启动 0停止
	String opTime;//时间


	//台账接口用
	String activeId;//活动ID
	String ledgerId;//台账模型ID
	String sbsj;//上班时间
	String xbsj;//下班时间
	List<AccountAppVo> rowList;//行数据列表
	Boolean opResult;

	//公式解析
	String formulaTxt;//公式内容
	String formId;//表单ID
	String orgName;//
	String zyid;//
	String zymc;//
	String gwid;//
	String gwmc;//

	List<Map<String, String>> formulaMap;//公式数组
	String val;//变量值 [统计值]的结果
	String tableId;
    String taskId;

}
