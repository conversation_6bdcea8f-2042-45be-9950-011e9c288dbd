package com.yunhesoft.leanCosting.baseConfig.entity.po;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

import com.yunhesoft.core.common.entity.BaseEntity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;



@Entity
@Setter
@Getter
@ApiModel("方案库分类")
@Table(name = "PROGRAMLIBRARYCLASS")
public class Programlibraryclass  extends BaseEntity {


    /**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value="父分类ID", example="8a1244a772565c34")
    @Column(name = "PID",length=100)
    private String pid;
    
    @ApiModelProperty(value="类名", example="类名1")
    @Column(name = "PCNAME",length=200)
    private String pcname;
    
    @ApiModelProperty(value="注释", example="注释1")
    @Column(name = "MEMO",length=1000)
    private String memo;
    
    @ApiModelProperty(value="排序", example="排序1")
    @Column(name = "ORDERNO",length=1000)
    private String orderno;
    
    @ApiModelProperty(value="装置类型", example="装置类型1")
    @Column(name = "DEVICETYPE",length=100)
    private String devicetype;
    
    @ApiModelProperty(value="无用", example="123")
    @Column(name = "USELESS")
    private int useless;
    

}