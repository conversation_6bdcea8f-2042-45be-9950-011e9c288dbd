package com.yunhesoft.leanCosting.priceManage.service;


import java.util.HashMap;
import java.util.List;

import com.yunhesoft.leanCosting.baseConfig.entity.vo.CostclassTreeVo;
import com.yunhesoft.leanCosting.priceManage.entity.dto.PriceManageQueryDto;
import com.yunhesoft.leanCosting.priceManage.entity.dto.PriceManageSaveDto;
import com.yunhesoft.leanCosting.priceManage.entity.po.ItemPriceDetail;
import com.yunhesoft.leanCosting.priceManage.entity.po.ItemPriceLog;
import com.yunhesoft.leanCosting.priceManage.entity.vo.ComboVo;
import com.yunhesoft.leanCosting.priceManage.entity.vo.ItemPriceChangeInfoVo;
import com.yunhesoft.leanCosting.priceManage.entity.vo.ItemPriceConfigVo;


/**
 *	价格管理相关服务接口
 * <AUTHOR>
 * @date 2023-09-21
 */
public interface IPriceManageService {

	//————————————————————————————————————————— 执行日期 ↓ ———————————————————————————————————————————
	
	/**
	 *	获取价格管理执行日期范围
	 * @return
	 */
	public ComboVo getPriceExecDateRange();
	
	/**
	 *	获取价格管理执行日期数据
	 * @param queryDto
	 * @return
	 */
	public List<ItemPriceChangeInfoVo> getPriceExecDateList(PriceManageQueryDto queryDto);
	
	/**
	 *	保存项目价格信息数据
	 * @param saveDto
	 * @return
	 */
	public String savePriceExecDateData(PriceManageSaveDto saveDto);
	
	//————————————————————————————————————————— 执行日期 ↑ ———————————————————————————————————————————
	
	
	
	
	
	//————————————————————————————————————————— 价格维护 ↓ ———————————————————————————————————————————
	
	/**
	 *	获取价格维护分类下拉框数据
	 * @return
	 */
	public List<ComboVo> getPriceConfigClassList();
	
	/**
	 *	获取价格维护表头数据
	 * @param dataType
	 * @param isExport
	 * @return
	 */
	public List<ComboVo> getPriceConfigColmList(String dataType, boolean isExport);
	
	/**
	 *	获取项目价格字段名称
	 * @param dataType
	 * @param colmCode
	 * @return
	 */
	public String getItemPriceName(String dataType, String colmCode);
	
	/**
	 *	获取价格维护数据
	 * @param queryDto
	 * @return
	 */
	public List<ItemPriceConfigVo> getPriceConfigDataList(PriceManageQueryDto queryDto);
	
	/**
	 *	保存价格维护数据
	 * @param saveDto
	 * @return
	 */
	public String savePriceConfigData(PriceManageSaveDto saveDto);
	
	/**
	 *	获取价格维护数据
	 * @param queryDto
	 * @return
	 */
	public List<ItemPriceConfigVo> getPriceConfigList(PriceManageQueryDto queryDto);
	
	//————————————————————————————————————————— 价格维护 ↑ ———————————————————————————————————————————

	
	
	
	
	//————————————————————————————————————————— 费用维护 ↓ ———————————————————————————————————————————

	/**
	 *	获取费用维护数据
	 * @param queryDto
	 * @return
	 */
	public List<ItemPriceConfigVo> getCostConfigDataList(PriceManageQueryDto queryDto);
	
	/**
	 *	获取价格明细数据Map(分类id+项目id作为主键)
	 * @param list
	 * @return
	 */
	public HashMap<String, ItemPriceDetail> getItemPriceDetailMap(List<ItemPriceDetail> list);
	
	/**
	 *	保存费用维护数据
	 * @param saveDto
	 * @return
	 */
	public String saveCostConfigData(PriceManageSaveDto saveDto);
	
	//————————————————————————————————————————— 费用维护 ↑ ———————————————————————————————————————————
	
	
	
	
	
	//————————————————————————————————————————— 价格日志 ↓ ———————————————————————————————————————————
	
	/**
	 *	生成日志数据
	 * @param itemPriceName
	 * @param dataObj
	 * @param saveObj
	 * @param userName
	 * @param currDt
	 * @return
	 */
	public ItemPriceLog getLogInfoObj(String itemPriceName,ItemPriceDetail dataObj,ItemPriceConfigVo saveObj,String userName,String currDt);
	
	/**
	 *	上传价格导入日志
	 * @param itemPriceName
	 * @param dataObj
	 * @param userName
	 * @param currDt
	 * @return
	 */
	public ItemPriceLog getPriceLogInfoObj(String itemPriceName,ItemPriceDetail dataObj,String userName,String currDt);
	
	/**
	 *	获取价格日志数据
	 * @param queryDto
	 * @return
	 */
	public List<ItemPriceLog> getPriceLogList(PriceManageQueryDto queryDto);
	
	/**
	 *	删除日志数据
	 * @param pIdList
	 * @return
	 */
	public String deletePriceLogData(List<String> pIdList);
	
	//————————————————————————————————————————— 价格日志 ↑ ———————————————————————————————————————————
	
	/**
	 *	价格管理是否使用Erp编码导入
	 * @param dataType
	 * @return
	 */
	public boolean isUseErpCodeImport(String dataType);
	
	
	/**
	 * 变成树形
	 * @param list
	 * @return
	 */
	public List<CostclassTreeVo> getTree();
	
}
