package com.yunhesoft.leanCosting.unitConf.service;


import com.yunhesoft.leanCosting.unitConf.entity.dto.CostKeyDeviceQueryDto;
import com.yunhesoft.leanCosting.unitConf.entity.dto.CostKeyDeviceSaveDto;
import com.yunhesoft.leanCosting.unitConf.entity.dto.DeviceParamDto;
import com.yunhesoft.leanCosting.unitConf.entity.po.CostKeyDeviceConf;
import com.yunhesoft.leanCosting.unitConf.entity.vo.CostKeyDeviceConfVo;

import java.util.List;


/**
 * 关键设备设置相关服务接口
 * <AUTHOR>
 * @date 2023-11-24
 */
public interface IKeyDeviceService {
	
	/**
	 * 获取关键设备设置数据（vo带附加属性）
	 * @param queryDto
	 * @return
	 */
	public List<CostKeyDeviceConfVo> getCostKeyDeviceConfVoList(CostKeyDeviceQueryDto queryDto);
	
	/**
	 * 获取关键设备设置数据（单表）
	 * @param queryDto
	 * @return
	 */
	public List<CostKeyDeviceConf> getKeyDeviceConfList(CostKeyDeviceQueryDto queryDto);

	/**
	 * 保存关键设备设置数据
	 * @param saveDto
	 * @return
	 */
	public String saveCostKeyDeviceData(CostKeyDeviceSaveDto saveDto);

	/**
	 *	保存关键设备数据
	 * @param addList
	 * @param updList
	 * @param delList
	 * @return
	 */
	public String saveDataCostKeyDevice(List<CostKeyDeviceConf> addList,List<CostKeyDeviceConf> updList,List<CostKeyDeviceConf> delList);
	
	/**
	 *	获取用于记事的关键设备列表
	 * @param unitid  （必填项）
	 * @param begintime  （非必填）
	 * @return
	 */
	public List<CostKeyDeviceConfVo> getUseToRecordEventList(String unitid, String begintime);
	
	
	/*-------------------------------------------------------------------------------------------------------------------------**/
	
	/**
	 * @category 获取设备数据
	 * @param param
	 * @return
	 */
	public List<CostKeyDeviceConf> getCostDeviceList(DeviceParamDto param);
	
}
