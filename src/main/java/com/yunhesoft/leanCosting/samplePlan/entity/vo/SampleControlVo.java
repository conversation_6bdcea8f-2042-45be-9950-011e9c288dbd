package com.yunhesoft.leanCosting.samplePlan.entity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.persistence.Column;
import java.util.Date;
import java.util.List;

@Getter
@Setter
@ToString
public class SampleControlVo {

	private String id;

	@ApiModelProperty(value = "主表id")
	private String pid;

	@ApiModelProperty(value = "顾客单位")
	private String client;

	@ApiModelProperty(value = "产品编号")
	private String productNo;

	@ApiModelProperty(value = "产品名称")
	private String product;

	@ApiModelProperty(value = "交货日期")
	private Date deliveryDate;

	@ApiModelProperty(value = "产品别名")
	private String productBm;

	@ApiModelProperty(value = "订单数量")
	private int orderNumber;

	@ApiModelProperty(value = "生产数量")
	private Integer manufactureNumber;

	@ApiModelProperty(value = "产品单位")
	private String unit;

	@ApiModelProperty(value = "物资描述")
	private String materialsDescribe;

	@ApiModelProperty(value = "产品描述")
	private String productDescribe;

	@ApiModelProperty(value = "材料牌号")
	private String materialsNo;

	@ApiModelProperty(value = "工艺")
	private String technology;

	@ApiModelProperty(value = "尺寸")
	private String measure;
	
	@ApiModelProperty(value = "实际尺寸")
	private String measureReal;
	
    @ApiModelProperty(value = "大端DN")
    private String bigDn;
    
    @ApiModelProperty(value = "小端DN")
    private String smallDn;

	@ApiModelProperty(value = "壁厚")
	private String wallThickness;

	@ApiModelProperty(value = "实际壁厚")
	private String wallThicknessReal;
	
    @ApiModelProperty(value = "大段SCH")
    private String bigSch;
    
    @ApiModelProperty(value = "小端SCH")
    private String smallSch;

	@ApiModelProperty(value = "制造标准")
	private String manufacturingStandards;

	@ApiModelProperty(value = "材料规格")
	private String materialSpecification;

	@ApiModelProperty(value = "材料编号")
	private String materialSserial;
	
	@ApiModelProperty(value = "审定人id")
	private String auditId;

	@ApiModelProperty(value = "重量")
	private Double weight;

	@ApiModelProperty(value = "审定人姓名")
	private String auditName;
	
	@ApiModelProperty(value = "审定人时间")
    private Date auditDate;

    @ApiModelProperty(value = "编辑人id")
    private String editorIalId;

    @ApiModelProperty(value = "编辑人姓名")
    private String editorIalName;

    @ApiModelProperty(value = "编辑时间")
    private Date editorIalDate;

	@ApiModelProperty(value = "状态")
	private int status;

	@ApiModelProperty(value = "是否有工艺卡")
	private int isTechnology;

	@ApiModelProperty(value = "产品编码ID")
	private String productNumberId;
	
	@ApiModelProperty(value = "图片地址list")
	private List<String> addressList;

	@ApiModelProperty(value = "是否使用")
	private int used;

	@ApiModelProperty(value = "排序")
	private int sort;
	
	@ApiModelProperty(value = "驳回原因")
	@Column(name = "REJECTIONREASONS",length = 500)
	private String rejectionReasons;
	
	@ApiModelProperty(value = "采用的工序类型id")
	@Column(name = "CLASSID",length = 50)
	private String classId;
	
	@ApiModelProperty(value = "采用的工序类型名")
	@Column(name = "CLASSNAME",length = 50)
	private String className;

}
