package com.yunhesoft.leanCosting.synLeanCost;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.yunhesoft.core.common.utils.DateTimeUtils;
import com.yunhesoft.core.common.utils.TMUID;
import com.yunhesoft.core.utils.ObjUtils;
import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.leanCosting.calcLogic.IGetLimsDataService;
import com.yunhesoft.leanCosting.deliverGoods.entity.po.ItemStockDayLedger;
import com.yunhesoft.system.synchronous.utils.SynModel;

@Service
public class SynGetItemStock extends SynModel {

	@Autowired
	private IGetLimsDataService glds;

	private String synDelFlagValue = synParam.get("synDelFlagValue"); // 删除标识值

	@Override
	protected List<HashMap<String, Object>> saveSynData(List<HashMap<String, Object>> dataList,
			HashMap<String, String> synParam) {
		List<HashMap<String, Object>> result = new ArrayList<HashMap<String, Object>>(); // 失败返回的数据
		if (StringUtils.isNotEmpty(dataList)) {
			if (StringUtils.isNotEmpty(synParam)) {
				this.synDelFlagValue = synParam.get("synDelFlagValue"); // 删除标识值
			}
			if (StringUtils.isEmpty(this.synDelFlagValue)) {
				this.synDelFlagValue = "T"; // 默认标识T为删除
			}

			List<ItemStockDayLedger> addList = new ArrayList<ItemStockDayLedger>();
			int count = dataList.size();
			String releasedon;
			for (int i = 0; count > i; i++) {
				HashMap<String, Object> temp = dataList.get(i);
				ItemStockDayLedger synVo = ObjUtils.convertToObject(ItemStockDayLedger.class, temp);
				if (synVo != null) { // 数据读取成功
					synVo.setId(TMUID.getUID());
					releasedon = synVo.getSendDate();
					if (StringUtils.isEmpty(releasedon)) {
						continue;// 没有返回版本的数据不用
					}
					if (StringUtils.isEmpty(this.version)) {
						this.setVersion(DateTimeUtils
								.formatDateTime(DateTimeUtils.addSeconds(DateTimeUtils.parseDateTime(releasedon), 1)));// 返回版本加1秒，避免重复采集
					} else {
						if (DateTimeUtils.bjDate(DateTimeUtils.parseDate(releasedon),
								DateTimeUtils.parseDate(this.version)) > 0) {
							this.setVersion(DateTimeUtils.formatDateTime(
									DateTimeUtils.addSeconds(DateTimeUtils.parseDateTime(releasedon), 1)));// 返回版本加1秒，避免重复采集
						}
					}
					addList.add(synVo);
				}
			}

			if (StringUtils.isNotEmpty(addList)) {
				this.glds.saveItemStockDayLedgerData(addList);
			}
		}
		return result;
	}

	@Override
	protected List<Object> getPullData(String whereSql, HashMap<String, String> synParam) {
		List<Object> dataList = new ArrayList<Object>();
		HashMap<String, Object> a = new HashMap<String, Object>();
		a.put("X", 1);
		a.put("Y", "是");
		dataList.add(a);
		HashMap<String, Object> b = new HashMap<String, Object>();
		b.put("X", 2);
		b.put("Y", "否");
		dataList.add(b);
		return dataList;
	}

}
