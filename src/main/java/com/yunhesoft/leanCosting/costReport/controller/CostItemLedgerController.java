package com.yunhesoft.leanCosting.costReport.controller;

import com.yunhesoft.core.common.model.Res;
import com.yunhesoft.leanCosting.costReport.entity.dto.ItemLedgerDto;
import com.yunhesoft.leanCosting.costReport.service.ICostItemLedgerService;
import com.yunhesoft.system.kernel.controller.BaseRestController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

@RestController
@Api(tags = "物料台账接口")
@RequestMapping("/costItemLedger")
public class CostItemLedgerController extends BaseRestController {

    @Autowired
    private ICostItemLedgerService srv;

    @ApiOperation("核算对象下拉")
    @RequestMapping(value = "/queryCostUnitComboList", method = RequestMethod.GET)
    public Res<?> queryCostUnitComboList() {
        return Res.OK(srv.getCostUnitComboList());
    }

    @ApiOperation("操作机构下拉")
    @RequestMapping(value = "/queryCostTeamComboList", method = RequestMethod.POST)
    public Res<?> queryCostTeamComboList(@RequestBody ItemLedgerDto dto) {
        return Res.OK(srv.getCostTeamComboList(dto.getUnitId()));
    }

    @ApiOperation("核算项目分类下拉")
    @RequestMapping(value = "/queryCostItemClassComboList", method = RequestMethod.POST)
    public Res<?> queryCostItemClassComboList(@RequestBody ItemLedgerDto dto) {
        return Res.OK(srv.getCostItemClassComboList(dto.getUnitId(), dto.getEndDate()));
    }

    @ApiOperation("查询表格内容")
    @RequestMapping(value = "/queryTableContent", method = RequestMethod.POST)
    public Res<?> queryTableContent(@RequestBody ItemLedgerDto dto) {
        return Res.OK(srv.getQueryTableJsonObj(dto.getUnitId(), dto.getTeamId(), dto.getStartDate(), dto.getEndDate(), dto.getClassId()));
    }


    @ApiOperation("导出excel")
    @RequestMapping(value = "/exportQueryExcel", method = RequestMethod.POST)
    public void exportQueryExcel(@RequestBody ItemLedgerDto dto) {
        srv.exportExcel(response, dto.getUnitId(), dto.getTeamId(), dto.getStartDate(), dto.getEndDate(), dto.getClassId());
    }

}
