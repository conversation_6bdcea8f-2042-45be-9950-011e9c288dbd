package com.yunhesoft.leanCosting.order.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.yunhesoft.core.common.utils.TMUID;
import com.yunhesoft.core.utils.ObjUtils;
import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.leanCosting.order.entity.dto.SaveDto;
import com.yunhesoft.leanCosting.order.entity.dto.SearchOrderDto;
import com.yunhesoft.leanCosting.order.entity.dto.SelectOrderDto;
import com.yunhesoft.leanCosting.order.entity.po.OrderManagement;
import com.yunhesoft.leanCosting.order.entity.po.OrderTableSet;
import com.yunhesoft.leanCosting.order.entity.po.ProductControl;
import com.yunhesoft.leanCosting.order.service.IOrderTableSet;
import com.yunhesoft.leanCosting.order.service.IProductService;
import com.yunhesoft.leanCosting.order.entity.vo.ExcleCell;
import com.yunhesoft.leanCosting.order.service.IOrderMangeService;
import com.yunhesoft.system.kernel.config.SysUserHolder;
import com.yunhesoft.system.kernel.service.EntityService;
import com.yunhesoft.system.kernel.service.model.Order;
import com.yunhesoft.system.kernel.service.model.Pagination;
import com.yunhesoft.system.kernel.service.model.Where;
import com.yunhesoft.system.kernel.utils.excel.ExcelExport;
import org.apache.poi.hssf.usermodel.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description: 订单管理接口实现类
 * @date 2021/09/10
 */
@Service
public class OrderMangeServiceImpl implements IOrderMangeService {

    @Autowired
    private EntityService dao;

    @Autowired
    private IProductService ips;

    @Autowired
    private IOrderTableSet IOrderTableSet;

    /**
     * 批量添加订单.
     *
     * @param
     * @return
     */
    @Override
    public Boolean addOrder(List<OrderManagement> orders) {
        String maxNo;
//        List<OrderManagement> data = dao.rawQueryList(OrderManagement.class);
//        if (StringUtils.isNotEmpty(data)) {
//            maxNo = String.valueOf(Integer.parseInt(data.get(data.size() - 1).getOrderNo()) + 1);
//        } else {
//            maxNo = String.valueOf(1);
//        }
        List<OrderManagement> addlist = new ArrayList<>();
        for (OrderManagement neworder : orders) {
            if (!StringUtils.isNotEmpty(neworder.getId())) {
                neworder.setId(TMUID.getUID());
            }
            neworder.setUsed(1);
            //设置创建人
            neworder.setCreatePersonName(SysUserHolder.getCurrentUser().getRealName());
//            if (StringUtils.isEmpty(neworder.getOrderNo())) {
//                neworder.setOrderNo(maxNo);
//            }
            addlist.add(neworder);
        }
        int msg = 0;
        if (StringUtils.isNotEmpty(addlist)) {
            msg = dao.insertBatch(addlist);
        }
        return msg > 0;
    }

    /**
     * 批量删除订单.
     *
     * @param
     * @return
     */
    @Override
    public Boolean delOrder(List<OrderManagement> dels) throws Exception {
        List<OrderManagement> delorders = new ArrayList<>();
        List<ProductControl> proList = ips.selectProduct(null); // 获取全部产品
        List<ProductControl> delProList = new ArrayList<>();

        List<String> idList = new ArrayList<>();
        int index = 0;
        for (OrderManagement del : dels) {
            //查看此订单下有无产品
            List<ProductControl> collect = proList.stream().filter(i -> i.getPid().equals(del.getId())).collect(Collectors.toList());
            if(StringUtils.isNotEmpty(collect)){
                //有产品不能删除
                throw new Exception("选中记录中第"+(index+1)+"条不能删除,因为该订单包含产品");
            }
            del.setUsed(0);
            delorders.add(del);
            for (ProductControl p : proList) {
                // 删除其下的产品
                if (StringUtils.isNotEmpty(p.getPid())) {
                    if (p.getPid().equals(del.getId())) {
                        p.setUsed(0);
                        idList.add(p.getId());
                        delProList.add(p);
                    }
                }
            }
            index++;
        }
        int msg = 0;
        if (StringUtils.isNotEmpty(delorders)) {
            msg = dao.updateByIdBatch(delorders);
        }
        if (StringUtils.isNotEmpty(delProList)) {
            dao.updateByIdBatch(delProList);
        }
        return msg > 0;
    }

    /**
     * 批量更新订单.
     *
     * @param
     * @return
     */
    @Override
    public Boolean updateOrder(List<OrderManagement> orders) {
        int msg = 0;
        if (StringUtils.isNotEmpty(orders)) {
            for (OrderManagement order : orders) {
                order.setUpdatePersonName(SysUserHolder.getCurrentUser().getRealName());
            }
            msg = dao.updateByIdBatch(orders);
        }

        // 修改产品信息
        List<ProductControl> updatePro = new ArrayList<>();
        List<ProductControl> allPro = ips.getAllPro();
        for (OrderManagement o : orders) {
            for (ProductControl pro : allPro) {
                if (o.getId().equals(pro.getPid())) {
                    pro.setClient(o.getClient());
                    pro.setDeliveryDate(o.getDeliveryDate());
                    pro.setStatus(o.getStatus());
                    updatePro.add(pro);
                }
            }
        }
        if (StringUtils.isNotEmpty(updatePro)) {
            ips.updateProduct(updatePro);
        }

        return msg > 0;
    }

    /**
     * 分页查询订单.
     *
     * @param
     * @return
     */
    @Override
    public List<OrderManagement> selectPageOrder(Pagination<?> page, SelectOrderDto param) {
        Where where = Where.create();
        where.eq(OrderManagement::getUsed, 1);
        if (param.getIsRealse() != null && param.getIsRealse()) {
            where.gt(OrderManagement::getStatus, 0);
        }
        if (StringUtils.isNotEmpty(param.getCustomerName())) {
            where.like(OrderManagement::getClient, param.getCustomerName());
        }
        if (StringUtils.isNotEmpty(param.getMonth())) {
            where.gt(OrderManagement::getDeliveryDate, param.getMonth().get(0));
            where.lt(OrderManagement::getDeliveryDate, param.getMonth().get(1));
        }
        Order order = Order.create();
        order.orderByAsc(OrderManagement::getStatus);
        order.orderByDesc(OrderManagement::getCreateTime);
        return dao.queryData(OrderManagement.class, where, order, page);
    }

    /**
     * 保存数据.
     *
     * @param
     * @return
     */
    @Override
    public Boolean saveOrder(SaveDto param) throws Exception {
        boolean bln = false;
        if (param != null && StringUtils.isNotEmpty(param.getData())) {
            List<OrderManagement> insertList = new ArrayList<>();
            List<OrderManagement> upateList = new ArrayList<>();
            List<OrderManagement> deleteList = new ArrayList<>();
            JSONArray datas = JSONArray.parseArray(param.getData());
            if (datas != null) {
                for (int i = 0; i < datas.size(); i++) {
                    JSONObject row = datas.getJSONObject(i);
                    Integer flag = row.getInteger("flag");
                    OrderManagement bean = new OrderManagement();
                    bean = JSONObject.toJavaObject(row, OrderManagement.class);
                    if (flag == null || flag == 0) {
                        bean.setId(TMUID.getUID());
                        insertList.add(bean);
                    } else if (flag == 1) {
                        if (StringUtils.isNotEmpty(bean.getId())) {
                            deleteList.add(bean);
                        }
                    } else if (flag == 2) {
                        bean.setUpdateTime(new Date());
                        upateList.add(bean);
                    }
                }

            }
            if (StringUtils.isNotEmpty(insertList)) {
                bln = this.addOrder(insertList);
            }
            if (StringUtils.isNotEmpty(deleteList)) {
                bln = this.delOrder(deleteList);
            }
            if (StringUtils.isNotEmpty(upateList)) {
                bln = this.updateOrder(upateList);
            }
        }
        return bln;
    }

    /**
     * 查询订单总数.
     *
     * @param
     * @return
     */
    @Override
    public int orderTotal() {
        Where where = Where.create();
        where.eq(OrderManagement::getUsed, 1);
        return dao.queryCount(OrderManagement.class, where).intValue();
    }

    /**
     * 搜索订单.
     *
     * @param
     * @return
     */
    @Override
    public List<OrderManagement> searchOrder(Pagination<?> page, SearchOrderDto searchDto) {
        Where where = Where.create();
        where.eq(OrderManagement::getUsed, 1);
        if (StringUtils.isNotEmpty(searchDto.getOrderNo())) {
            where.like(OrderManagement::getOrderNo, searchDto.getOrderNo());
        }
        if (StringUtils.isNotEmpty(searchDto.getClient())) {
            where.like(OrderManagement::getClient, searchDto.getClient());
        }
        if (ObjUtils.notEmpty(searchDto.getDeliveryDate())) {
            where.like(OrderManagement::getDeliveryDate, searchDto.getDeliveryDate());
        }
        if (StringUtils.isNotEmpty(searchDto.getPlanNo())) {
            where.like(OrderManagement::getPlanNo, searchDto.getPlanNo());
        }
        if (StringUtils.isNotEmpty(searchDto.getContractNo())) {
            where.like(OrderManagement::getContractNo, searchDto.getContractNo());
        }
        if (StringUtils.isNotEmpty(searchDto.getContact())) {
            where.like(OrderManagement::getContact, searchDto.getContact());
        }
        if (StringUtils.isNotEmpty(searchDto.getTel())) {
            where.like(OrderManagement::getTel, searchDto.getTel());
        }
        if (StringUtils.isNotEmpty(searchDto.getEmail())) {
            where.like(OrderManagement::getEmail, searchDto.getEmail());
        }
        if (ObjUtils.notEmpty(searchDto.getOrderDate())) {
            where.like(OrderManagement::getOrderDate, searchDto.getOrderDate());
        }
        if (ObjUtils.notEmpty(searchDto.getTransportation())) {
            where.like(OrderManagement::getTransportation, searchDto.getTransportation());
        }
        if (ObjUtils.notEmpty(searchDto.getConfirm())) {
            where.like(OrderManagement::getConfirm, searchDto.getConfirm());
        }
        if (ObjUtils.notEmpty(searchDto.getIsUrgent())) {
            where.eq(OrderManagement::getIsUrgent, searchDto.getIsUrgent());
        }
        Order order = Order.create();
        order.orderByAsc(OrderManagement::getStatus);
        order.orderByDesc(OrderManagement::getCreateTime);
        List<OrderManagement> orderlist;
        if (page != null) {
            orderlist = dao.queryData(OrderManagement.class, where, order, page);
        } else {
            orderlist = dao.rawQueryListByWhere(OrderManagement.class, where, order);
        }
        return orderlist;
    }

    /**
     * 按订单日期月份搜索.
     *
     * @param
     * @return
     */
    @Override
    public List<OrderManagement> searchOrderByMonth(String month) {
        Where where = Where.create();
        where.eq(OrderManagement::getUsed, 1);
        where.like(OrderManagement::getOrderDate, month);
        return dao.rawQueryListByWhere(OrderManagement.class, where);
    }

    /**
     * 查询搜索条件下总数.
     *
     * @param
     * @return
     */
    @Override
    public int orderTotalSearch(SearchOrderDto searchDto) {
        Where where = Where.create();
        where.eq(OrderManagement::getUsed, 1);
        if (StringUtils.isNotEmpty(searchDto.getMonth())) {
            where.like(OrderManagement::getOrderDate, searchDto.getMonth());
        }
        if (searchDto.getStatus() != null) {
            where.eq(OrderManagement::getStatus, searchDto.getStatus());
        }
        if (StringUtils.isNotEmpty(searchDto.getClient())) {
            where.like(OrderManagement::getClient, searchDto.getClient());
        }
        return dao.queryCount(OrderManagement.class, where).intValue();
    }

    /**
     * 验重.
     *
     * @param
     * @return
     */
    @Override
    public Boolean isRepeat(String[] planNo) {
        Where where = Where.create();
        where.eq(OrderManagement::getUsed, 1);
        where.andIns(OrderManagement::getPlanNo, planNo);
        List<OrderManagement> list = dao.rawQueryListByWhere(OrderManagement.class, where);
        Boolean repeat = false;
        if (StringUtils.isNotEmpty(list)) {
            repeat = true; // 当有重复返回true
        } else {
            repeat = false; // 当没有重复返回false
        }
        return repeat;
    }

    /**
     * 切换状态.
     *
     * @param
     * @return
     */

    @Override
    public Boolean switchOrderStatus(String id, Integer statusCode) {
        int msg = 0;
        Where where = Where.create();
        where.eq(OrderManagement::getUsed, 1);
        where.eq(OrderManagement::getId, id);
        OrderManagement order = dao.queryObject(OrderManagement.class, where);
        if (order != null) {
            order.setStatus(statusCode);
            msg = dao.updateById(order);
        } else {
            return false;
        }
        return msg > 0;
    }

    /**
     * 产品状态改变时 修改订单状态
     *
     * @param
     * @return
     * <AUTHOR>
     */
    @Override
    public Boolean changeOrderStatusBypro(String productId) {
        Boolean flag = false;
        int status = -1;
        ProductControl product = ips.selectProductById(productId);
        List<ProductControl> productList = ips.selectProduct(product.getPid());
        for (ProductControl pro : productList) {
            if (pro.getStatus() == 3) {
                status = 2;
                break;
            } else if (pro.getStatus() == 4) {
                status = 3;
            }
        }
        if (status > -1) {
            flag = this.switchOrderStatus(product.getPid(), status);
        }
        return flag;

    }


    /**
     * 导出数据
     *
     * @param searchDto
     */
    @Override
    public void exportOrderData(SearchOrderDto searchDto, HttpServletResponse response) {
//		获取订单数据
        List<OrderManagement> orderList = this.searchOrder(null, searchDto);
//		获取全部产品数据
        List<ProductControl> allProducts = ips.selectProduct(null);
//      制作订单和产品列表的映射
        Map<String, List<ProductControl>> productsByOrderIdMap = new HashMap<>();
        for (ProductControl product : allProducts) {
            if (product.getUsed() == 1) {
                String orderId = product.getPid();
                if (productsByOrderIdMap.containsKey(orderId)) {
                    List<ProductControl> proList = productsByOrderIdMap.get(orderId);
                    proList.add(product);
                } else {
                    List<ProductControl> proList = new ArrayList<>();
                    proList.add(product);
                    productsByOrderIdMap.put(orderId, proList);
                }
            }
        }
        //创建工作薄对象
        HSSFWorkbook workbook = new HSSFWorkbook();
        HSSFCellStyle style = workbook.createCellStyle();
        HSSFFont font = workbook.createFont();
//		准备订单表头
        List<ExcleCell> titleCellOrder = new ArrayList<>();
        this.buildOrderTitle(titleCellOrder, workbook, style, font);
        //index 和表头对象map
        Map<String, ExcleCell> titleOrderMap = titleCellOrder.stream().collect(Collectors.toMap(ExcleCell::getProperty, a -> a, (k1, k2) -> k1));
//        准备产品表头
        HSSFCellStyle style1 = workbook.createCellStyle();
        HSSFFont font1 = workbook.createFont();
        List<ExcleCell> titleCellPro = new ArrayList<>();
        this.buildProductTitle(titleCellPro, workbook, style1, font1);
        Map<String, ExcleCell> titleProMap = titleCellPro.stream().collect(Collectors.toMap(ExcleCell::getProperty, a -> a, (k1, k2) -> k1));

        //开始构建excle
        //创建sheet
        HSSFSheet sheet = workbook.createSheet();
        sheet.setActive(true);
        sheet.createFreezePane(0, 1, 0, 1);
        //单元格高度宽度
        this.columnWidth(16, sheet);
        //设置sheet内容
        //创建行
        int rowIndex = 0;
        this.buildTitle(rowIndex, sheet, titleCellOrder);
        //将订单信息设置
        int orderNo = 0;
        for (OrderManagement order : orderList) {
            ++rowIndex;
            ++orderNo;
            HSSFCellStyle styleOrder = workbook.createCellStyle();
            HSSFFont fontOrder = workbook.createFont();
            this.buildExcle(order, 2, orderNo, rowIndex, titleOrderMap, sheet, workbook, styleOrder, fontOrder);
            List<ProductControl> proList = productsByOrderIdMap.get(order.getId());
            if (StringUtils.isNotEmpty(proList)) {
                ++rowIndex;
                this.buildTitle(rowIndex, sheet, titleCellPro);
                int proNo = 0;
                for (ProductControl pro : proList) {
                    ++rowIndex;
                    ++proNo;
                    HSSFCellStyle stylePro = workbook.createCellStyle();
                    HSSFFont fontPro = workbook.createFont();
                    this.buildExcle(pro, 3, proNo, rowIndex, titleProMap, sheet, workbook, stylePro, fontPro);
                }
                ++rowIndex;
                HSSFRow row = sheet.createRow(rowIndex);
            }
        }
        workbook.setSheetName(0, "订单数据");//设置sheet的Name
        //文档输出
        ExcelExport.downLoadExcel("订单数据", response, workbook);
    }

    /**
     * 设置列宽
     *
     * @param
     * @return
     * <AUTHOR>
     */
    private void columnWidth(int colNum, HSSFSheet sheet) {
        for (int i = 0; i < colNum; i++) {
            sheet.setColumnWidth(i, 20 * 256);//设置第一列的宽度
        }
    }

    /**
     * 构建订单表头
     *
     * @param
     * @return
     * <AUTHOR>
     */
    private List<ExcleCell> buildProductTitle(List<ExcleCell> titleCellPro, HSSFWorkbook workbook, HSSFCellStyle style, HSSFFont font) {
        int index = 1;
        List<OrderTableSet> products = IOrderTableSet.getOrderTableSet("product");
        titleCellPro.add(new ExcleCell("序号", "no", 1, index, workbook, style, font));
        for (OrderTableSet product : products) {
            if (product.getIsShow() == 1) {
                titleCellPro.add(new ExcleCell(product.getHeaderName(), product.getSourceCode(), 1, ++index, workbook, style, font));
            }
        }
        return titleCellPro;
    }

    /**
     * 构建订单表头
     *
     * @param
     * @return
     * <AUTHOR>
     */
    private List<ExcleCell> buildOrderTitle(List<ExcleCell> titleCellOrder, HSSFWorkbook workbook, HSSFCellStyle style, HSSFFont font) {
        //获取表头信息进行编制
        int index = 0;
        List<OrderTableSet> orders = IOrderTableSet.getOrderTableSet("order");
        titleCellOrder.add(new ExcleCell("序号", "no", 0, 0, workbook, style, font));
        for (OrderTableSet order : orders) {
            if (order.getIsShow() == 1) {
                titleCellOrder.add(new ExcleCell(order.getHeaderName(), order.getSourceCode(), 0, ++index, workbook, style, font));
            }
        }
        return titleCellOrder;
    }

    /**
     * 构建标题
     *
     * @param
     * @return
     * <AUTHOR>
     */
    private void buildTitle(int rowIndex, HSSFSheet sheet, List<ExcleCell> titleCellOrder) {
        HSSFRow row = sheet.createRow(rowIndex);
        //设置首行标题
        for (ExcleCell title : titleCellOrder) {
            HSSFCell cell = row.createCell(title.getIndex());
            cell.setCellValue(title.getValue());
            cell.setCellStyle(title.getStyle());
        }
    }

    private <T> T buildExcle(T order, int type, int orderNo, int rowIndex, Map<String, ExcleCell> titleOrderMap, HSSFSheet sheet, HSSFWorkbook workbook, HSSFCellStyle style, HSSFFont font) {
        HSSFRow orderRow = sheet.createRow(rowIndex);
        List<ExcleCell> orderCellList = new ArrayList<>();
        String oNo = String.valueOf(orderNo);
        ExcleCell orderInfo = new ExcleCell(oNo, "no", type, titleOrderMap.get("no").getIndex(), workbook, style, font);
        orderCellList.add(orderInfo);
        String jsonStr = JSONObject.toJSONString(order, SerializerFeature.WriteMapNullValue);
        Map<String, Object> objMap = JSONObject.parseObject(jsonStr);
        //遍历对象map
        for (Map.Entry<String, Object> entry : objMap.entrySet()) {
            String value;
            if (entry.getValue() != null) {
                value = entry.getValue().toString();
            } else {
                value = "空";
            }

            String property = entry.getKey();
            if ("isUrgent".equals(property)) {
                if ("1".equals(value)) {
                    value = "加急";
                } else {
                    value = "";
                }
            } else if ("status".equals(property)) {
                if ("EmiOrderManagement".equals(order.getClass().getSimpleName())) {
                    switch (value) {
                        case "0":
                            value = "未下发";
                            break;
                        case "1":
                            value = "已下发";
                            break;
                        case "2":
                            value = "生产中";
                            break;
                        case "3":
                            value = "已完成";
                            break;
                        default:
                            break;
                    }
                } else if ("EmiProductControl".equals(order.getClass().getSimpleName())) {
                    switch (value) {
                        case "-1":
                            value = "审核驳回";
                            break;
                        case "0":
                            value = "未下发";
                            break;
                        case "1":
                            value = "待设计";
                            break;
                        case "2":
                            value = "待审核";
                            break;
                        case "3":
                            value = "待生产";
                            break;
                        case "4":
                            value = "已完成";
                            break;
                        default:
                            break;
                    }
                }
            }
            ExcleCell c = titleOrderMap.get(property);
            if (c == null) {
                continue;
            }
            orderCellList.add(new ExcleCell(value, property, type, c.getIndex(), workbook, style, font));
        }
        for (ExcleCell orderCell : orderCellList) {
            HSSFCell cell = orderRow.createCell(orderCell.getIndex());
            cell.setCellValue(orderCell.getValue());
            cell.setCellStyle(orderCell.getStyle());
        }

        return null;
    }
}
