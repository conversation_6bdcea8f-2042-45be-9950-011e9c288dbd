package com.yunhesoft.leanCosting.standby.entity.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 产品管理检索条件
 * 
 * <AUTHOR>
 * @date 2021/09/15
 */
@Getter
@Setter
@ToString
@ApiModel(value = "产品管理检索条件", description = "产品管理检索条件")
public class QueryStandbyProductDto {
	
	/** 分页当前页数*/
	@ApiModelProperty(value = "pageNum")
	private Integer pageNum;
	
	/** 分页每页记录数*/
	@ApiModelProperty(value = "pageSize")
	private Integer pageSize;

	/** 父ID*/
	@ApiModelProperty(value = "pid")
	private String pid;
	
	/** 月份 */
	@ApiModelProperty(value = "month")
    private String month;
	
	/** 状态 */
	@ApiModelProperty(value = "status")
    private Integer status;
	
	/** 产品名称 */
	@ApiModelProperty(value = "productName")
    private String productName;
	
	/** 顾客单位 */
	@ApiModelProperty(value = "customerName")
    private String customerName;

}
