package com.yunhesoft.leanCosting.standby.entity.po;

import com.yunhesoft.core.common.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

@ApiModel(value = "订单表头信息设置表")
@Entity
@Data
@Table(name = "STANDBY_TABLE_SET")
public class StandbyTableSet extends BaseEntity {
    @ApiModelProperty(value = "表头名称")
    @Column(name = "HEADERNAME", length = 50)
    private String headerName;

    @ApiModelProperty(value = "原始名称")
    @Column(name = "INITHEADERNAME", length = 50)
    private String initHeaderName;

    @ApiModelProperty(value = "实体字段代码")
    @Column(name = "SOURCECODE", length = 50)
    private String sourceCode;

    @ApiModelProperty(value = "组件类型")
    @Column(name = "COMPONENT_TYPE", length = 50)
    private String componentType;

    @ApiModelProperty(value = "是否必填")
    @Column(name = "ISMUST")
    private Integer isMust;

    @ApiModelProperty(value = "是否唯一")
    @Column(name = "ISONLY")
    private Integer isOnly;

    @ApiModelProperty(value = "是否显示")
    @Column(name = "ISSHOW")
    private Integer isShow;

    @ApiModelProperty(value = "是否删除")
    @Column(name = "TMUSED")
    private Integer tmused;

    @ApiModelProperty(value = "对其方式")
    @Column(name = "ALIGN")
    private String align;

    @ApiModelProperty(value = "宽度")
    @Column(name = "WIDTH")
    private Integer width;

    @ApiModelProperty(value = "输入最大长度")
    @Column(name = "MAXLENGTH")
    private Integer maxlength;

    @ApiModelProperty(value = "排序")
    @Column(name = "TMSORT")
    private Integer tmsort;

    @ApiModelProperty(value = "渲染脚本")
    @Column(name = "FORMATSCRIPT" ,length = 2000)
    private String formatScript;

    // order 代表订单表头  product代表产品表头
    @ApiModelProperty(value = "表头类型")
    @Column(name = "HEADERTYPE" ,length = 2000)
    private String headerType;

    @ApiModelProperty(value = "是否是锁定列")
    @Column(name = "ISLOCK" )
    private Integer isLock;

    @ApiModelProperty(value = "是否同时设置检索条件")
    @Column(name = "ISQUERY" )
    private Integer isQuery;
}
