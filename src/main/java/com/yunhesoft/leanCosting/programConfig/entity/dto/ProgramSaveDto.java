package com.yunhesoft.leanCosting.programConfig.entity.dto;


import com.yunhesoft.leanCosting.programConfig.entity.vo.ProgramClassItemVo;

import lombok.Getter;
import lombok.Setter;


/**
 *	方案保存类
 */
@Setter
@Getter
public class ProgramSaveDto{
	
	
	private String pId; //父ID
    
    private String editType; //del-删除；save-保存；
    
    private ProgramClassItemVo programClassItemObj; //方案分类项目树形保存数据
    
    
}
