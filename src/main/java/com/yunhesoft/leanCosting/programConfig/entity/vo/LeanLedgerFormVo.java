package com.yunhesoft.leanCosting.programConfig.entity.vo;


import com.yunhesoft.leanCosting.programConfig.entity.po.LeanLedgerForm;
import com.yunhesoft.tmsf.form.entity.po.SFForm;

import lombok.Getter;
import lombok.Setter;

/**
 * 	精益台账Vo类
 * <AUTHOR>
 * @date 2023/08/17
 */
@Setter
@Getter
public class LeanLedgerFormVo extends LeanLedgerForm {
	
	private static final long serialVersionUID = 1L;
	
	/** 表单名称 */
    private String formName;
    
    /** 表单数据对象 */
    private SFForm formObj;
    
    
}
