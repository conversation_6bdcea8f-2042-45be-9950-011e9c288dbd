package com.yunhesoft.leanCosting.programConfig.entity.po;


import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

import com.yunhesoft.core.common.entity.BaseEntity;

import lombok.Getter;
import lombok.Setter;


/**
 *	方案版本表
 */
@Entity
@Setter
@Getter
@Table(name = "PROGRAMVERSION")
public class ProgramVersion extends BaseEntity {
	
    private static final long serialVersionUID = 1L;
    
    /** 方案ID */
    @Column(name="PROJECTDATAID", length=100)
    private String projectDataId;
    
    /** 版本 */
    @Column(name="PVERSION", length=50)
    private String pVersion;
    
    /** 注释 */
    @Column(name="MEMO", length=1000)
    private String memo;
    
}
