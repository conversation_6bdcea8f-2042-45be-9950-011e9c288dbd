package com.yunhesoft.leanCosting.productSchedu.entity.po;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

import com.yunhesoft.core.common.entity.BaseEntity;

import lombok.Getter;
import lombok.Setter;


/**
 * 计划反馈-产品临时库
 * 
 */
@Entity
@Setter
@Getter
@Table(name = "PRODUCTSCHEDU_RECEIVELIBRARY")
public class ProductScheduReceiveLibrary extends BaseEntity {
	
    private static final long serialVersionUID = 1L;

    /** 核算对象ID  */
    @Column(name="UNITID", length=100)
    private String unitid;
    
    /** 项目ID  */
    @Column(name="ITEMID", length=100)
    private String itemid;
    
    /** 方案ID  */
    @Column(name="PROGRAMID", length=100)
    private String programid;
    
    /** 批次号 */
    @Column(name="BATCHNO", length=100)
    private String batchno;
   
    /** 数量 */
    @Column(name="AMOUNT")
    private double amount;
    

}
