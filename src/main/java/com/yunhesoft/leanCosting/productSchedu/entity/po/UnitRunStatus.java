package com.yunhesoft.leanCosting.productSchedu.entity.po;


import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

import com.yunhesoft.core.common.entity.BaseEntity;

import lombok.Getter;
import lombok.Setter;


/**
 * 核算对象运行状态表（点击开始记录当前核算对象的运行状态）
 * 
 */
@Entity
@Setter
@Getter
@Table(name = "UNITRUNSTATUS")
public class UnitRunStatus extends BaseEntity {
	
    private static final long serialVersionUID = 1L;

    
    /** 核算对象编码 */
    @Column(name="UNITID", length=100)
    private String unitid;
    
    /** 开始时间 */
    @Column(name="STARTDATETIME", length=20)
    private String startdatetime;
    
    /** 运行状态名称 */
    @Column(name="RUNSTATUSNAME", length=200)
    private String runstatusname;
    
    /** 运行状态编码 */
    @Column(name="RUNSTATUSID", length=100)
    private String runstatusid;
    
    /** 运行状态颜色 */
    @Column(name="RUNTYPECOLOR", length=200)
    private String runtypecolor;

}
