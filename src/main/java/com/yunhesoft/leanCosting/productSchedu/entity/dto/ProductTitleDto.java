package com.yunhesoft.leanCosting.productSchedu.entity.dto;

import java.util.List;


import com.yunhesoft.core.common.dto.BaseQueryDto;
import com.yunhesoft.leanCosting.productSchedu.entity.po.ProductScheduTitle;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class ProductTitleDto  extends BaseQueryDto{

    /** 核算对象编码 */
    @ApiModelProperty(value = "核算对象编码")
    private String unitId;
    
    /** 字段使用类型 */
    @ApiModelProperty(value = "字段使用类型")
    private Integer columnUseType;
    
    /** 表头数据 */
    @ApiModelProperty(value = "表头数据")
    private List<ProductScheduTitle> data;
}
