package com.yunhesoft.core.common.component;

import java.net.InetAddress;
import java.net.UnknownHostException;
import java.util.ArrayList;
import java.util.List;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import com.google.common.collect.Lists;

import lombok.extern.log4j.Log4j2;
import springfox.documentation.builders.ApiInfoBuilder;
import springfox.documentation.builders.OAuthBuilder;
import springfox.documentation.builders.PathSelectors;
import springfox.documentation.builders.RequestHandlerSelectors;
import springfox.documentation.service.ApiInfo;
import springfox.documentation.service.AuthorizationScope;
import springfox.documentation.service.GrantType;
import springfox.documentation.service.OAuth;
import springfox.documentation.service.ResourceOwnerPasswordCredentialsGrant;
import springfox.documentation.service.SecurityReference;
import springfox.documentation.service.SecurityScheme;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spi.service.contexts.SecurityContext;
import springfox.documentation.spring.web.plugins.Docket;

@Log4j2
@Component
public class Swagger2Utils {

	// api接口包扫描路径
	@Value("${swagger_scan_base_package:com.yunhesoft}")
	private String swagger_scan_base_package;
	@Value("${server.ip:0.0.0.0}")
	private String ip;

	@Value("${server.port:80}")
	private String port;

	@Value("${server.servlet.context-path:tm4main}")
	private String contextPath;

	@Value("${server.ssl.enabled:false}")
	private String sslEnabled;

	public ApiInfo apiInfo(String title, String description, String version) {
		return new ApiInfoBuilder()//
				.title(title) // 设置文档的标题
				.description(description) // 设置文档的描述
				.version(version) // 设置文档的版本信息-> 1.0.0 Version information
				.termsOfServiceUrl("http://www.yunhesoft.com") // 设置文档的License信息->1.3 License information
				.build();
	}

	public Docket newDocket(ApiInfo apiInfo, String groupName, String basePackage) {
		// schema
		List<GrantType> grantTypes = new ArrayList<>();
		// 密码模式
		String http = "http";
		if ("true".equalsIgnoreCase(sslEnabled)) {
			http = "https";
		}
		String passwordTokenUrl = http + "://" + getServerIp() + ":" + port + contextPath + "/auth/oauth2";
		ResourceOwnerPasswordCredentialsGrant resourceOwnerPasswordCredentialsGrant = new ResourceOwnerPasswordCredentialsGrant(
				passwordTokenUrl);
		grantTypes.add(resourceOwnerPasswordCredentialsGrant);
		OAuth oAuth = new OAuthBuilder().name("oauth2").grantTypes(grantTypes).build();
		// context
		// scope方位
		List<AuthorizationScope> scopes = new ArrayList<>();
		scopes.add(new AuthorizationScope("read", "read  resources"));
		scopes.add(new AuthorizationScope("write", "write resources"));
		scopes.add(new AuthorizationScope("reads", "read all resources"));
		scopes.add(new AuthorizationScope("writes", "write all resources"));

		SecurityReference securityReference = new SecurityReference("oauth2",
				scopes.toArray(new AuthorizationScope[] {}));
		SecurityContext securityContext = SecurityContext.builder()
				.securityReferences(Lists.newArrayList(securityReference)).forPaths(PathSelectors.regex("/.*")).build();
		// schemas
		List<SecurityScheme> securitySchemes = Lists.newArrayList(oAuth);
		// securyContext
		List<SecurityContext> securityContexts = Lists.newArrayList(securityContext);
//		String backages = "com.xiaominfo.knife4j.oauth2.web," + swagger_scan_base_package;
		return new Docket(DocumentationType.SWAGGER_2)//
				.apiInfo(apiInfo)//
				.groupName(groupName)//
				.select()//
//				.apis(basePackage(backages))//
				.apis(RequestHandlerSelectors.basePackage(basePackage)).paths(PathSelectors.any()) // 可以根据url路径设置哪些请求加入文档，忽略哪些请求
				.build()//
				.securityContexts(securityContexts)//
				.securitySchemes(securitySchemes);

	}

	private String getServerIp() {
		if ("0.0.0.0".equals(ip)) {
			try {
				ip = InetAddress.getLocalHost().getHostAddress();
			} catch (UnknownHostException e) {
				log.error("读取本机IP错误");
			}
		}
		return ip;
	}
}
