package com.yunhesoft.core.common.script;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.math.BigInteger;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import lombok.extern.log4j.Log4j2;

/**
 * 脚本解析引擎通用函数
 * 
 * <AUTHOR>
 *
 */
@Log4j2
public class ScriptEngineUtils {

	private static List<String> keyWords = null; // 变量中的关键字，如if,max,min,and,or,avg等
	private static String regStr = "['\"]?[0-9]*[#a-zA-Z_-]+[0-9]*[[.]*[0-9]*[#a-zA-Z_-]*[0-9]*]*['\"]?";

	/**
	 * eval 计算结果转换
	 * 
	 * @param obj
	 * @return
	 */
	public static Object parseNativeObject(Object obj) {

		if (obj == null) {
			return null;
		}
		try {
			// 判断结果对象是jdk6以上(不包含jdk6)的对象
			final Class<?> clshigh = Class.forName("jdk.nashorn.api.scripting.ScriptObjectMirror");
			if (clshigh.isAssignableFrom(obj.getClass())) {
				final Method isArray = clshigh.getMethod("isArray");
				final Object result = isArray.invoke(obj);
				if (result != null && result.equals(true)) {
					final Method values = clshigh.getMethod("values");
					final Object vals = values.invoke(obj);
					if (vals instanceof Collection<?>) {
						final Collection<?> coll = (Collection<?>) vals;
						return coll;
					}
					if (obj instanceof List<?>) {
						final List<?> list = (List<?>) obj;
						return list;
					}
					return obj;
				}
			}
		} catch (SecurityException e) {
			log.error("", e);
		} catch (IllegalArgumentException e) {
			log.error("", e);
		} catch (ClassNotFoundException e) {
			log.error("", e);
		} catch (NoSuchMethodException e) {
			log.error("", e);
		} catch (IllegalAccessException e) {
			log.error("", e);
		} catch (InvocationTargetException e) {
			log.error("", e);
		} catch (Exception e) {
			log.error("", e);
		}
		return obj;

	}

	/**
	 * @category 替换函数中的and、or为&&、||,四则运算时使用
	 * @param fun
	 * @return
	 */
	public static String replaceFunction(String fun) {
		if (fun == null) {
			return "";
		}
		String s = fun.replaceAll("if\\s*\\(", "iif(");// 替换if 为 iif
		s = s.replaceAll("int\\s*\\(", "parseInt\\(");// 修改解析方式替换函数
		s = formatScript(s);
		return s;
	}

	/**
	 * @category 替换函数中的and、or为&&、||,四则运算时使用(不替换IIF中的IF)
	 * @param fun
	 * @return
	 */
	public static String replaceFunctionWithOutIIF(String fun) {
		if (fun == null) {
			return "";
		}
		// 替换if 为 iif(使用零宽断言判断if前面没有i)
		String s = fun.replaceAll("(^|(?<=[^i]))if\\s*\\(", "iif(");
		s = s.replaceAll("int\\s*\\(", "parseInt\\(");// 修改解析方式替换函数
		s = formatScript(s);
		return s;
	}

	/**
	 * 格式化过滤条件脚本中不符合JS脚本的符号
	 * 
	 * @category 格式化脚本中不符合JS脚本的符号（用于四则运算及find条件的替换）
	 * @param script 脚本内容
	 * @return 处理后的脚本
	 */
	public static String formatFilterScript(String script) {
		if (script == null) {
			script = "";
		}
		Pattern p = Pattern.compile("=");
		Matcher m = p.matcher(script);
		script = m.replaceAll("==");
		p = Pattern.compile("\\band\\b", Pattern.CASE_INSENSITIVE);
		m = p.matcher(script);
		script = m.replaceAll("&&");
		p = Pattern.compile("\\bor\\b", Pattern.CASE_INSENSITIVE);
		m = p.matcher(script);
		script = m.replaceAll("||");
		p = Pattern.compile("<>");
		m = p.matcher(script);
		script = m.replaceAll("!=");
		p = Pattern.compile("'");
		m = p.matcher(script);
		script = m.replaceAll("\"");

		p = Pattern.compile("====");
		m = p.matcher(script);
		script = m.replaceAll("==");

		p = Pattern.compile(">==");
		m = p.matcher(script);
		script = m.replaceAll(">=");

		p = Pattern.compile("<==");
		m = p.matcher(script);
		script = m.replaceAll("<=");

		script = script.replaceAll("\\s+==", "==");// 替换==前后的空格
		script = script.replaceAll("==\\s+", "==");
		return script;
	}

	/**
	 * 格式化脚本中不符合JS脚本的符号
	 * 
	 * @category 格式化脚本中不符合JS脚本的符号（用于四则运算及find条件的替换）
	 * @param script 脚本内容
	 * @return 处理后的脚本
	 */
	public static String formatScript(String script) {
		if (script == null) {
			script = "";
		}
		Pattern p = Pattern.compile("=");
		Matcher m = p.matcher(script);
		script = m.replaceAll("==");

		p = Pattern.compile("<==");
		m = p.matcher(script);
		script = m.replaceAll("<=");

		p = Pattern.compile(">==");
		m = p.matcher(script);
		script = m.replaceAll(">=");

		p = Pattern.compile("!==");
		m = p.matcher(script);
		script = m.replaceAll("!=");

		p = Pattern.compile("====");
		m = p.matcher(script);
		script = m.replaceAll("==");

		p = Pattern.compile("\\band\\b", Pattern.CASE_INSENSITIVE);
		m = p.matcher(script);
		script = m.replaceAll("&&");

		p = Pattern.compile("\\bor\\b", Pattern.CASE_INSENSITIVE);
		m = p.matcher(script);
		script = m.replaceAll("||");

		p = Pattern.compile("<>");
		m = p.matcher(script);
		script = m.replaceAll("!=");

		p = Pattern.compile("\\+");
		m = p.matcher(script);
		script = m.replaceAll(" + ");

		p = Pattern.compile("--");
		m = p.matcher(script);
		script = m.replaceAll(" - - ");

		// script = script.replaceAll("\\s+==", "==");// 替换==前后的空格
		// script = script.replaceAll("==\\s+", "==");

		return script;
	}

	/**
	 * 解析公式中的自定义的变量映射表<br>
	 * 利用正则表达式将公式中的变量找出并放到一个列表中，值以null值表示
	 * 
	 * @category 解析公式中的自定义的变量映射表
	 * @param scriptStr
	 * @return
	 */
	public static Map<String, String> parseCustomVar(String scriptStr) {
		initKeyWord();
		Map<String, String> map = new HashMap<String, String>();
		Pattern p = Pattern.compile(regStr);
		Matcher m = p.matcher(scriptStr);
		while (m.find()) {
			String s = m.group();
			if (s.substring(0, 1).equals("-") && s.length() > 1)
				s = s.substring(1, s.length());
			if (!"-".equals(s) && !isKeyWord(s.toLowerCase())) {
				map.put(s, null);
			}
		}
		return map;
	}

	/**
	 * 根据外界填充好的映射表数据，替换公式中相应的变量
	 * 
	 * @category 根据外界填充好的映射表数据，替换公式中相应的变量
	 * @param scriptStr
	 * @param varMaps
	 * @return
	 */
	public static String replaceWithValue(String scriptStr, Map<String, String> varMaps) {
		if (scriptStr == null) {
			return null;
		}
		Pattern p = Pattern.compile(regStr);
		Matcher m = p.matcher(scriptStr);
		while (m.find()) {
			String s = m.group();
			if (s.substring(0, 1).equals("-") && s.length() > 1)
				s = s.substring(1, s.length());
			if (!"-".equals(s) && !isKeyWord(s.toLowerCase())) {
				String value = varMaps.get(s);
				if (value != null) {
					scriptStr = scriptStr.replace(s, value);
				}
			}
		}
		return scriptStr;
	}

	/**
	 * 判断是否是关键字
	 * 
	 * @category 判断是否是关键字
	 * @param key
	 * @return
	 */
	public static boolean isKeyWord(String key) {
		initKeyWord();
		return keyWords.contains(key.toLowerCase());
	}

	/**
	 * 初始化关键字列表
	 */
	private static void initKeyWord() {
		if (keyWords == null) {
			keyWords = new ArrayList<String>();
			keyWords.add("if");
			keyWords.add("else");
			keyWords.add("max");
			keyWords.add("min");
			keyWords.add("avg");
			keyWords.add("sum");
			keyWords.add("or");
			keyWords.add("and");
		}
	}

	/**
	 * 清除脚本中的变量($）
	 * 
	 * @param scriptString
	 * @return
	 */
	public static String clearScript(String scriptString) {
		if (scriptString == null) {
			return null;
		}
		Pattern p = Pattern.compile("[$]");
		Matcher m = p.matcher(scriptString);
		scriptString = m.replaceAll("");
		return scriptString;
	}

	/**
	 * 获取MD5值
	 * 
	 * @param str
	 * @return
	 */
	public static String getMd5(String str) {
		if (str == null) {
			return null;
		}
		try {
			MessageDigest md = MessageDigest.getInstance("MD5");
			md.update(str.getBytes());
			return (new BigInteger(1, md.digest()).toString(16)).toUpperCase();
		} catch (NoSuchAlgorithmException e) {
			log.error("MD5加密错误," + str, e);
		}
		return str.toUpperCase();
	}

	public static boolean isInteger(String str) {
		Pattern pattern = Pattern.compile("^[-\\+]?[\\d]*$");
		return pattern.matcher(str).matches();
	}

	public static boolean isString(String str) {
		if (str.startsWith("\"") && str.endsWith("\"")) {
			return true;
		}
		if (str.startsWith("'") && str.endsWith("'")) {
			return true;
		}
		return false;
	}

	/**
	 * 判断是否为bool
	 * 
	 * @param str
	 * @return
	 */
	public static Boolean isBool(String str) {
		if (isString(str)) {
			str = str.substring(1, str.length() - 1);
		}
		if ("true".equalsIgnoreCase(str)) {
			return true;
		}
		if ("false".equalsIgnoreCase(str)) {
			return false;
		}
		return null;
	}

	public static boolean isArray(String str) {
		if (str.startsWith("[") && str.endsWith("]")) {
			return true;
		}
		return false;
	}

	public static boolean isDouble(String str) {
		Pattern pattern = Pattern.compile("^[-\\+]?\\d*[.]\\d+$");
		return pattern.matcher(str).matches();
	}

	/**
	 * 常量的eval计算
	 * 
	 * @param s
	 * @return
	 */
	public static Object evalConst(String script) {
		Object obj = null;
		try {
			if (script != null && script.trim().length() > 0) {
				script = script.trim();
				if ("0.0".equals(script)) {
					return 0.0;
				}
				if ("0".equals(script)) {
					return 0;
				}
				Boolean bool = isBool(script);
				if (bool != null) {
					return bool;
				}
				if (isString(script)) {// 字符串
					return script.substring(1, script.length() - 1);
				}
				if (isArray(script)) {// 数组
					List<String> list = new ArrayList<String>();
					String s = script.substring(1, script.length() - 1);
					String[] ary = s.split(",");
					for (String ch : ary) {
						if (isString(ch)) {
							ch = ch.substring(1, ch.length() - 1);
						}
						list.add(ch);
					}
					return list;
				}
				if (isInteger(script)) {// 整数
					try {
						obj = Long.parseLong(script);
						return obj;
					} catch (Exception ex) {
						return script;
					}
				}
				if (isDouble(script)) {// 浮点数
					try {
						obj = Double.parseDouble(script);
						return obj;
					} catch (Exception ex) {
						return script;
					}
				}
			}
		} catch (Exception e) {
			log.error("", e);
		}
		return obj;
	}

}
