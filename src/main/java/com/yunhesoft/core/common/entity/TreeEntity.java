package com.yunhesoft.core.common.entity;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;

import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @category 树形实体基类
 * <AUTHOR>
 * 
 *         <pre>
 *		注:继承此类的实体类，必须实现一个子节点，类型为:List<T>
 *			变量名称建议为:children，否则需要在操作时，输入子节点集合的字段名
 *         </pre>
 */
@Data
@MappedSuperclass
public class TreeEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/** 唯一主键 */
	@Id
	@ApiModelProperty(value = "主键ID")
	@Column(name = "ID", length = 50)
	private String id;

	@ApiModelProperty(value = "父节点", notes = "如果查询时pid为空，则为系统默认的根:root")
	@Column(name = "PID")
	private String pid;

	@ApiModelProperty(value = "根ID", notes = "根节点的id值，必须", required = true)
	@Column(name = "MODEL_ID")
	private String modelId;

	@ApiModelProperty(value = "左编号", hidden = true)
	@Column(name = "LFT")
	private long lft;

	@ApiModelProperty(value = "右编号", hidden = true)
	@Column(name = "RGT")
	private long rgt;

	@ApiModelProperty(value = "删除标识", notes = "用于逻辑删除时所用 0 有效 1 已删除")
	@Column(name = "DEL_FLAG")
	private int delFlag;

	// @Column(name = "TENANT_ID", columnDefinition = "varchar(50) default '0'",
	// length = 50)
	// private String tenant_id = "0";

	/** 租户ID 开发人员不需要考虑 */
	// @ApiModelProperty(value = "租户编号", required = false, hidden = true)
	// @Column(name = "TENANT_UID", length = 50)
	// private String tenantUid = "sys";

	/** 开发人员不需要考虑 */
	@ApiModelProperty(value = "创建时间", required = false, hidden = true)
	@Column(name = "CREATE_TIME")
	@CreatedDate
	private Date createTime;

	/** 开发人员不需要考虑 */
	@ApiModelProperty(value = "创建人", required = false, hidden = true)
	@Column(name = "CREATE_BY", length = 50)
	@CreatedBy
	private String createBy;

	/** 开发人员不需要考虑 */
	@ApiModelProperty(value = "更新时间", required = false, hidden = true)
	@Column(name = "UPDATE_TIME")
	@LastModifiedDate
	private Date updateTime;

	/** 开发人员不需要考虑 */
	@ApiModelProperty(value = "更新人", required = false, hidden = true)
	@Column(name = "UPDATE_BY", length = 50)
	@LastModifiedBy
	private String updateBy;

	@ApiModelProperty(value = "创建人所属机构编码", required = false, hidden = true)
	@Column(name = "CREATE_BY_ORG", length = 50)
	private String createByOrg;

//	@ApiModelProperty(value = "创建人所属机构名称", required = false, hidden = true)
//	@Column(name = "CREATE_BY_ORGNAME", length = 255)
//	private String createByOrgName;

	@ApiModelProperty(value = "创建人岗位编码", required = false, hidden = true)
	@Column(name = "CREATE_BY_POST", length = 50)
	private String createByPost;

//	@ApiModelProperty(value = "创建人岗位名称", required = false, hidden = true)
//	@Column(name = "CREATE_BY_POSTNAME", length = 255)
//	private String createByPostName;
}
