package com.yunhesoft.core.common.aviator.function;

import java.util.Map;

import com.googlecode.aviator.runtime.function.AbstractFunction;
import com.googlecode.aviator.runtime.function.FunctionUtils;
import com.googlecode.aviator.runtime.type.AviatorDecimal;
import com.googlecode.aviator.runtime.type.AviatorObject;

/**
 * 正弦函数
 * 
 * sin(n)
 * 
 * <AUTHOR>
 *
 */
public class FunSin extends AbstractFunction {

	private static final long serialVersionUID = 1L;

	@Override
	public AviatorObject call(Map<String, Object> env, AviatorObject arg1) {
		Number value = FunctionUtils.getNumberValue(arg1, env);
		if (value != null) {
			double val = value.doubleValue();
			val = Math.sin(val);
			return AviatorDecimal.valueOf(val);
		} else {
			return AviatorDecimal.valueOf(0);
		}
	}

	/**
	 * 函数名
	 */
	@Override
	public String getName() {
		return "sin";
	}

}
