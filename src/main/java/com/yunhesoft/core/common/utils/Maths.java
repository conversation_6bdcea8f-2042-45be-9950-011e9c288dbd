package com.yunhesoft.core.common.utils;

import java.math.BigDecimal;
import java.text.DecimalFormat;

import lombok.extern.log4j.Log4j2;

/**
 * 常用的数学函数 by x.zhong
 */
@Log4j2
public class Maths {

	/**
	 * 默认数字格式
	 * 
	 * @return
	 */
	private static String getDefaultNumFormat() {
		return "#.####################";
	}

	/**
	 * 绝对值
	 * 
	 * @param d
	 * @return
	 */
	public static double abs(double d) {
		return Math.abs(d);
	}

	/**
	 * 绝对值
	 * 
	 * @param d
	 * @return
	 */
	public static float abs(float d) {
		return Math.abs(d);
	}

	/**
	 * 绝对值
	 * 
	 * @param d
	 * @return
	 */
	public static int abs(int d) {
		return Math.abs(d);
	}

	/**
	 * 绝对值
	 * 
	 * @param d
	 * @return
	 */
	public static long abs(long d) {
		return Math.abs(d);
	}

	/**
	 * 求两个数中的最大值。
	 * 
	 * @return
	 */
	public static int max(int a, int b) {
		return Math.max(a, b);
	}

	/**
	 * 求两个数中的最大值。
	 * 
	 * @return
	 */
	public static long max(long a, long b) {
		return Math.max(a, b);
	}

	/**
	 * 求两个数中的最大值。
	 * 
	 * @return
	 */
	public static double max(double a, double b) {
		return Math.max(a, b);
	}

	/**
	 * 求两个数中的最小值。
	 * 
	 * @return
	 */
	public static int min(int a, int b) {
		return Math.min(a, b);
	}

	/**
	 * 求两个数中的最小值。
	 * 
	 * @return
	 */
	public static long min(long a, long b) {
		return Math.min(a, b);
	}

	/**
	 * 求两个数中的最小值。
	 * 
	 * @return
	 */
	public static double min(double a, double b) {
		return Math.min(a, b);
	}

	/**
	 * 截取数字
	 * 
	 * @param d
	 * @param n 截取位数
	 * @return
	 */
	public static double truncate(double d, int n) {
		double value = d;
		if (n >= 0) {
			if (n == 0) {// 截取整数部分
				value = floor(d);
			} else {
				double parmNum = Math.pow(10, n);
				return floor(d * parmNum) / parmNum;
			}
		}
		return value;
	}

	/**
	 * 四舍五入
	 * 
	 * @param ld
	 * @param n
	 * @return
	 */
	public static double round(double d, int n) {
		if (n <= 0) {
			return round(d);
		} else {
			double parmNum = Math.pow(10, n);
			return Math.round(d * parmNum) / parmNum;
		}
	}

	/**
	 * 四舍五入（取整）
	 * 
	 * @param d
	 * @return
	 */
	public static double round(double d) {
		return Math.round(d);
	}

	/**
	 * 取小数整数部分(小数点后10位四舍五入取整)
	 * 
	 * @param ld
	 */
	public static int parseInt(double ld) {
		ld = round(ld, 10);
		return (int) Math.floor(ld);
	}

	/**
	 * 取小数整数部分
	 * 
	 * @param ld
	 */

	public static int floor(double ld) {
		return (int) Math.floor(ld);
	}

	/**
	 * 向上取整
	 * 
	 * @param ld
	 * @return
	 */
	public static int ceil(double ld) {
		return (int) Math.ceil(ld);
	}

	/**
	 * 取余数
	 * 
	 * @param x
	 * @param y
	 * @return
	 */
	public static int mod(int x, int y) {
		return (x % y);
	}

	/**
	 * 格式化数据
	 * 
	 * @param d double数值
	 * @param s 格式，为空默认###.0
	 * @return 字符串
	 */
	public static String format(double d, String s) {
		String value = "";
		try {
			DecimalFormat df = new DecimalFormat(s == null ? "###.0" : s);
			df.setRoundingMode(java.math.RoundingMode.valueOf(4));
			value = df.format(d);
		} catch (Exception e) {
			log.error("", e);
			value = String.valueOf(d);
		}
		return value;
	}

	/**
	 * 格式化数据
	 * 
	 * @param d double数值
	 * @param s 格式，为空默认###.0
	 * @return 字符串
	 */
	public static String format(float d, String s) {
		String value = "";
		try {
			DecimalFormat df = new DecimalFormat(s == null ? "###.0" : s);
			df.setRoundingMode(java.math.RoundingMode.valueOf(4));
			value = df.format(d);
		} catch (Exception e) {
			log.error("", e);
			value = String.valueOf(d);
		}
		return value;
	}

	/**
	 * 格式化数据（四舍五入）
	 * 
	 * @param d double数值
	 * @param s 格式，为空默认###.0
	 * @return 字符串
	 */
	public static String format(Object d, String s) {
		String value = "";
		try {
			DecimalFormat df = new DecimalFormat(s == null ? "###.0" : s);
			df.setRoundingMode(java.math.RoundingMode.valueOf(4));
			value = df.format(d);
		} catch (Exception e) {
			log.error("", e);
			value = d.toString();
		}
		return value;

	}

	/**
	 * 格式化数据（不四舍五入，多余部分直接舍去）
	 * 
	 * @param d double数值
	 * @param s 格式，为空默认###.0
	 * @return 字符串
	 */
	public static String format_down(Object d, String s) {
		String value = "";
		try {
			DecimalFormat df = new DecimalFormat(s == null ? "###.0" : s);
			df.setRoundingMode(java.math.RoundingMode.FLOOR);
			value = df.format(d);
		} catch (Exception e) {
			log.error("", e);
			value = d.toString();
		}
		return value;

	}

	/**
	 * doulbe 类型数据个数，防止出现科学计数法
	 * 
	 * @param d
	 * @return String
	 */
	public static String formatNum(Object d) {
		String value = "";
		try {
			String f = getDefaultNumFormat();
			if (isNumType(d)) {// 数值类型
				value = format(d, f);
			} else {
				if (d == null || d.toString().length() == 0) {
					value = "0";
				} else {
					double d1 = Double.parseDouble(d.toString());
					value = format(d1, f);
				}
			}
		} catch (Exception e) {
			value = d.toString();
			log.error("", e);
		}
		return value;
	}

	/**
	 * doulbe 类型数据个数，防止出现科学计数法
	 * 
	 * @param d
	 * @return String
	 */
	public static String formatNum(double d) {
		String value = "";
		try {
			value = format(d, getDefaultNumFormat());
		} catch (Exception e) {
			value = String.valueOf(d);
			log.error("", e);
		}
		return value;

	}

	/**
	 * 科学计数法转换为正常的数字
	 * 
	 * @param d “1.238761976E-10 ”
	 * @return 0.0000000001238761976
	 */
	public static String formatNum(String d) {
		if (d == null || "null".equals(d) || d.length() == 0)
			return d;
		String value = "";
		try {
			Object obj = (Object) d;
			value = formatNum(obj);
		} catch (Exception e) {
			log.error("", e);
			value = d;
		}
		return value;
	}

	/**
	 * 判断 Object 对象是否是数值型
	 * 
	 * @param d
	 * @return
	 */
	public static boolean isNumType(Object d) {
		if (d == null)
			return false;
		boolean bln = false;
		try {
			String sn = d.getClass().getSimpleName();
			if ("BigDecimal".equals(sn) || "Double".equals(sn) || "Integer".equals(sn) || "Long".equals(sn)
					|| "Float".equals(sn) || "Byte".equals(sn) || "Short".equals(sn)) {
				bln = true;
			} else if ("String".equals(sn) || "Timestamp".equals(sn) || "Boolean".equals(sn) || "Date".equals(sn)
					|| "Character".equals(sn)) {
				bln = false;
			}
		} catch (Exception e) {
			log.error("", e);
		}
		return bln;
	}

	/**
	 * float 类型数据个数，防止出现科学计数法
	 * 
	 * @param d
	 * @return String
	 */
	public static String formatNum(float d) {
		String value = "";
		try {
			value = format(d, getDefaultNumFormat());
		} catch (Exception e) {
			log.error("", e);
			value = String.valueOf(d);
		}
		return value;

	}

	/**
	 * 四舍六入（计量专用）
	 * 
	 * @param ld 数值
	 * @param n  保留小数位数
	 * @return
	 */
	public static double roundx(double d, int n) {
		if (n < 0)
			return d;
		double value = d;
		try {
			StringBuffer sb = new StringBuffer();
			for (int i = 0; i < n + 3; i++) {
				sb.append("0");
			}
			String ls_value = format(truncate(d, n + 2), "#." + sb.toString());
			int li_pos = ls_value.indexOf(".");
			if (li_pos >= 0) {
				// 查找被修约的数字；例如 roundx(0.12345,2) li_num = 3
				int li_num = Integer.parseInt(ls_value.substring(li_pos + n + 1, li_pos + n + 2));
				if (li_num == 5) {
					/**
					 * 被修约的数字等于5时，要看5前面的数字，若是奇数则进位，若是偶数则将5舍掉，
					 * 即修约后末尾数字都成为偶数；若5的后面还有不为“0”的任何数，则此时无论5的前面是奇数还是偶数，均应进位
					 */
					String s = ls_value.substring(li_pos + n, li_pos + n + 1);
					if (".".equals(s)) {
						s = ls_value.substring(li_pos - 1, li_pos);
					}
					int li_num_last = Integer.parseInt(s);
					if (mod(li_num_last, 2) == 0) {// 偶数 ，舍去
						value = truncate(d, n);
					} else {// 奇数，进位
						value = round(d, n);
					}
				} else {// 正常，四舍五入
					value = round(d, n);
				}
			}
		} catch (Exception e) {
			log.error("", e);
			value = round(d, n);
		}
		return value;
	}

	// --------------------------------------精确计算小数精度start--------------------------------------
	private static final int DEF_DIV_SCALE = 10;

	/**
	 * 相加
	 * 
	 * @param d1
	 * @param d2
	 * @return
	 */
	public static double add(double d1, double d2) {
		BigDecimal b1 = new BigDecimal(Maths.format(d1, "###.##########"));
		BigDecimal b2 = new BigDecimal(Maths.format(d2, "###.##########"));
		return b1.add(b2).doubleValue();

	}

	/**
	 * 相减
	 * 
	 * @param d1
	 * @param d2
	 * @return
	 */
	public static double sub(double d1, double d2) {
		BigDecimal b1 = new BigDecimal(Maths.format(d1, "###.##########"));
		BigDecimal b2 = new BigDecimal(Maths.format(d2, "###.##########"));
		return b1.subtract(b2).doubleValue();

	}

	/**
	 * 相乘
	 * 
	 * @param d1
	 * @param d2
	 * @return
	 */
	public static double mul(double d1, double d2) {
		BigDecimal b1 = new BigDecimal(Maths.format(d1, "###.##########"));
		BigDecimal b2 = new BigDecimal(Maths.format(d2, "###.##########"));
		return b1.multiply(b2).doubleValue();

	}

	/**
	 * 相除
	 * 
	 * @param d1
	 * @param d2
	 * @return
	 */
	public static double div(double d1, double d2) {
		return div(d1, d2, DEF_DIV_SCALE);
	}

	public static double div(double d1, double d2, int scale) {
		if (scale < 0) {
			throw new IllegalArgumentException("The scale must be a positive integer or zero");
		}
		BigDecimal b1 = new BigDecimal(Maths.format(d1, "###.##########"));
		BigDecimal b2 = new BigDecimal(Maths.format(d2, "###.##########"));
		return b1.divide(b2, scale, BigDecimal.ROUND_HALF_UP).doubleValue();

	}

	public static Object formatVal(Object ld) {
		if (ld == null) {
			return null;
		}
		if (isNumType(ld)) {
			try {
				String sn = ld.getClass().getSimpleName();
				if ("BigDecimal".equalsIgnoreCase(sn)) {
					BigDecimal d = (BigDecimal) ld;
					if (d.doubleValue() == 0d) {
						return null;
					} else {
						return Maths.formatNum(d);
					}
				} else if ("Integer".equalsIgnoreCase(sn)) {
					Integer d = (Integer) ld;
					if (d.intValue() == 0) {
						return null;
					} else {
						return d;
					}
				} else if ("Long".equalsIgnoreCase(sn)) {
					Long d = (Long) ld;
					if (d.intValue() == 0) {
						return null;
					} else {
						return d;
					}
				} else {
					Double d = Double.parseDouble(ld.toString());
					if (d.doubleValue() == 0d) {
						return null;
					} else {
						return Maths.formatNum(d);
					}
				}
			} catch (Exception e) {
				log.error("", e);
				return ld;
			}
		} else {
			return ld;
		}

	}

	// --------------------------------------精确计算小数精度stop--------------------------------------
}
