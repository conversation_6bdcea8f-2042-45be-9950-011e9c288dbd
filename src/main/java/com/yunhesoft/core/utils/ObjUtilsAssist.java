package com.yunhesoft.core.utils;

import java.util.ArrayList;
import java.util.List;

import lombok.Data;

@Data
public class ObjUtilsAssist {
	List<Object> srcs = new ArrayList<Object>();
	List<Object> dess = new ArrayList<Object>();

	public void add(Object src, Object des) {
		srcs.add(src);
		dess.add(des);
	}

	public Object get(Object src) {
		for (int i = 0; i < srcs.size(); i++) {
			if (srcs.get(i).equals(src)) {
				return dess.get(i);
			}
		}
		return null;
	}
}