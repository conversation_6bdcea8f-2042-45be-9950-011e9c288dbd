package com.yunhesoft.syslog.service.impl;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import com.yunhesoft.core.common.model.Res;
import com.yunhesoft.core.common.model.SysUser;
import com.yunhesoft.core.common.utils.DateTimeUtils;
import com.yunhesoft.core.common.utils.TMUID;
import com.yunhesoft.core.utils.ObjUtils;
import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.syslog.entity.dto.OperationLogAdd;
import com.yunhesoft.syslog.entity.dto.OperationLogQuery;
import com.yunhesoft.syslog.entity.po.OperationLogInfo;
import com.yunhesoft.syslog.service.LogManageService;
import com.yunhesoft.syslog.utils.ControllerFunctionMap;
import com.yunhesoft.syslog.utils.LogOperType;
import com.yunhesoft.system.kernel.config.SysUserHolder;
import com.yunhesoft.system.kernel.service.SysUserUtil;
import com.yunhesoft.system.kernel.utils.mongodb.service.MongoDBService;

import lombok.extern.log4j.Log4j2;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;

@Log4j2
@Service
public class LogServiceImpl implements LogManageService {

	@Autowired
	private MongoDBService mongodbServ;

	private static int EXPIRE_DAY = 180; // 日志保存多少天
	private static int RESPONSE_DATA_MAX_LENGTH = 204800; //应答数据最大存储字符个数，超出截断（200K）
	private static int REQUEST_DATA_MAX_LENGTH = 204800; //请求数据最大存储字符个数，超出截断（200K）

	@Value("${syslog.data.longTruncateCharNum:4000}")
	public Integer longTruncateCharNum;

	/**
	 * 添加操作日志
	 */
	@Override
	public void addOperLog(OperationLogAdd dto) {
		try {
			OperationLogInfo oplog = this.getOpLog(dto);
			mongodbServ.insert(oplog);// 添加到mongodb
			// System.out.println("*添加成功:" + dto.getId());
		} catch (Exception e) {
			// System.out.println("*添加失败:" + dto.getId());
			log.error("", e);
		}

	}

	/**
	 * @category 异步记录日志
	 */
	@Async
	@Override
	public void saveOperLogAsync(OperationLogAdd dto) {
		try {
			OperationLogInfo oplog = this.getOpLog(dto);
			// System.out.println("**saveAsyncMdb:" + oplog.getId());
			mongodbServ.save(oplog);// 添加到mongodb
		} catch (Exception e) {
			log.error("", e);
		}
	}

	/**
	 * 查找日志查询
	 */
	@Override
	public Res<List<OperationLogInfo>> queryOperLog(OperationLogQuery queryDto) {
		Res<List<OperationLogInfo>> res = new Res<List<OperationLogInfo>>();
		// query设置查询条件
		Criteria criteria = new Criteria();
		if (StringUtils.isNotEmpty(queryDto.getModuleName())) {// 模块
			criteria.and("moduleName").regex(".*" + queryDto.getModuleName() + ".*");
		}
		if (StringUtils.isNotEmpty(queryDto.getOperator())) {// 操作人
			// criteria.and("createByName").is(queryDto.getOperName());
			criteria.and("createByName").regex(".*" + queryDto.getOperator() + ".*");
		}

		if (StringUtils.isNotEmpty(queryDto.getMenuName())) {// 功能菜单 模糊检索
			criteria.and("menuName").regex(".*" + queryDto.getMenuName() + ".*");
		}

		if (StringUtils.isNotEmpty(queryDto.getOpKeywords())) {// 操作关键字模糊检索
			criteria.and("operation").regex(".*" + queryDto.getOpKeywords() + ".*");
		}

		if (queryDto.getOperType() != null) {// 操作类型
			criteria.and("operType").is(queryDto.getOperType());
		}

		if (StringUtils.isNotEmpty(queryDto.getRequestData())) { // 请求数据模糊检索
			criteria.and("requestData").regex(".*" + queryDto.getRequestData() + ".*");
		}

		if (StringUtils.isNotEmpty(queryDto.getResponseData())) { // 应答数据模糊检索
			criteria.and("responseData").regex(".*" + queryDto.getResponseData() + ".*");
		}

		// 查询时间段
		String startDt = queryDto.getStartDt();
		String endDt = queryDto.getEndDt();

		if (StringUtils.isEmpty(startDt)) {
			startDt = DateTimeUtils.getDate();
		}
		if (StringUtils.isEmpty(endDt)) {
			endDt = DateTimeUtils.getDate();
		}
		if (startDt.length() == 10) {
			startDt = startDt + " 00:00:00";
		}
		if (endDt.length() == 10) {
			endDt = endDt + " 23:59:59";
		}
		Date d1 = DateTimeUtils.parseDateTime(startDt);
		Date d2 = DateTimeUtils.parseDateTime(endDt);
		criteria.and("createTimestamp").gte(d1.getTime()).lte(d2.getTime());// 大于等于

		// 排序
		Sort sort = Sort.by("createTimestamp").descending(); // 创建时间倒序排列
		int pageNum = 0;// 当前页 0：第一页
		int pageSize = 100;// 每页数量
		if (queryDto.getPageNum() != null && queryDto.getPageNum() > 0) {
			pageNum = queryDto.getPageNum() - 1;
		}
		if (queryDto.getPageSize() != null && queryDto.getPageSize() > 0) {
			pageSize = queryDto.getPageSize();
		}
		PageRequest page = PageRequest.of(pageNum, pageSize, sort);

		Query query = new Query(criteria);
		query.with(sort);// 排序
		query.with(page);// 分页

		List<OperationLogInfo> list = mongodbServ.find(query, OperationLogInfo.class);
		SysUser user = SysUserHolder.getCurrentUser();
		if (user != null && SysUserUtil.isAdmin(user.getId())) {
			for (OperationLogInfo info : list) {
				if (longTruncateCharNum > 0 && StringUtils.isNotEmpty(info.getRequestData()) && info.getRequestData().length() > longTruncateCharNum) {
					info.setRequestData(info.getRequestData().substring(0, longTruncateCharNum));
					info.setRequestDataTruncated(true);
				}
				if (longTruncateCharNum > 0 && StringUtils.isNotEmpty(info.getResponseData()) && info.getResponseData().length() > longTruncateCharNum) {
					info.setResponseData(info.getResponseData().substring(0, longTruncateCharNum));
					info.setResponseDataTruncated(true);
				}
			}
		} else {
			for (OperationLogInfo info : list) {
				info.setRequestUri(info.getRequestUri().replaceFirst("[\\w\\d]{1,}(\\/)", "**/"));

				if (longTruncateCharNum > 0 && StringUtils.isNotEmpty(info.getRequestData()) && info.getRequestData().length() > longTruncateCharNum) {
					info.setRequestData(info.getRequestData().substring(0, longTruncateCharNum));
					info.setRequestDataTruncated(true);
				}
				if (longTruncateCharNum > 0 && StringUtils.isNotEmpty(info.getResponseData()) && info.getResponseData().length() > longTruncateCharNum) {
					info.setResponseData(info.getResponseData().substring(0, longTruncateCharNum));
					info.setResponseDataTruncated(true);
				}
			}
		}
		// 查找数量
		Query queryCount = new Query(criteria);
		long total = mongodbServ.count(queryCount, OperationLogInfo.class);
		res.setTotal(total);
		res.setResult(list);
		return res;
	}

	/**
	 * 操作日志对象转换
	 * 
	 * @param dto
	 * @return
	 */
	private OperationLogInfo getOpLog(OperationLogAdd dto) {
		OperationLogInfo oplog = new OperationLogInfo();
		ObjUtils.copyTo(dto, oplog);

		//请求数据
		String requestData = oplog.getRequestData();
		if (StringUtils.isNotEmpty(requestData)) {
			//超出最大字符数截断，解决MongoDB排序超出内存上限（100MB）报错问题，此最大字符数目前可保障每页500条左右记录不会超出是内存上限
			if (requestData.length() > REQUEST_DATA_MAX_LENGTH) {
				oplog.setRequestData(requestData.substring(0, REQUEST_DATA_MAX_LENGTH));
			}
		}
		//应答数据
		String responseData = oplog.getResponseData();
		if (StringUtils.isNotEmpty(responseData)) {
			//超出最大字符数截断，解决MongoDB排序超出内存上限（100MB）报错问题，此最大字符数目前可保障每页500条左右记录不会超出是内存上限
			if (responseData.length() > RESPONSE_DATA_MAX_LENGTH) {
				oplog.setResponseData(responseData.substring(0, RESPONSE_DATA_MAX_LENGTH));
			}
		}

		if (oplog.getOperType() != null) {
			oplog.setOperTypeName(this.getOperTypeName(oplog.getOperType()));
		}
		oplog.setCreateTime(DateTimeUtils.getNowDateTimeStr());
		oplog.setCreateTimestamp(new Date().getTime());
		oplog.setExpireAt(this.getExpireAt(EXPIRE_DAY));// 日志失效日期
		SysUser user = SysUserHolder.getCurrentUser();
		if (user != null) {
			oplog.setCreateBy(user.getId());// 用户ID
			oplog.setCreateByName(user.getRealName());// 用户名
			oplog.setTenant_id(user.getTenant_id());// 租户id
			oplog.setOrgName(user.getOrgName());
			oplog.setOrgCode(user.getOrgId());
			oplog.setLoginName(user.getUserName());// 登陆账号
			oplog.setOSVer(user.getOSVer());// 操作系统版本
			oplog.setExplorerVer(user.getExplorerVer());// 浏览器版本
			oplog.setIpAddress(user.getIpAddress());// ip地址
		}
		if (StringUtils.isEmpty(oplog.getMenuName())) {
			oplog.setMenuName(ControllerFunctionMap.getFunctionName(dto.getRequestUri())); //功能名称
		}
		if (ObjUtils.isEmpty(oplog.getId())) {
			oplog.setId(TMUID.getUID());
		}
		return oplog;
	}

	/**
	 * 获得日志操作类型
	 * 
	 * @param operType
	 * @return
	 */
	private String getOperTypeName(int operType) {
		String name = "其他";
		if (operType == LogOperType.INSERT) {
			name = "添加";
		} else if (operType == LogOperType.DELETE) {
			name = "删除";
		} else if (operType == LogOperType.UPDATE) {
			name = "修改";
		} else if (operType == LogOperType.QUERY) {
			name = "查询";
		}
		return name;
	}

	/**
	 * 获得失效时间
	 * 
	 * @return
	 */
	private Date getExpireAt(int day) {
		return DateTimeUtils.doDate(new Date(), day);
	}

	/**
	 * 导出完整内容文件
	 * @param queryDto
	 * @param response
	 */
	@Override
	public String exportFile(OperationLogQuery queryDto, HttpServletResponse response) throws IOException {
		String error = "";
		String fileName = "完整内容.txt";
		String content = "";

		String id = queryDto.getId();
		if (StringUtils.isEmpty(id)) {
			return "日志记录ID无效";
		}
		String exportField = queryDto.getExportField();
		if (StringUtils.isEmpty(exportField)) {
			return "指定导出记录字段无效";
		}

		//通过记录ID获取数据
		Criteria criteria = new Criteria();
		criteria.and("_id").is(id);
		Query query = new Query(criteria);
		List<OperationLogInfo> list = mongodbServ.find(query, OperationLogInfo.class);
		if (ObjUtils.isEmpty(list)) {
			return "日志记录不存在（ID：" + id + "）";
		}
		OperationLogInfo operationLogInfo = list.get(0);
		if ("requestData".equals(exportField)) {
			content = operationLogInfo.getRequestData();
			if (StringUtils.isEmpty(content)) {
				return "请求数据内容无效";
			}
		} else if ("responseData".equals(exportField)) {
			content = operationLogInfo.getResponseData();
			if (StringUtils.isEmpty(content)) {
				return "应答数据内容无效";
			}
		}

		try (InputStream inputStream = new ByteArrayInputStream(content.getBytes()); ServletOutputStream outputStream = response.getOutputStream();) {
			response.setContentType("application/octet-stream");
			response.setHeader("Content-Disposition", "attachment; filename=\"" + fileName + "\"");
			byte[] buffer = new byte[1024];
			int len;
			while ((len = inputStream.read(buffer)) > 0) {
				outputStream.write(buffer, 0, len);
			}
			outputStream.flush();
			//outputStream.close();
			//inputStream.close();
		}

		return error;
	}
}
