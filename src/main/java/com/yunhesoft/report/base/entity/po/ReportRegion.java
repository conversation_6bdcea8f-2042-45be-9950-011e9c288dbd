package com.yunhesoft.report.base.entity.po;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

import com.yunhesoft.core.common.entity.BaseEntity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


@Entity
@Data
@ApiModel("报表管理区域")
@Table(name = "REPORT_REGION")
public class ReportRegion extends BaseEntity {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "报表ID", example = "报表ID1")
    @Column(name = "REPORT_ID", length = 50)
    private String reportId;

    @ApiModelProperty(value = "分布区域", notes = "row=行区 col=列区 measure=度量")
    @Column(name = "REGION", length = 50)
    private String region;

    @ApiModelProperty(value = "绑定参数", example = "绑定参数1")
    @Column(name = "BIND_PARAM", length = 200)
    private String bindParam;

    @ApiModelProperty(value = "绑定参数别名", example = "绑定参数别名1")
    @Column(name = "BIND_PARAM_ALIAS", length = 200)
    private String bindParamAlias;

    @ApiModelProperty(value = "参数类型", notes = "1=数据源输出参数 2=Σ度量组")
    @Column(name = "PARAM_TYPE")
    private Integer paramType;

    @ApiModelProperty(value = "排序方式", notes = "asc=升序 desc=降序 field_asc=指定字段升序 field_desc=指定字段降序")
    @Column(name = "ORDER_BY_WAY", length = 50)
    private String orderByWay;

    @ApiModelProperty(value = "排序字段", example = "排序字段1")
    @Column(name = "ORDER_BY_FIELD", length = 200)
    private String orderByField;

    @ApiModelProperty(value = "排序字段别名", example = "排序字段别名1")
    @Column(name = "ORDER_BY_FIELD_ALIAS", length = 200)
    private String orderByFieldAlias;

    @ApiModelProperty(value = "统计方式", notes = "sum=合计值 max=最大值 min=最小值 avg=平均值 count=计数值")
    @Column(name = "STATISTIC_WAY", length = 50)
    private String statisticWay;

    @ApiModelProperty(value = "排序号", example = "123")
    @Column(name = "TMSORT")
    private Long tmsort;

    @ApiModelProperty(value = "可用标识", notes = "1=有效 0=无效")
    @Column(name = "TMUSED")
    private Integer tmused;

    @ApiModelProperty(value = "数据类型", notes = "tdsString=字符型 tdsDouble=数值型")
    @Column(length = 50)
    private String datatype;

    @ApiModelProperty(value = "排序字段数据类型", notes = "tdsString=字符型 tdsDouble=数值型")
    @Column(name = "ORDER_BY_DATATYPE", length = 50)
    private String orderByDatatype;

    @ApiModelProperty(value = "列宽", example = "100")
    @Column(name = "COL_WIDTH")
    private Integer colWidth;

    @ApiModelProperty(value = "列对齐方式", notes = "left=居左 right=居右 center=居中", example = "center")
    @Column(name = "COL_ALIGN", length = 50)
    private String colAlign;

    @ApiModelProperty(value = "列值保留小数位数", notes = "小数位数", example = "2")
    @Column(name = "COL_SCALE")
    private Integer colScale;

    @ApiModelProperty(value="是否锁定列", example="只针对行区列表头有效，1=锁定 0=不锁定")
    @Column(name="COL_FIXED")
    private Integer colFixed;
    
    @ApiModelProperty(value = "是否用于分析图", notes = "1=用于 0=不用")
    @Column(name = "ANALYSIS_FIELD")
    private Integer analysisField;
}
