package com.yunhesoft.report.base.entity.vo;

import com.yunhesoft.report.base.entity.po.ReportQuery;
import com.yunhesoft.report.base.entity.po.ReportRegion;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel("报表详细信息返回实体类")
public class QueryReportManageInfoVo {

    @ApiModelProperty(value = "报表id", notes = "报表id")
    private String reportId;

    @ApiModelProperty(value = "条件数据列表", notes = "条件数据列表")
    private List<ReportQuery> reportQueryList;

    @ApiModelProperty(value = "行区数据列表", notes = "行区数据列表")
    private List<ReportRegion> reportRegionRowList;

    @ApiModelProperty(value = "列区数据列表", notes = "列区数据列表")
    private List<ReportRegion> reportRegionColList;

    @ApiModelProperty(value = "度量数据列表", notes = "度量数据列表")
    private List<ReportRegion> reportRegionMeasureList;

}
