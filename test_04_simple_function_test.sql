-- 测试4：简化版存储过程测试
-- 创建一个简化的测试版本来验证逻辑

-- 替换这里的参数值进行测试
\set p_objid 'YOUR_OBJID_HERE'
\set p_datetime '2024-01-15 14:30:00'
\set p_acctobj_id 'YOUR_ACCTOBJ_ID_HERE'  -- 可选，如果不需要过滤可以留空

-- 创建临时测试函数
CREATE OR REPLACE FUNCTION test_get_previous_shift_data(
    p_objid VARCHAR(200),
    p_datetime VARCHAR(19) DEFAULT NULL,
    p_acctobj_id VARCHAR(200) DEFAULT NULL
)
RETURNS TABLE (
    step_info VARCHAR,
    collect_point_id VARCHAR,
    ipt_id VARCHAR,
    collect_point_text VARCHAR,
    collect_point_val VARCHAR,
    shift_class_id VARCHAR,
    shift_class_name VARCHAR,
    shift_start_time VARCHAR,
    shift_end_time VARCHAR,
    shift_date VARCHAR
) AS $$
DECLARE
    query_datetime VARCHAR(19);
    query_date VARCHAR(10);
    query_time VARCHAR(8);
    parent_orgcode VARCHAR(200);
    current_shift_found BOOLEAN := FALSE;
    previous_shift_found BOOLEAN := FALSE;
    debug_info TEXT := '';
BEGIN
    -- 处理输入参数
    IF p_datetime IS NULL OR LENGTH(TRIM(p_datetime)) = 0 THEN
        query_datetime := TO_CHAR(CURRENT_TIMESTAMP, 'YYYY-MM-DD HH24:MI:SS');
    ELSE
        query_datetime := p_datetime;
    END IF;

    query_date := SUBSTRING(query_datetime FROM 1 FOR 10);
    query_time := SUBSTRING(query_datetime FROM 12 FOR 8);

    -- 通过objid查找父级机构代码
    SELECT porgcode INTO parent_orgcode
    FROM sys_org_relation 
    WHERE orgcode = p_objid AND used = 1;
    
    -- 如果没有找到父级机构，先通过objid查找班次记录获取orgcode
    IF parent_orgcode IS NULL THEN
        SELECT orgcode INTO parent_orgcode
        FROM shift_data
        WHERE objid = p_objid
        LIMIT 1;
    END IF;

    -- 返回调试信息
    RETURN QUERY SELECT 
        'DEBUG: 参数信息'::VARCHAR as step_info,
        ('objid=' || p_objid || ', datetime=' || query_datetime || ', parent_orgcode=' || COALESCE(parent_orgcode, 'NULL'))::VARCHAR as collect_point_id,
        ''::VARCHAR as ipt_id,
        ''::VARCHAR as collect_point_text,
        ''::VARCHAR as collect_point_val,
        ''::VARCHAR as shift_class_id,
        ''::VARCHAR as shift_class_name,
        ''::VARCHAR as shift_start_time,
        ''::VARCHAR as shift_end_time,
        ''::VARCHAR as shift_date;

    -- 检查是否找到父级机构
    IF parent_orgcode IS NULL THEN
        RETURN QUERY SELECT 
            'ERROR: 未找到父级机构'::VARCHAR as step_info,
            ''::VARCHAR as collect_point_id,
            ''::VARCHAR as ipt_id,
            ''::VARCHAR as collect_point_text,
            ''::VARCHAR as collect_point_val,
            ''::VARCHAR as shift_class_id,
            ''::VARCHAR as shift_class_name,
            ''::VARCHAR as shift_start_time,
            ''::VARCHAR as shift_end_time,
            ''::VARCHAR as shift_date;
        RETURN;
    END IF;

    -- 检查当前班次
    IF EXISTS (
        SELECT 1 FROM shift_data
        WHERE orgcode = parent_orgcode
          AND objid = p_objid
          AND (
              (dbrq = query_date AND query_time BETWEEN sbsj AND xbsj)
              OR
              (dbrq = TO_CHAR(TO_DATE(query_date, 'YYYY-MM-DD') - INTERVAL '1 day', 'YYYY-MM-DD')
               AND xbsj < sbsj
               AND query_time >= sbsj)
              OR
              (dbrq = query_date
               AND xbsj < sbsj
               AND query_time <= xbsj)
          )
    ) THEN
        current_shift_found := TRUE;
        RETURN QUERY SELECT 
            'SUCCESS: 找到当前班次'::VARCHAR as step_info,
            ''::VARCHAR as collect_point_id,
            ''::VARCHAR as ipt_id,
            ''::VARCHAR as collect_point_text,
            ''::VARCHAR as collect_point_val,
            ''::VARCHAR as shift_class_id,
            ''::VARCHAR as shift_class_name,
            ''::VARCHAR as shift_start_time,
            ''::VARCHAR as shift_end_time,
            ''::VARCHAR as shift_date;
    ELSE
        RETURN QUERY SELECT 
            'ERROR: 未找到当前班次'::VARCHAR as step_info,
            ''::VARCHAR as collect_point_id,
            ''::VARCHAR as ipt_id,
            ''::VARCHAR as collect_point_text,
            ''::VARCHAR as collect_point_val,
            ''::VARCHAR as shift_class_id,
            ''::VARCHAR as shift_class_name,
            ''::VARCHAR as shift_start_time,
            ''::VARCHAR as shift_end_time,
            ''::VARCHAR as shift_date;
        RETURN;
    END IF;

    -- 返回实际的采集点数据（简化版）
    RETURN QUERY 
    WITH current_shift AS (
        SELECT 
            sd.dbrq, sd.sbsj, sd.shiftclassid, sd.objid
        FROM shift_data sd
        WHERE sd.orgcode = parent_orgcode
          AND sd.objid = p_objid
          AND (
              (sd.dbrq = query_date AND query_time BETWEEN sd.sbsj AND sd.xbsj)
              OR
              (sd.dbrq = TO_CHAR(TO_DATE(query_date, 'YYYY-MM-DD') - INTERVAL '1 day', 'YYYY-MM-DD')
               AND sd.xbsj < sd.sbsj
               AND query_time >= sd.sbsj)
              OR
              (sd.dbrq = query_date
               AND sd.xbsj < sd.sbsj
               AND query_time <= sd.xbsj)
          )
        ORDER BY sd.dbrq DESC, sd.sbsj DESC
        LIMIT 1
    ),
    previous_shift AS (
        SELECT 
            sd.shiftclassid, sd.shiftclassname, sd.sbsj, sd.xbsj, sd.dbrq, sd.objid
        FROM shift_data sd, current_shift cs
        WHERE sd.orgcode = parent_orgcode
          AND (
              (sd.dbrq = cs.dbrq AND sd.xbsj = cs.sbsj)
              OR
              (sd.dbrq = TO_CHAR(TO_DATE(cs.dbrq, 'YYYY-MM-DD') - INTERVAL '1 day', 'YYYY-MM-DD')
               AND sd.xbsj = cs.sbsj)
              OR
              (sd.dbrq = cs.dbrq AND sd.sbsj < cs.sbsj)
              OR
              (sd.dbrq = TO_CHAR(TO_DATE(cs.dbrq, 'YYYY-MM-DD') - INTERVAL '1 day', 'YYYY-MM-DD')
               AND NOT EXISTS (
                   SELECT 1 FROM shift_data sd2
                   WHERE sd2.orgcode = parent_orgcode
                     AND sd2.dbrq = cs.dbrq
                     AND sd2.sbsj < cs.sbsj
               ))
          )
        ORDER BY
            CASE WHEN sd.xbsj = cs.sbsj THEN 1 ELSE 2 END,
            sd.dbrq DESC,
            sd.sbsj DESC
        LIMIT 1
    )
    SELECT 
        'DATA'::VARCHAR as step_info,
        mx.collect_point_id::VARCHAR,
        mx.ipt_id::VARCHAR,
        mx.collect_point_text::VARCHAR,
        mx.collect_point_val::VARCHAR,
        ps.shiftclassid::VARCHAR as shift_class_id,
        ps.shiftclassname::VARCHAR as shift_class_name,
        ps.sbsj::VARCHAR as shift_start_time,
        ps.xbsj::VARCHAR as shift_end_time,
        ps.dbrq::VARCHAR as shift_date
    FROM acctobj_inputmx mx, previous_shift ps
    WHERE mx.tmused = 1
      AND EXISTS (
          SELECT ai.id
          FROM acctobj_input ai
          WHERE ai.tmused = 1
            AND ai.bcdm = ps.shiftclassid
            AND ai.sbsj = ps.sbsj
            AND ai.team_id = ps.objid
            AND (p_acctobj_id IS NULL OR LENGTH(TRIM(p_acctobj_id)) = 0 OR ai.acctobj_id = p_acctobj_id)
            AND ai.id = mx.ipt_id
      )
    ORDER BY mx.collect_point_id
    LIMIT 5;

END;
$$ LANGUAGE plpgsql;

-- 执行测试
SELECT * FROM test_get_previous_shift_data(:'p_objid', :'p_datetime', :'p_acctobj_id');

-- 清理测试函数
-- DROP FUNCTION IF EXISTS test_get_previous_shift_data(VARCHAR, VARCHAR, VARCHAR);
